import { mockApi } from "./mockApi";
import {
  LoginRequest,
  LoginResponse,
  User,
  Cha<PERSON>,
  ChatFilters,
  Message,
  PaginatedResponse,
  CreateOrUpdateChatRequest,
  ApiClient,
  SendMessageResponse,
  AIChatCompletionResponse,
} from "../types";

// Get the API URL from environment variables or use a default
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "https://api.example.com";
const USE_MOCK_API =
  import.meta.env.VITE_USE_MOCK_API?.toLowerCase() === "false" ? false : true;

/*
// Log environment variables
console.log('Environment Variables:', {
  VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
  VITE_USE_MOCK_API: import.meta.env.VITE_USE_MOCK_API,
  API_BASE_URL,
  USE_MOCK_API,
});
*/

// Headers to be sent with every request
const getHeaders = (includeAuth = true): HeadersInit => {
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (includeAuth) {
    const token = localStorage.getItem("auth_token");
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }

  return headers;
};

// Generic request function
async function request<T>(
  url: string,
  method: string,
  data?: unknown,
  requiresAuth = true
): Promise<T> {
  const options: RequestInit = {
    method,
    headers: getHeaders(requiresAuth),
  };

  if (data && (method === "POST" || method === "PUT" || method === "PATCH")) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(`${API_BASE_URL}${url}`, options);

  // Handle response
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! Status: ${response.status}`
    );
  }

  // Check if response is empty or not JSON
  const contentType = response.headers.get("content-type");
  if (contentType && contentType.includes("application/json")) {
    return await response.json();
  }

  return {} as T;
}

// Exported API methods
export const apiClient: ApiClient = USE_MOCK_API
  ? mockApi
  : {
      // Auth
      login: async (
        email: string,
        password: string
      ): Promise<LoginResponse> => {
        const data: LoginRequest = { email, password };
        return request<LoginResponse>("/auth/login", "POST", data, false);
      },

      getMe: async (): Promise<User> => {
        return request<User>("/auth/me", "GET");
      },

      // Chats
      getChats: async (
        filters: ChatFilters = {}
      ): Promise<PaginatedResponse<Chat>> => {
        const queryParams = new URLSearchParams();

        if (filters.page) queryParams.append("page", filters.page.toString());
        if (filters.limit)
          queryParams.append("limit", filters.limit.toString());
        if (filters.search) queryParams.append("search", filters.search);
        if (filters.tags && filters.tags.length > 0) {
          filters.tags.forEach((tag) => queryParams.append("tags", tag));
        }
        if (filters.participants && filters.participants.length > 0) {
          filters.participants.forEach((participant) =>
            queryParams.append("participants", participant)
          );
        }
        if (filters.startDate)
          queryParams.append("startDate", filters.startDate);
        if (filters.endDate) queryParams.append("endDate", filters.endDate);

        const query = queryParams.toString()
          ? `?${queryParams.toString()}`
          : "";
        return request<PaginatedResponse<Chat>>(`/chats${query}`, "GET");
      },

      getChat: async (chatId: string): Promise<Chat> => {
        return request<Chat>(`/chats/${chatId}`, "GET");
      },

      createChat: async (data: CreateOrUpdateChatRequest): Promise<Chat> => {
        return request<Chat>("/chats", "POST", data);
      },

      updateChat: async (
        chatId: string,
        data: CreateOrUpdateChatRequest
      ): Promise<Chat> => {
        return request<Chat>(`/chats/${chatId}`, "PATCH", data);
      },

      deleteChat: async (chatId: string): Promise<void> => {
        return request<void>(`/chats/${chatId}`, "DELETE");
      },

      // Messages
      getMessages: async (
        chatId: string,
        page = 1,
        limit = 50
      ): Promise<PaginatedResponse<Message>> => {
        return request<PaginatedResponse<Message>>(
          `/chats/${chatId}/messages?page=${page}&limit=${limit}`,
          "GET"
        );
      },

      getMessagesByIds: async (chatId: string, messageIds: string[]) => {
        const query = messageIds.map((id) => `messageIds=${id}`).join("&");
        return request<{ data: Message[] }>(
          `/chats/${chatId}/messages/by-ids?${query}`,
          "GET"
        );
      },

      sendMessage: async (
        chatId: string,
        textContent: string,
        mentions: string[] = []
      ): Promise<SendMessageResponse> => {
        return request<SendMessageResponse>(
          `/chats/${chatId}/messages`,
          "POST",
          {
            textContent,
            mentions,
          }
        );
      },

      updateMessage: async (
        chatId: string,
        messageId: string,
        content: string,
        mentions: string[] = []
      ): Promise<Message> => {
        return request<Message>(
          `/chats/${chatId}/messages/${messageId}`,
          "PATCH",
          {
            content,
            mentions,
          }
        );
      },

      deleteMessage: async (
        chatId: string,
        messageId: string
      ): Promise<void> => {
        return request<void>(
          `/chats/${chatId}/messages/${messageId}`,
          "DELETE"
        );
      },

      // Users
      getUsers: async (
        page = 1,
        limit = 20,
        search = ""
      ): Promise<PaginatedResponse<User>> => {
        const query = search
          ? `?page=${page}&limit=${limit}&search=${encodeURIComponent(search)}`
          : `?page=${page}&limit=${limit}`;

        return request<PaginatedResponse<User>>(`/users${query}`, "GET");
      },

      getUser: async (userId: string): Promise<User> => {
        return request<User>(`/users/${userId}`, "GET");
      },

      getAIAssistants: async (): Promise<User[]> => {
        return request<User[]>("/users/ai-assistants", "GET");
      },

      getChatCompletion: async (requestData) => {
        //const queryString = new URLSearchParams({...requestData }).toString();
        return request<AIChatCompletionResponse>(
          "/ai/chat-completion/recent",
          "POST",
          requestData
        );
      },
    };
