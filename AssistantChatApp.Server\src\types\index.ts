import { Request } from 'express';

// User types
export interface User {
  id: string;
  email: string;
  displayName: string;
  avatar?: string;
  createdAt: Date;
  isAI: boolean;
}

export interface UserWithPassword extends User {
  password: string;
}

export interface SafeUser extends Omit<User, 'password'> {}

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: SafeUser;
}

export interface JwtPayload {
  userId: string;
  email: string;
}

// Request with authenticated user
export interface AuthRequest extends Request {
  user?: SafeUser;
  userId?: string;
}

// Chat types
export interface Chat {
  id: string;
  title: string;
  description?: string;
  participants: User[];
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
  lastMessage?: Message;
}

export interface ChatFilters {
  page?: number;
  limit?: number;
  search?: string;
  tags?: string[];
  participants?: string[];
  startDate?: string;
  endDate?: string;
}

// Message types
export interface Message {
  id: string;
  chatId: string;
  author: User;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  mentions?: string[];
}

// Pagination types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
}

// API Response type
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}
