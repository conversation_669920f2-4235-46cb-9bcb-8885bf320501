import request from 'supertest';
import app from '../app';
import { db } from '../db';
import { users } from '../db/schema';
import { hashPassword } from '../auth/password';
import { v4 as uuidv4 } from 'uuid';

// Mock user for testing
const testUser = {
  id: uuidv4(),
  email: '<EMAIL>',
  password: 'password123',
  displayName: 'Test User',
  isAI: false,
};

describe('Auth API', () => {
  beforeAll(async () => {
    // Insert test user with hashed password
    const hashedPassword = await hashPassword(testUser.password);
    await db.insert(users).values({
      ...testUser,
      password: hashedPassword,
    });
  });

  afterAll(async () => {
    // Clean up test user
    await db.delete(users).where({ id: testUser.id });
  });

  describe('POST /api/auth/login', () => {
    it('should login with valid credentials', async () => {
      const res = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password,
        });

      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('token');
      expect(res.body).toHaveProperty('user');
      expect(res.body.user.email).toBe(testUser.email);
    });

    it('should return 401 with invalid credentials', async () => {
      const res = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword',
        });

      expect(res.status).toBe(401);
    });
  });

  describe('GET /api/auth/me', () => {
    it('should return user data with valid token', async () => {
      // First login to get token
      const loginRes = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password,
        });

      const token = loginRes.body.token;

      // Then get user data
      const res = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`);

      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('id');
      expect(res.body.email).toBe(testUser.email);
    });

    it('should return 401 with invalid token', async () => {
      const res = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalidtoken');

      expect(res.status).toBe(401);
    });
  });
});
