openapi: 3.0.4
info:
  title: 'KernelMemory.WebService, Version=*******, Culture=neutral, PublicKeyToken=null'
  version: '1.0'
paths:
  /upload:
    post:
      tags:
        - 'KernelMemory.WebService, Version=*******, Culture=neutral, PublicKeyToken=null'
      summary: Upload a new document to the knowledge base
      description: 'Upload a document consisting of one or more files to extract memories from. The extraction process happens asynchronously. If a document with the same ID already exists, it will be overwritten and the memories previously extracted will be updated.'
      operationId: UploadDocument
      requestBody:
        description: Document to upload and extract memories from
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                index:
                  type: string
                  description: Name of the index where to store memories generated by the files.
                documentId:
                  type: string
                  description: Unique ID used for import pipeline and document ID.
                tags:
                  type: array
                  items:
                    type: string
                  description: Tags to apply to the memories extracted from the files.
                  example:
                    - group:abc123
                    - user:xyz
                steps:
                  type: array
                  items:
                    type: string
                  description: 'How to process the files, e.g. how to extract/chunk etc.'
                  example:
                    - extract
                    - partition
                    - gen_embeddings
                    - save_records
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Files to process and extract memories from.
            encoding:
              tags:
                explode: true
              steps:
                explode: true
      responses:
        '200':
          description: OK
          content:
            application/json: { }
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadAccepted'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '503':
          description: Service Unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  /upload-status:
    get:
      tags:
        - 'KernelMemory.WebService, Version=*******, Culture=neutral, PublicKeyToken=null'
      summary: 'Check the status of a file upload in progress. When uploading a document, which can consist of multiple files, each file goes through multiple steps. The status include details about which steps are completed.'
      description: Check the status of a file upload in progress.
      operationId: CheckDocumentStatus
      parameters:
        - name: index
          in: query
          schema:
            type: string
        - name: documentId
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataPipelineStatus'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '413':
          description: Content Too Large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  /ask:
    post:
      tags:
        - 'KernelMemory.WebService, Version=*******, Culture=neutral, PublicKeyToken=null'
      summary: Use the memories extracted from the files uploaded to generate an answer. The query can include filters to use only a subset of the memories available.
      description: Answer a user question using the internal knowledge base.
      operationId: AnswerQuestion
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MemoryQuery'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemoryAnswer'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '503':
          description: Service Unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  /search:
    post:
      tags:
        - 'KernelMemory.WebService, Version=*******, Culture=neutral, PublicKeyToken=null'
      summary: Search the knowledge base for relevant snippets of text. The search can include filters to use only a subset of the knowledge base.
      description: Search the knowledge base for relevant snippets of text.
      operationId: SearchDocumentSnippets
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchQuery'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResult'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  /indexes:
    get:
      tags:
        - 'KernelMemory.WebService, Version=*******, Culture=neutral, PublicKeyToken=null'
      summary: Get the list of containers (aka 'indexes') from the knowledge base. Each index has a unique name. Indexes are collections of memories extracted from the documents uploaded.
      description: Get the list of containers (aka 'indexes') from the knowledge base.
      operationId: ListIndexes
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IndexCollection'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
    delete:
      tags:
        - 'KernelMemory.WebService, Version=*******, Culture=neutral, PublicKeyToken=null'
      summary: Delete a container of documents (aka 'index') from the knowledge base. Indexes are collections of memories extracted from the documents uploaded.
      description: Delete a container of documents (aka 'index') from the knowledge base.
      operationId: DeleteIndexByName
      parameters:
        - name: index
          in: query
          schema:
            type: string
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteAccepted'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  /documents:
    delete:
      tags:
        - 'KernelMemory.WebService, Version=*******, Culture=neutral, PublicKeyToken=null'
      summary: 'Delete a document from the knowledge base. When deleting a document, which can consist of multiple files, all the memories previously extracted are deleted too.'
      description: Delete a document from the knowledge base.
      operationId: DeleteDocumentById
      parameters:
        - name: index
          in: query
          schema:
            type: string
        - name: documentId
          in: query
          required: true
          schema:
            type: string
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteAccepted'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
components:
  schemas:
    Citation:
      type: object
      properties:
        link:
          type: string
          nullable: true
        index:
          type: string
          nullable: true
        documentId:
          type: string
          nullable: true
        fileId:
          type: string
          nullable: true
        sourceContentType:
          type: string
          nullable: true
        sourceName:
          type: string
          nullable: true
        sourceUrl:
          type: string
          nullable: true
        partitions:
          type: array
          items:
            $ref: '#/components/schemas/Partition'
          nullable: true
      additionalProperties: false
    DataPipelineStatus:
      type: object
      properties:
        completed:
          type: boolean
        empty:
          type: boolean
        index:
          type: string
          nullable: true
        document_id:
          type: string
          nullable: true
        tags:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          nullable: true
        creation:
          type: string
          format: date-time
        last_update:
          type: string
          format: date-time
        steps:
          type: array
          items:
            type: string
          nullable: true
        remaining_steps:
          type: array
          items:
            type: string
          nullable: true
        completed_steps:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeleteAccepted:
      type: object
      properties:
        index:
          type: string
          nullable: true
        documentId:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
      additionalProperties: false
    IndexCollection:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/IndexDetails'
          nullable: true
      additionalProperties: false
    IndexDetails:
      type: object
      properties:
        name:
          type: string
          nullable: true
      additionalProperties: false
    MemoryAnswer:
      type: object
      properties:
        streamState:
          $ref: '#/components/schemas/StreamStates'
        question:
          type: string
          nullable: true
        noResult:
          type: boolean
        noResultReason:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        tokenUsage:
          type: array
          items:
            $ref: '#/components/schemas/TokenUsage'
          nullable: true
        relevantSources:
          type: array
          items:
            $ref: '#/components/schemas/Citation'
          nullable: true
      additionalProperties: false
    MemoryQuery:
      type: object
      properties:
        index:
          type: string
          nullable: true
        question:
          type: string
          nullable: true
        filters:
          type: array
          items:
            type: object
            additionalProperties:
              type: array
              items:
                type: string
          nullable: true
        minRelevance:
          type: number
          format: double
        stream:
          type: boolean
        args:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    Partition:
      type: object
      properties:
        text:
          type: string
          nullable: true
        relevance:
          type: number
          format: float
        partitionNumber:
          type: integer
          format: int32
        sectionNumber:
          type: integer
          format: int32
        lastUpdate:
          type: string
          format: date-time
        tags:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          nullable: true
      additionalProperties: false
    ProblemDetails:
      type: object
      properties:
        type:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        status:
          type: integer
          format: int32
          nullable: true
        detail:
          type: string
          nullable: true
        instance:
          type: string
          nullable: true
      additionalProperties: { }
    SearchQuery:
      type: object
      properties:
        index:
          type: string
          nullable: true
        query:
          type: string
          nullable: true
        filters:
          type: array
          items:
            type: object
            additionalProperties:
              type: array
              items:
                type: string
          nullable: true
        minRelevance:
          type: number
          format: double
        limit:
          type: integer
          format: int32
        args:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    SearchResult:
      type: object
      properties:
        query:
          type: string
          nullable: true
        noResult:
          type: boolean
          readOnly: true
        results:
          type: array
          items:
            $ref: '#/components/schemas/Citation'
          nullable: true
      additionalProperties: false
    StreamStates:
      enum:
        - error
        - reset
        - append
        - last
      type: string
    TokenUsage:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
        serviceType:
          type: string
          nullable: true
        modelType:
          type: string
          nullable: true
        modelName:
          type: string
          nullable: true
        tokenizerTokensIn:
          type: integer
          format: int32
          nullable: true
        tokenizerTokensOut:
          type: integer
          format: int32
          nullable: true
        serviceTokensIn:
          type: integer
          format: int32
          nullable: true
        serviceTokensOut:
          type: integer
          format: int32
          nullable: true
        serviceReasoningTokens:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    UploadAccepted:
      type: object
      properties:
        index:
          type: string
          nullable: true
        documentId:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
      additionalProperties: false