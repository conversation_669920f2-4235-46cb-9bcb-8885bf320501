#!meta

{"kernelInfo":{"defaultKernelName":"fsharp","items":[{"name":"csharp","languageName":"C#","aliases":["c#","cs"]},{"name":"fsharp","languageName":"fsharp"},{"name":"html","languageName":"HTML"},{"name":"http","languageName":"HTTP"},{"name":"javascript","languageName":"JavaScript","aliases":["js"]},{"name":"mermaid","languageName":"Mermaid"},{"name":"pwsh","languageName":"PowerShell","aliases":["powershell"]},{"name":"value"}]}}

#!fsharp

#r "nuget: FSharp.Data.LiteralProviders, 1.0.3"
#r "nuget: SQLProvider, 1.5.9"
#r "nuget: SQLProvider.PostgreSql, 1.5.9"

#!fsharp

open System
open System.Linq
open FSharp.Data.Sql
open FSharp.Data.Sql.PostgreSql
open FSharp.Data.LiteralProviders

#!fsharp

module DbConfig =

    let [<Literal>] DbConnectionStringSample = TextFile<"DbConnectionString.txt">.Text
    let [<Literal>] DbVendor = Common.DatabaseProviderTypes.POSTGRESQL
    let [<Literal>] UseOptionTypes = Common.NullableColumnType.OPTION

#!fsharp

type ChatAppDb = SqlDataProvider<
        DbConfig.DbVendor,
        DbConfig.DbConnectionStringSample,
        UseOptionTypes = DbConfig.UseOptionTypes
    >

#!fsharp

let ctx = ChatAppDb.GetDataContext()

#!fsharp

type User = {
    Id: string
    Email: string
    DisplayName: string
    EmailConfirmed: bool
    IsAi: bool
    CreatedAt: DateTime
    Avatar: string
}

#!fsharp

let allUsers = query {
    for u in ctx.Public.AspNetUsers do
        select u
}

#!fsharp

allUsers.ToArray()
