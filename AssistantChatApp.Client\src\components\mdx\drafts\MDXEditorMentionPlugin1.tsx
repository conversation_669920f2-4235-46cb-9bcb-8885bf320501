import React from 'react';
import {
  realmPlugin,
  Cell,
  Signal,
  addImportVisitor$,
  addLexicalNode$,
  addExportVisitor$,
  createActiveEditorSubscription$,
  useCellValue,
  usePublisher,
  activeEditor$,
  MdastImportVisitor,
  LexicalVisitor,
  MdastImportVisitorParams,
} from '@mdxeditor/editor';
import {
  $createTextNode,
  $getSelection,
  $isRangeSelection,
  $isTextNode,
  TextNode,
  LexicalNode,
  NodeKey,
  EditorConfig,
  LexicalEditor,
  $getNodeByKey,
  KEY_DOWN_COMMAND,
  COMMAND_PRIORITY_LOW,
} from 'lexical';
import {
  DecoratorNode,
  DOMConversionMap,
  DOMConversionOutput,
  DOMExportOutput,
  SerializedLexicalNode,
  Spread,
} from 'lexical';
import { ComponentType } from 'react';
import type { MdastNode, Text as MdastText } from 'mdast-util-from-markdown';

// Types
export interface MentionUser {
  id: string;
  displayName: string;
  avatar?: string;
  email?: string;
}

export interface MentionProps {
  user: MentionUser;
  onRemove?: () => void;
  onClick?: (user: MentionUser) => void;
}

export type SerializedMentionNode = Spread<
  {
    userId: string;
    user: MentionUser;
  },
  SerializedLexicalNode
>;

// Custom MDAST node type for mentions
interface MentionMdastNode extends MdastNode {
  type: 'mention';
  userId: string;
  user: MentionUser;
}

// Lexical Node for Mentions
export class MentionNode extends DecoratorNode<React.ReactElement> {
  __userId: string;
  __user: MentionUser;

  static getType(): string {
    return 'mention';
  }

  static clone(node: MentionNode): MentionNode {
    return new MentionNode(node.__userId, node.__user, node.__key);
  }

  constructor(userId: string, user: MentionUser, key?: NodeKey) {
    super(key);
    this.__userId = userId;
    this.__user = user;
  }

  getUserId(): string {
    return this.__userId;
  }

  getUser(): MentionUser {
    return this.__user;
  }

  createDOM(config: EditorConfig): HTMLElement {
    const span = document.createElement('span');
    span.className = 'mention-node';
    span.setAttribute('data-user-id', this.__userId);
    span.setAttribute('data-lexical-mention', 'true');
    return span;
  }

  updateDOM(): false {
    return false;
  }

  static importDOM(): DOMConversionMap | null {
    return {
      span: (domNode: HTMLElement) => {
        if (domNode.hasAttribute('data-lexical-mention')) {
          return {
            conversion: (element: HTMLElement): DOMConversionOutput | null => {
              const userId = element.getAttribute('data-user-id');
              if (userId) {
                // Note: In a real implementation, you'd need to resolve the user data
                // This is a simplified version
                const user: MentionUser = {
                  id: userId,
                  displayName: element.textContent?.replace('@', '') || `User ${userId}`,
                };
                const node = $createMentionNode(userId, user);
                return { node };
              }
              return null;
            },
            priority: 1,
          };
        }
        return null;
      },
    };
  }

  exportDOM(): DOMExportOutput {
    const element = document.createElement('span');
    element.setAttribute('data-user-id', this.__userId);
    element.setAttribute('data-lexical-mention', 'true');
    element.textContent = `@${this.__user.displayName}`;
    return { element };
  }

  static importJSON(serializedNode: SerializedMentionNode): MentionNode {
    const { userId, user } = serializedNode;
    const node = $createMentionNode(userId, user);
    return node;
  }

  exportJSON(): SerializedMentionNode {
    return {
      userId: this.__userId,
      user: this.__user,
      type: 'mention',
      version: 1,
    };
  }

  getTextContent(): string {
    return `@${this.__user.displayName}`;
  }

  decorate(editor: LexicalEditor, config: EditorConfig): React.ReactElement {
    return (
      <MentionComponent
        key={this.__key}
        nodeKey={this.__key}
        userId={this.__userId}
        user={this.__user}
        editor={editor}
      />
    );
  }

  isInline(): boolean {
    return true;
  }
}

// Helper function to create mention nodes
export function $createMentionNode(userId: string, user: MentionUser): MentionNode {
  return new MentionNode(userId, user);
}

export function $isMentionNode(node: LexicalNode | null | undefined): node is MentionNode {
  return node instanceof MentionNode;
}

// Cell values for plugin configuration
const mentionUsers$ = Cell<MentionUser[]>([]);
const mentionComponentFactory$ = Cell<ComponentType<MentionProps> | null>(null);
const onMentionClick$ = Cell<((user: MentionUser) => void) | null>(null);
const onMentionAdd$ = Cell<((user: MentionUser) => void) | null>(null);
const onMentionRemove$ = Cell<((userId: string) => void) | null>(null);

// Internal component that wraps the custom mention component
interface MentionComponentProps {
  nodeKey: NodeKey;
  userId: string;
  user: MentionUser;
  editor: LexicalEditor;
}

const MentionComponent: React.FC<MentionComponentProps> = ({
  nodeKey,
  userId,
  user,
  editor,
}) => {
  const mentionComponentFactory = useCellValue(mentionComponentFactory$);
  const onMentionClick = useCellValue(onMentionClick$);
  const onMentionRemove = useCellValue(onMentionRemove$);

  const handleRemove = () => {
    editor.update(() => {
      const node = $getNodeByKey(nodeKey);
      if (node) {
        node.remove();
        if (onMentionRemove) {
          onMentionRemove(userId);
        }
      }
    });
  };

  const handleClick = () => {
    if (onMentionClick) {
      onMentionClick(user);
    }
  };

  const MentionComponentToRender = mentionComponentFactory || DefaultMentionComponent;

  return (
    <MentionComponentToRender
      user={user}
      onRemove={handleRemove}
      onClick={handleClick}
    />
  );
};

// Default mention component if none provided
const DefaultMentionComponent: React.FC<MentionProps> = ({ user, onRemove, onClick }) => (
  <span
    style={{
      backgroundColor: '#e3f2fd',
      color: '#1976d2',
      padding: '2px 6px',
      borderRadius: '12px',
      fontSize: '14px',
      cursor: 'pointer',
      display: 'inline-flex',
      alignItems: 'center',
      gap: '4px',
      margin: '0 2px',
      border: '1px solid #bbdefb',
    }}
    onClick={onClick}
    title={user.email || user.displayName}
  >
    @{user.displayName}
    {onRemove && (
      <span
        style={{
          marginLeft: '4px',
          cursor: 'pointer',
          fontSize: '12px',
          opacity: 0.7,
          fontWeight: 'bold',
        }}
        onClick={(e) => {
          e.stopPropagation();
          onRemove();
        }}
        title="Remove mention"
      >
        ×
      </span>
    )}
  </span>
);

// Transform function to convert @userId patterns to mention nodes
const transformMentions = (editor: LexicalEditor, users: MentionUser[]) => {
  const userMap = new Map(users.map(user => [user.id, user]));

  editor.update(() => {
    const selection = $getSelection();
    if (!$isRangeSelection(selection)) return;

    const nodes = selection.getNodes();

    nodes.forEach(node => {
      if ($isTextNode(node)) {
        const textContent = node.getTextContent();
        const mentionRegex = /@([a-zA-Z0-9\-_]+)/g;
        let match;
        const replacements: Array<{ start: number; end: number; userId: string; user: MentionUser }> = [];

        while ((match = mentionRegex.exec(textContent)) !== null) {
          const userId = match[1];
          const user = userMap.get(userId);

          if (user) {
            replacements.push({
              start: match.index,
              end: match.index + match[0].length,
              userId,
              user,
            });
          }
        }

        // Process replacements in reverse order to maintain correct indices
        replacements.reverse().forEach(({ start, end, userId, user }) => {
          const beforeText = textContent.substring(0, start);
          const afterText = textContent.substring(end);

          // Create new nodes
          const mentionNode = $createMentionNode(userId, user);

          if (beforeText) {
            const beforeNode = $createTextNode(beforeText);
            node.insertBefore(beforeNode);
          }

          node.insertBefore(mentionNode);

          if (afterText) {
            const afterNode = $createTextNode(afterText);
            node.insertAfter(afterNode);
          }

          node.remove();
        });
      }
    });
  });
};

// Export visitor for markdown conversion
const exportVisitor: LexicalVisitor<MentionNode, MentionMdastNode> = {
  testLexicalNode: $isMentionNode,
  visitLexicalNode: ({ lexicalNode }) => {
    return {
      type: 'mention',
      userId: lexicalNode.getUserId(),
      user: lexicalNode.getUser(),
    } as MentionMdastNode;
  },
};

// Import visitor for markdown conversion
const importVisitor: MdastImportVisitor<MentionMdastNode> = {
  testNode: (node: MdastNode): node is MentionMdastNode => {
    return node.type === 'mention' &&
           'userId' in node &&
           'user' in node &&
           typeof (node as MentionMdastNode).userId === 'string';
  },
  visitNode: ({ mdastNode }: MdastImportVisitorParams<MentionMdastNode>) => {
    return $createMentionNode(mdastNode.userId, mdastNode.user);
  },
};

// Text import visitor to handle @userId patterns in markdown
const textImportVisitor: MdastImportVisitor<MdastText> = {
  testNode: (node: MdastNode): node is MdastText => {
    return node.type === 'text' && 'value' in node && typeof (node as MdastText).value === 'string';
  },
  visitNode: ({ mdastNode }: MdastImportVisitorParams<MdastText>) => {
    const text = mdastNode.value;
    const mentionRegex = /@([a-zA-Z0-9\-_]+)/g;
    const nodes: LexicalNode[] = [];
    let lastIndex = 0;
    let match;

    // Get users from the current context
    const users = mentionUsers$.getValue();
    const userMap = new Map(users.map(user => [user.id, user]));

    while ((match = mentionRegex.exec(text)) !== null) {
      const userId = match[1];
      const user = userMap.get(userId);

      if (user) {
        // Add text before mention
        if (match.index > lastIndex) {
          const beforeText = text.substring(lastIndex, match.index);
          if (beforeText) {
            nodes.push($createTextNode(beforeText));
          }
        }

        // Add mention node
        nodes.push($createMentionNode(userId, user));
        lastIndex = match.index + match[0].length;
      }
    }

    // Add remaining text
    if (lastIndex < text.length) {
      const remainingText = text.substring(lastIndex);
      if (remainingText) {
        nodes.push($createTextNode(remainingText));
      }
    }

    // If we found mentions, return the array of nodes, otherwise return the original text node
    return nodes.length > 1 ? nodes : $createTextNode(text);
  },
};

// Main plugin configuration
export interface MentionsPluginParams {
  users: MentionUser[];
  mentionComponent?: ComponentType<MentionProps>;
  onMentionClick?: (user: MentionUser) => void;
  onMentionAdd?: (user: MentionUser) => void;
  onMentionRemove?: (userId: string) => void;
  autoTransform?: boolean; // Whether to auto-transform @userId patterns
}

export const mentionsPlugin = realmPlugin<MentionsPluginParams>({
  init: (realm) => {
    // Register the lexical node
    realm.pubIn({
      [addLexicalNode$]: MentionNode,
    });

    // Register import/export visitors with correct format
    realm.pub(addImportVisitor$, importVisitor);
    realm.pub(addImportVisitor$, textImportVisitor);
    realm.pub(addExportVisitor$, exportVisitor);
  },

  update: (realm, params) => {
    const {
      users = [],
      mentionComponent,
      onMentionClick,
      onMentionAdd,
      onMentionRemove,
      autoTransform = true,
    } = params || {};

    // Update configuration cells
    realm.pub(mentionUsers$, users);
    realm.pub(mentionComponentFactory$, mentionComponent || null);
    realm.pub(onMentionClick$, onMentionClick || null);
    realm.pub(onMentionAdd$, onMentionAdd || null);
    realm.pub(onMentionRemove$, onMentionRemove || null);

    // Setup auto-transform functionality
    if (autoTransform && users.length > 0) {
      realm.pub(createActiveEditorSubscription$, (editor) => {
        let timeoutId: NodeJS.Timeout;

        return editor.registerTextContentListener((textContent) => {
          clearTimeout(timeoutId);
          // Debounce the transformation to avoid excessive processing
          timeoutId = setTimeout(() => {
            transformMentions(editor, users);
          }, 500);
        });
      });
    }
  },
});

// Utility functions for programmatic mention insertion
export const insertMention = (editor: LexicalEditor, user: MentionUser) => {
  editor.update(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const mentionNode = $createMentionNode(user.id, user);
      selection.insertNodes([mentionNode]);
    }
  });
};

// Hook to use mention functionality
export const useMentions = () => {
  const editor = useCellValue(activeEditor$);
  const users = useCellValue(mentionUsers$);
  const onMentionAdd = useCellValue(onMentionAdd$);

  const addMention = (user: MentionUser) => {
    if (editor) {
      insertMention(editor, user);
      if (onMentionAdd) {
        onMentionAdd(user);
      }
    }
  };

  const getMentions = (): MentionUser[] => {
    if (!editor) return [];

    const mentions: MentionUser[] = [];
    editor.getEditorState().read(() => {
      const root = editor.getEditorState()._nodeMap;
      root.forEach((node) => {
        if ($isMentionNode(node)) {
          mentions.push(node.getUser());
        }
      });
    });

    return mentions;
  };

  const getAllMentionedUserIds = (): string[] => {
    return getMentions().map(user => user.id);
  };

  return {
    addMention,
    getMentions,
    getAllMentionedUserIds,
    users,
  };
};
