# Web API Specification

## Base URL
- Development: http://localhost:5000
- Production: https://api.example.com

## Authentication
- Type: Bearer Token JWT
- Header: `Authorization: Bearer {token}`
- All endpoints except /auth/login require authentication

## Endpoints

### Authentication

#### POST /auth/login
Request:
```json
{
  "email": "string",
  "password": "string"
}
```
Response (200 OK):
```json
{
  "token": "string",
  "user": {
    "id": "string",
    "displayName": "string",
    "avatar": "string?",
    "createdAt": "string (ISO-8601)",
    "isAI": "boolean"
  }
}
```

#### GET /auth/me
Response (200 OK):
```json
{
  "id": "string",
  "displayName": "string",
  "avatar": "string?",
  "createdAt": "string (ISO-8601)",
  "isAI": "boolean"
}
```

### Chats

#### GET /chats
Query Parameters:
- page (optional, default: 1)
- limit (optional, default: 20)
- search (optional)
- tags[] (optional, multiple)
- participants[] (optional, multiple)
- startDate (optional, ISO-8601)
- endDate (optional, ISO-8601)

Response (200 OK):
```json
{
  "data": [{
    "id": "string",
    "title": "string",
    "description": "string?",
    "participants": [{
      "id": "string",
      "displayName": "string",
      "avatar": "string?",
      "createdAt": "string",
      "isAI": "boolean"
    }],
    "createdAt": "string (ISO-8601)",
    "updatedAt": "string (ISO-8601)",
    "tags": "string[]?",
    "lastMessage": {
      "id": "string",
      "chatId": "string",
      "author": {
        "id": "string",
        "displayName": "string",
        "avatar": "string?",
        "createdAt": "string",
        "isAI": "boolean"
      },
      "content": "string",
      "createdAt": "string",
      "updatedAt": "string",
      "mentions": "string[]?"
    }?
  }],
  "total": "number",
  "page": "number",
  "limit": "number"
}
```

#### GET /chats/{chatId}
Response (200 OK): Single chat object (same as in array above)

#### POST /chats
Request:
```json
{
  "title": "string",
  "description": "string?",
  "participants": "string[]",
  "tags": "string[]?"
}
```
Response (201 Created): Created chat object

#### PATCH /chats/{chatId}
Request: Any chat fields to update
Response (200 OK): Updated chat object

#### DELETE /chats/{chatId}
Response (204 No Content)

### Messages

#### GET /chats/{chatId}/messages

##### Get page of messages

Query Parameters:
- page (optional, default: 1)
- limit (optional, default: 50)

Response (200 OK):
```json
{
  "data": [{
    "id": "string",
    "chatId": "string",
    "author": {
      "id": "string",
      "displayName": "string",
      "avatar": "string?",
      "createdAt": "string",
      "isAI": "boolean"
    },
    "content": "string",
    "createdAt": "string (ISO-8601)",
    "updatedAt": "string (ISO-8601)",
    "mentions": "string[]?"
  }],
  "total": "number",
  "page": "number",
  "limit": "number"
}
```

##### Get messages by IDs

Query Parameters:
- messageIds (required, multiple)

Response (200 OK):
```json
[
  {
    "id": "string",
    "chatId": "string",
    "author": {
      "id": "string",
      "displayName": "string",
      "avatar": "string?",
      "createdAt": "string",
      "isAI": "boolean"
    },
    "content": "string",
    "createdAt": "string (ISO-8601)",
    "updatedAt": "string (ISO-8601)",
    "mentions": "string[]?"
  }
]
```


#### POST /chats/{chatId}/messages
Request:
```json
{
  "content": "string",
  "mentions": "string[]?"
}
```
Response (201 Created): Created message object

#### PATCH /chats/{chatId}/messages/{messageId}
Request:
```json
{
  "content": "string",
  "mentions": "string[]?"
}
```
Response (200 OK): Updated message object

#### DELETE /chats/{chatId}/messages/{messageId}
Response (204 No Content)

### Users

#### GET /users
Query Parameters:
- page (optional, default: 1)
- limit (optional, default: 20)
- search (optional)

Response (200 OK):
```json
{
  "data": [{
    "id": "string",
    "displayName": "string",
    "avatar": "string?",
    "createdAt": "string (ISO-8601)",
    "isAI": "boolean"
  }],
  "total": "number",
  "page": "number",
  "limit": "number"
}
```

#### GET /users/{userId}
Response (200 OK): Single user object

#### GET /users/ai-assistants
Response (200 OK): Array of AI user objects

## Error Responses

All endpoints may return these error responses:

### 400 Bad Request
```json
{
  "message": "string",
  "errors": "object?"
}
```

### 401 Unauthorized
```json
{
  "message": "Authentication required"
}
```

### 403 Forbidden
```json
{
  "message": "Access denied"
}
```

### 404 Not Found
```json
{
  "message": "Resource not found"
}
```

### 500 Internal Server Error
```json
{
  "message": "Internal server error"
}
```
