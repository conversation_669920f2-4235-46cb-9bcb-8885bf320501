using System;
using System.Collections.Generic;

namespace AssistantChatApp.Server.AspNetCore.DTOs
{
    public class MessageNonTextContents
    {
        public ICollection<FunctionCallContentDto>? FunctionCalls { get; set; }
        public ICollection<FunctionResultContentDto>? FunctionResults { get; set; }
    }

    public class MessageDto
    {
        public string Id { get; set; } = string.Empty;
        public string ChatId { get; set; } = string.Empty;
        public UserDto Author { get; set; } = null!;
        public string Content { get; set; } = string.Empty;
        public MessageNonTextContents? OtherContent { get; set; }
        public string CreatedAt { get; set; } = string.Empty;
        public string UpdatedAt { get; set; } = string.Empty;
        public List<string>? Mentions { get; set; }
        public List<TagDto>? Tags { get; set; }
    }
}
