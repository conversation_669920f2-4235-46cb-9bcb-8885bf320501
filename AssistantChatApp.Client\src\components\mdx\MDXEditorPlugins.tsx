import React from "react";
import {
    createCommand,
    COMMAND_PRIORITY_HIGH,
    type LexicalCommand,
} from "lexical";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
    realmPlugin,
} from "@mdxeditor/editor";

const CTRL_ENTER_COMMAND: LexicalCommand<KeyboardEvent> = createCommand();

export const useCtrlEnterKeyHandlerPlugin = () => {
    // Get the Lexical editor from the realm.
    const [editor] = useLexicalComposerContext();
    // Cleanup function that will unregister the command.
    const [unregister, setUnregister] = React.useState<() => void>();

    const createPlugin = React.useMemo(
        () =>
            realmPlugin<{ onCtrlEnterKeyDown: (e: KeyboardEvent) => void }>({
                init: (realm, params) => {
                    // Register a command to handle key down events.
                    const unregister = editor?.registerCommand(
                        CTRL_ENTER_COMMAND,
                        (event: KeyboardEvent) => {
                            // Check for Ctrl (or Cmd) + Enter combination.
                            if (
                                (event.ctrlKey || event.metaKey) &&
                                event.key === "Enter"
                            ) {
                                // Execute your custom handling here
                                // console.log('Ctrl+Enter was pressed!')
                                params?.onCtrlEnterKeyDown(event);
                                // You can also use realm.pub or other realm interactions to integrate with your app.
                                return true; // Prevent further propagation
                            }
                            return false;
                        },
                        COMMAND_PRIORITY_HIGH,
                    );
                    setUnregister(unregister);
                },
            }),
        [editor],
    );

    return React.useMemo(
        () => ({
            createPlugin,
            unregister,
        }),
        [createPlugin, unregister],
    );
};
