/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react";

const isParagraphElement = (
  node: React.ReactNode
): node is React.ReactElement<HTMLParagraphElement> => {
  return React.isValidElement(node) && node.type === "p";
};

export const tryGetNodeChildren = (
  node: React.ReactNode
): React.ReactNode | undefined => {
  // Type guard: check if it's a ReactElement
  if (!React.isValidElement<React.PropsWithChildren>(node)) {
    return undefined;
  }
  return node.props.children ?? undefined;
};

export const tryGetNodeStringChild = (node: React.ReactNode) => {
  const children = tryGetNodeChildren(node);
  if (children && typeof children === "string") {
    return children;
  }
  return undefined;
};

export const tryExtractTextFromRootOrFirstChild = (node: React.ReactNode) => {
  if (typeof node === "string") {
    return node;
  }
  return tryGetNodeStringChild(node);
};

export const tryExtractFirstParagraphText = (
  node: React.ReactNode
): string | undefined => {
  if (!isParagraphElement(node)) {
    return undefined;
  }

  const { children } = node.props;

  // Handle different types of children
  if (typeof children === "string") {
    return children;
  }

  // If children is an array, try to extract text from the first string element
  if (Array.isArray(children)) {
    const firstStringChild = children.find(
      (child) => typeof child === "string"
    );
    return firstStringChild as string | undefined;
  }

  return undefined;
};

export const tryParseJsonObject = (text: string) => {
  try {
    const value = JSON.parse(text) as unknown;
    if (typeof value === "object") {
      return value;
    }
    return undefined;
  } catch (error) {
    return undefined;
  }
};

export const tryParseJsonObjectFromFirstParagraphText = (
  node: React.ReactNode
) => {
  const text = tryExtractFirstParagraphText(node);
  if (!text) {
    return undefined;
  }

  return tryParseJsonObject(text);
};
