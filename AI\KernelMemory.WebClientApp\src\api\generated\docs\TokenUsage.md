# TokenUsage


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**timestamp** | **string** |  | [optional] [default to undefined]
**serviceType** | **string** |  | [optional] [default to undefined]
**modelType** | **string** |  | [optional] [default to undefined]
**modelName** | **string** |  | [optional] [default to undefined]
**tokenizerTokensIn** | **number** |  | [optional] [default to undefined]
**tokenizerTokensOut** | **number** |  | [optional] [default to undefined]
**serviceTokensIn** | **number** |  | [optional] [default to undefined]
**serviceTokensOut** | **number** |  | [optional] [default to undefined]
**serviceReasoningTokens** | **number** |  | [optional] [default to undefined]

## Example

```typescript
import { TokenUsage } from './api';

const instance: TokenUsage = {
    timestamp,
    serviceType,
    modelType,
    modelName,
    tokenizerTokensIn,
    tokenizerTokensOut,
    serviceTokensIn,
    serviceTokensOut,
    serviceReasoningTokens,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
