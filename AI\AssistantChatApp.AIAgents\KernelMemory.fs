module AssistantChatApp.AIAgents.KernelMemory

open System
open System.Collections.Generic
open System.Text.Json
open System.Text.Encodings.Web
open System.Net.Http
open System.Runtime.InteropServices
open System.Reflection
open System.ComponentModel
open Microsoft.SemanticKernel
open Microsoft.KernelMemory
open Microsoft.Extensions.Logging
open Microsoft.Extensions.DependencyInjection
open Microsoft.Extensions.Configuration
open AssistantChatApp.Shared

//let createMemory (config: IConfiguration) =
//    KernelMemoryBuilder()
//        .ConfigureDependencies(config)
//        .Build<MemoryServerless>()

//let createMemoryPlugin (config: IConfiguration) =
//    let memory = createMemory config
//    MemoryPlugin(memory)


let getFromDI (sp: IServiceProvider) =
    sp.GetRequiredService<IKernelMemory>()

let configureKernelMemoryWebClient (services: IServiceCollection) =
    services.AddSingleton<IKernelMemory>(fun sp ->
        let kmSettings =
            Configuration.getKernelMemoryAccessSettingsFromOptions sp
        let endpoint =
            kmSettings
            |> Option.bind (fun s -> s.KernelMemoryServiceUrl |> Option.ofObj)
            |> Option.defaultWith (fun () ->
                let config = sp.GetRequiredService<IConfiguration>()
                let key = $"services:{ServiceNames.KernelMemoryService}:http:0"
                config[key]
            )
        MemoryWebClient(endpoint) :> IKernelMemory
    )

let importAsSearchPlugin
    (loggerFactory: ILoggerFactory)
    (memoryPlugin: MemoryPlugin)
    (kernel: Kernel)
    =
    let inline kfun (methodName: string) =
        KernelFunctionFactory.CreateFromMethod(
            typeof<MemoryPlugin>.GetMethod(methodName),
            target = memoryPlugin,
            loggerFactory = loggerFactory
        )
    let kernelFunctions = [
        kfun (nameof memoryPlugin.SearchAsync)
    ]
    kernel.ImportPluginFromFunctions(
        "memory", kernelFunctions
    )

module MemoryFunctionNames =
    let [<Literal>] Search = "search"

type ReadonlyMemoryPlugin(
    memory: IKernelMemory,
    settings: KernelMemoryPluginSettings
) =

    let serializationOptions =
        let o = JsonSerializerOptions(JsonSerializerDefaults.Web)
        o.Encoder <- JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        o

    static member PluginName = "memory"

    member this.AddToKernel(builder: IKernelBuilder) =
        builder.Plugins.AddFromObject(this, ReadonlyMemoryPlugin.PluginName)

    [<KernelFunction(MemoryFunctionNames.Search)>]
    [<Description "Search for memory text fragments semantically similar to the input text">]
    member _.SearchAsync(
        [<Description "Input text to search in memory">]
        query: string,
        [<Optional; DefaultParameterValue(null: string | null)>]
        [<Description "Name of memory index container to search for information">]
        index: string | null,
        [<Optional; DefaultParameterValue(null: IReadOnlyDictionary<string, string> | null)>]
        [<Description "Key-value tags to filter memory entries by.">]
        tags: IReadOnlyDictionary<string, string> | null
        //[<Optional; DefaultParameterValue(1)>]
        //[<Description "Maximum number of memories to return">]
        //limit: int
    ) =
        task {
            let { Limit = limit; MinRelevance = minRelevance } = settings.SearchSettings
            let filter =
                match tags with
                | null -> None
                | tags ->
                    let f = MemoryFilter()
                    for tag in tags do f.Add(tag.Key, tag.Value)
                    Some f
            let! searchResult =
                memory.SearchAsync(
                    query,
                    index = index,
                    ?filter = filter,
                    ?limit = Option.ofNullable limit,
                    ?minRelevance = Option.ofNullable minRelevance
                )
            if searchResult.NoResult || searchResult.Results.Count = 0 then
                return ""
            else
                let results = seq {
                    for doc in searchResult.Results do
                        for chunk in doc.Partitions do
                            yield chunk.Text
                }
                return JsonSerializer.Serialize(results, serializationOptions)
        }
