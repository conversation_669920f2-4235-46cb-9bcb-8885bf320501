using AssistantChatApp.Shared;

var builder = DistributedApplication.CreateBuilder(args);

var kernelMemoryService =
    builder.AddProject<Projects.KernelMemory_WebService>(ServiceNames.KernelMemoryService)
        .WithExternalHttpEndpoints();

var agentService =
    builder.AddProject<Projects.AssistantChatApp_AIAgents_ConsoleApp>(ServiceNames.AgentService)
        .WithReference(kernelMemoryService)
        .WaitFor(kernelMemoryService);

var chatAppBackend =
    builder.AddProject<Projects.AssistantChatApp_Server_AspNetCore>(ServiceNames.ChatAppBackend)
        .WithReference(agentService)
        .WaitFor(agentService)
        .WithExternalHttpEndpoints();

//var chatAppFrontendProject = new Projects.AssistantChatApp_Client();
var chatAppFrontendDirPath = //Path.GetDirectoryName(chatAppFrontendProject.ProjectPath)!;
    Path.Combine(builder.Environment.ContentRootPath, "../AssistantChatApp.Client");

var chatAppFrontend =
    // builder.AddViteApp("chat-app-frontend",
    //     packageManager: "pnpm",
    //     workingDirectory: chatAppFrontendDirPath
    // )
    //     .WaitFor(chatAppBackend)
    //     .WithReference(chatAppBackend)
    //     .WithExternalHttpEndpoints();
    builder.AddPnpmApp(ServiceNames.ChatAppFrontend,
        workingDirectory: chatAppFrontendDirPath,
        scriptName: "dev"
    )
        .WaitFor(chatAppBackend)
        .WithReference(chatAppBackend)
        .WithEnvironment("VITE_SERVER_URL", chatAppBackend.GetEndpoint("http"))
        .WithHttpEndpoint(env: "VITE_PORT")
        .WithExternalHttpEndpoints();

builder.Build().Run();
