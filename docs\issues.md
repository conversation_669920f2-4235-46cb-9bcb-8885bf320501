# Frontend

## Issues

1. ~~Mention rendering in completed message is not working (outputs as raw XML tag, e.g.  `<mention userId="9bec9a27-3dbc-4373-966a-6503819efd46" />`)~~
2. ~~In Chat List View, not all participants are shown (related to Server issue #1).~~
3. ~~In MDXEditor, User summary popup (triggered by clicking on a user mention) has low Z-index which causes it to be hidden behind the editor. Probable fix: make popup's Z-index higher.~~
4. ~~In the API implementation, `createChat` method expects `Chat` object (particularly, with `participants: User[]`) as an argument, while the Server-side implementation expects an object with `participants: string[]`. I.e., Client submits a complete `Chat` object, while Server expects a refined objects with User IDs instead of full User objects as participants value.~~
5. In `MessageComposer`, custom toolbar for message editing doesn't define any logic (it contains buttons with icons but without handlers).
6. (`MessageItem`) Message editing is not working.
7. ~~Chat editing and deleting is not working.~~
8. ~~**[Message Item]** Implement Message's `otherContent` rendering.~~
9. Fix Message overflow rendering (now it's hidden when the message is too lon./g).
10. **[Message Item]** Message's menu button is hard to find (it's not visible unless hovering over it, thus it's difficult to identify its position). Also, it's position now depends on the length of the message, so it varies from message to message, which complicates identifying the position.
11. ~~**[Chat message list]** Chat messages are listed in the descending order (from the newest to the oldest) instead of ascending (from the oldest to the latest), with the ascending order being more natural, intuitive in chat apps.~~
12. **[Root UI]** Add color theme switcher (light/dark/system).
13. (BUG) (`LoginPage.tsx:17:32`) A component is changing a controlled input to be uncontrolled.
14. (FLAW) (`NewChatButton`) Only the first page of users is fetched. It's okay for now while the total list of users is small, but in general the fetching should be fixed to be able to fetch all the users (maybe in a delayed manner, utilizing `Autocomplete`'s features).
15. (IMPROVEMENT) (`MessageComposer`) Show list of mentions somewhere near the message composer. Probably, allow to remove mentions from the message (however, this will provoke discrepancy between the mentions in the message text and the mentions list, so it's not clear how to handle this case).
16. (BUG) (`MDXEditorMentionPlugin.tsx`) Mention plugin implementation is not working correctly: it do renders the 1st mention, but after that stops recognize any Markdown text.
17. (IDEA) (`MessageComposer`) Instead of implementing mentions via `@` prefix and custom MDX Plugin, what about implementing mentions via Markdown *inline directives* feature?

---

# ASP.NET Core Backend

## Issues & Improvements

1. For Messages, support Tags and Contents update.
2. When creating messages, support parsing and recognizing mentions from message text.
3. `GetUsers` returns all the users (including AI assistants). Probably, a method to return only human users should be introduced. Or `GetUsers` method should accept an option filter out non-human users.

---

# Node.js Backend

## Issues

1. `GET /chats` returns chat list in which chats seem not returning all the participants but only the current user.

## Improvements

1. Introduce new `UserRole` entity. Then, in `User` entity, replace `isAI` bool flag with a reference to a new separate `UserRole` entity. This way, `isAI` flag can be emulated on the data level (by adding a `UserRole` data entry that represents AI role) rather than on the schema level.
