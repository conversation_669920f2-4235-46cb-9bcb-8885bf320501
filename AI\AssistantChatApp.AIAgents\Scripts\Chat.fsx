//#r "nuget: ManagedCode.Together.SemanticKernel, 0.0.3"
//#r "nuget: Microsoft.Extensions.AI.Abstractions"
#r "nuget: FSharp.Control.TaskSeq, 0.4.0"
#r "nuget: FSharp.Control.AsyncSeq, 3.2.1"
#r "nuget: Microsoft.SemanticKernel, 1.54"

// #r @"S:\Prog\AI\together-dotnet\Together\bin\Debug\net9.0\Together.dll"
// #r @"S:\Prog\AI\together-dotnet\Together.SemanticKernel\bin\Debug\net9.0\Together.SemanticKernel.dll"

#load "Serialization.fsx"

open System
open System.Net.Http
open System.Threading
open System.Threading.Tasks
open FSharp.Linq
open FSharp.Control
open Microsoft.Extensions.DependencyInjection
open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.ChatCompletion
//open AssistantChatApp.AIAgents
// open Together.SemanticKernel.Extensions
// open Together.Models.ChatCompletions


module ChatMessageContent =

    let toPrettyString (m: ChatMessageContent) =
        let author =
            match m with
            | m when not <| String.IsNullOrEmpty m.AuthorName -> m.AuthorName
            | m when
                m.Role = AuthorRole.Assistant &&
                not <| String.IsNullOrEmpty m.ModelId
                ->
                m.ModelId
            | m -> m.Role.Label
        let sb = System.Text.StringBuilder()
        sb
            .AppendLine($"[{author}]:")
            .AppendLine($"\t{m.Content}")
            .ToString()


module ChatHistory =

    let toStringList (history: ChatHistory) =
        [for m in history -> ChatMessageContent.toPrettyString m]

    let print (history: ChatHistory) =
        history
        |> Seq.iter (ChatMessageContent.toPrettyString >> printfn "%s")


type IChat =
    abstract AddUserMessage: message: string -> unit
    abstract AddAssistantMessage: modelId: string -> message: string -> unit
    abstract GetCompletionStreamAsync: unit -> TaskSeq<StreamingChatMessageContent>
    abstract GetCompletionMessageAsync: unit -> Task<ChatMessageContent>


let sendUserMessageAndPrintStreamedResults
    (chat: IChat)
    (userMessage: string)
    =
    task {
        do chat.AddUserMessage userMessage
        printfn "USER:"
        printfn $"\t{userMessage}"
        printfn "---[END USER MESSAGE]---"
        printfn "ASSISTANT:"
        printf "\t"
        let sb = System.Text.StringBuilder()
        let rspStream = chat.GetCompletionStreamAsync()
        let mutable modelId = ""
        do!
            rspStream
            |> TaskSeq.iteriAsync (fun i part -> task {
                if i = 0 then
                    modelId <- part.ModelId
                sb.Append(part) |> ignore
                printf $"{part.Content}"
            })
        printfn ""
        printfn "---[END ASSISTANT MESSAGE]---"
        let aiMessageText = sb.ToString()
        do chat.AddAssistantMessage modelId aiMessageText
    }
    |> Async.AwaitTask
    |> Async.RunSynchronously

let sendUserMessageAndPrintResults
    (chat: IChat)
    (userMessage: string)
    =
    task {
        do chat.AddUserMessage userMessage
        printfn "USER:"
        printfn $"\t{userMessage}"
        printfn "---[END USER MESSAGE]---"
        printfn "ASSISTANT:"
        printf "\t"
        let! rsp = chat.GetCompletionMessageAsync()
        let modelId = rsp.ModelId
        let aiMessage = rsp.Content
        printfn "%s" aiMessage
        printfn "---[END ASSISTANT MESSAGE]---"
        do chat.AddAssistantMessage modelId aiMessage
    }
    |> Async.AwaitTask
    |> Async.RunSynchronously


let sendUserMessageAsyncSeqBase
    (history: ChatHistory)
    (getCompletionAsync: ChatHistory -> TaskSeq<StreamingChatMessageContent>)
    (text: string)
    =
    do history.AddUserMessage(text)
    printfn $"USER: {text}"
    printf $"AI: "
    let rspSeq =
        getCompletionAsync history
        |> AsyncSeq.ofAsyncEnum
    rspSeq
    |> AsyncSeq.iterAsync (fun x -> async { printf "%s" x.Content })
    |> Async.RunSynchronously
    let aiMessage =
        ("", rspSeq)
        ||> AsyncSeq.fold (fun res x -> res + x.Content)
        |> Async.RunSynchronously
    do history.AddAssistantMessage(aiMessage)
    do printfn ""
    do printfn "---[QA END]---"

let sendUserMessageTaskSeqBase
    (history: ChatHistory)
    (getCompletionAsync: ChatHistory -> TaskSeq<StreamingChatMessageContent>)
    (text: string)
    =
    let aiResponseParts =
        taskSeq {
            do
                history.AddUserMessage(text)
                printfn $"USER: {text}"
                printf $"AI: "
            for m in getCompletionAsync history do
                do printf $"{m.Content}"
                yield m.Content
        }
    let aiMessageText = aiResponseParts |> TaskSeq.toSeq |> String.concat ""
    do history.AddAssistantMessage(aiMessageText)
    do printfn "---[QA END]---"


let simulateStreamingResponse () =
    taskSeq {
        for c in "Lorem ipsum dolor sit amet, consectetur adipiscing elit." do
            do! Task.Delay(100)
            yield StreamingChatMessageContent(AuthorRole.Assistant, string c)
    }
    :> System.Collections.Generic.IAsyncEnumerable<_>


let messageWithMetadata (metadata: 'Metadata) (messageText: string) =
    let metadata = Serialization.Yaml.serialize metadata
    $"""
---
{metadata.Trim()}
---
{messageText}
"""

let namedUserMessage (authorName: string) messageText =
    messageText
    |> messageWithMetadata
        {|
            authorName = authorName
        |}


//type ChatHost(completionService: IChatCompletionService, history: ChatHistory) =

//    let getTogetherAICompletionStream (history: ChatHistory) =
//        let s = PromptExecutionSettings()
//        //s.ExtensionData <- dict ["stream", true]
//        completionService.GetStreamingChatMessageContentsAsync(history)

//    let sendUserMessageAndPrintResults = sendUserMessageAndPrintResults completionService history

//    do

//        ()


//type ChatBuilder(completionService: IChatCompletionService, history: ChatHistory) =

//    [<CustomOperation "anon_user_message">]
//    member this.SendUserMessage(messageText: string) =
//        sendUserMessageAndPrintResults completionService history messageText

    //member this.SendNamedUserMessage(authorName: string, messageText: string) =
    //    userMessageWithMetadata authorName messageText
