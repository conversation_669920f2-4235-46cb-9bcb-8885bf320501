﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <UserSecretsId>47cd5641-3189-4442-9201-4f6fa8376718</UserSecretsId>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Utils.fs" />
    <Compile Include="Json.fs" />
    <Compile Include="Configuration.fs" />
    <Compile Include="KernelMemory.fs" />
    <Compile Include="KnowledgeBase.fs" />
    <Compile Include="TogetherAI.fs" />
    <Compile Include="Http.fs" />
    <Compile Include="SemanticKernelHelpers.fs" />
    <Compile Include="Chat.fs" />
    <Compile Include="Core.fs" />
    <Compile Include="Agents.fs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Scripts\StringBuilder.fsx" />
    <None Include="Scripts\Env.fsx" />
    <None Include="Scripts\Config.fsx" />
    <None Include="Scripts\OpenRouter.fsx" />
    <None Include="Scripts\TogetherAI.fsx" />
    <None Include="Scripts\Serialization.fsx" />
    <None Include="Scripts\Chat.fsx" />
    <None Include="Scripts\Plugins.fsx" />
    <None Include="Scripts\Http.fsx" />
    <None Include="Scripts\Helpers.fsx" />
    <None Include="Scripts\SemanticKernelHelpers.fsx" />
    <None Include="Scripts\TestScript.fsx" />
    <None Include="Scripts\AgentTests.dib" />
  </ItemGroup>
  <ItemGroup>
    <None Include="appsettings.example.json" />
      <None Include="appsettings.multipleKernelMemoriesExample.json" />
    <None Include=".env" />
    <None Include=".gitignore" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="FSharp.Control.TaskSeq" Version="0.4.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Options.DataAnnotations" Version="9.0.7" />
    <PackageReference Include="Microsoft.KernelMemory.Core" Version="0.98.250508.3" />
    <PackageReference Include="Microsoft.KernelMemory.SemanticKernelPlugin" Version="0.98.250508.3" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.60.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Agents.Core" Version="1.60.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Yaml" Version="1.60.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.1" />
    <PackageReference Include="Serilog.HttpClient" Version="3.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\AssistantChatApp.Shared\AssistantChatApp.Shared.csproj" />
    <ProjectReference Include="..\AssistantChatApp.AIAgents.Shared.Dotnet\AssistantChatApp.AIAgents.Shared.Dotnet.fsproj" />
  </ItemGroup>
</Project>