import { apiClient } from './apiClient';
import { <PERSON><PERSON>, ChatFilt<PERSON>, CreateOrUpdateChatRequest, Message, PaginatedResponse, SendMessageResponse, ChatApi } from '../types';

export const chatApi: ChatApi = {
  // Get all chats with pagination and filters
  getChats: async (filters: ChatFilters = {}): Promise<PaginatedResponse<Chat>> => {
    // Build query string from filters
    const queryParams = new URLSearchParams();

    if (filters.page) queryParams.append('page', filters.page.toString());
    if (filters.limit) queryParams.append('limit', filters.limit.toString());
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.tags && filters.tags.length > 0) {
      filters.tags.forEach(tag => queryParams.append('tags', tag));
    }
    if (filters.participants && filters.participants.length > 0) {
      filters.participants.forEach(participant => queryParams.append('participants', participant));
    }
    if (filters.startDate) queryParams.append('startDate', filters.startDate);
    if (filters.endDate) queryParams.append('endDate', filters.endDate);

    return apiClient.getChats(filters);
  },

  // Get a single chat by ID
  getChat: async (chatId: string): Promise<Chat> => {
    return apiClient.getChat(chatId);
  },

  // Create a new chat
  createChat: async (data: CreateOrUpdateChatRequest): Promise<Chat> => {
    return apiClient.createChat(data);
  },

  // Update an existing chat
  updateChat: async (chatId: string, data: CreateOrUpdateChatRequest): Promise<Chat> => {
    return apiClient.updateChat(chatId, data);
  },

  // Delete a chat
  deleteChat: async (chatId: string): Promise<void> => {
    return apiClient.deleteChat(chatId);
  },

  // Get messages for a chat
  getMessages: async (chatId: string, page = 1, limit = 50): Promise<PaginatedResponse<Message>> => {
    return apiClient.getMessages(chatId, page, limit);
  },

  getMessagesByIds: async (
    chatId: string,
    messageIds: string[]
  ) => {
    return apiClient.getMessagesByIds(chatId, messageIds);
  },

  // Send a new message
  sendMessage: async (chatId: string, content: string, mentions: string[] = []): Promise<SendMessageResponse> => {
    return apiClient.sendMessage(chatId, content, mentions);
  },

  // Update a message
  updateMessage: async (
    chatId: string,
    messageId: string,
    content: string,
    mentions: string[] = []
  ): Promise<Message> => {
    return apiClient.updateMessage(chatId, messageId, content, mentions);
  },

  // Delete a message
  deleteMessage: async (chatId: string, messageId: string): Promise<void> => {
    return apiClient.deleteMessage(chatId, messageId);
  },
};
