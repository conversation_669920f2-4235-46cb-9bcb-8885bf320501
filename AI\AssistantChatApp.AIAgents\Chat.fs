module AssistantChatApp.AIAgents.Chat

open System
open System.Threading
open System.Threading.Tasks
open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.ChatCompletion

type AIAssistantChat(
    kernel: Kernel,
    initialMessages: seq<ChatMessageContent>
) =
    let chatSvc = kernel.GetRequiredService<IChatCompletionService>()

    let history = ChatHistory(initialMessages)

    //let executionSettings = PromptExe

    //let mutable lastAssistantMessage =
    //    ChatHistory.tryGetLastAssistantMessage history

    let createPromptExecSettings (modelId: string) =
        Connectors.OpenAI.OpenAIPromptExecutionSettings(
            FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(),
            ToolCallBehavior = Connectors.OpenAI.ToolCallBehavior.AutoInvokeKernelFunctions,
            ModelId = modelId
        )

    let sendAndGetCompletionAsync (modelId: string) ct = task {
        let settings = createPromptExecSettings modelId
        return! chatSvc.GetChatMessageContentAsync(history, settings, kernel, ct)
    }

    let sendAndGetCompletionStreamAsync (modelId: string) ct =
        let settings = createPromptExecSettings modelId
        chatSvc.GetStreamingChatMessageContentsAsync(history, settings, kernel, ct)

    member this.ChatHistory = history

    member this.SendAndGetCompletionAsync(modelId, ?cancellationToken) =
        let ct = defaultArg cancellationToken CancellationToken.None
        sendAndGetCompletionAsync modelId ct

    member this.SendAndGetCompletionStreamAsync(modelId, ?cancellationToken) =
        let ct = defaultArg cancellationToken CancellationToken.None
        sendAndGetCompletionStreamAsync modelId ct

    member this.SendAndGetCompletion(modelId, ?cancellationToken) =
        this.SendAndGetCompletionAsync(modelId, ?cancellationToken = cancellationToken)
        |> Async.AwaitTask
        |> Async.RunSynchronously
