import React from "react";
import {
  Paper,
  Grid2 as Grid,
  useTheme,
  Dialog,
  DialogContent,
  DialogActions,
  DialogTitle,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  Close as CloseIcon,
  // OpenInFull as OpenInFullIcon,
} from "@mui/icons-material";
import {
  MDXEditor,
  MDXEditorMethods,
  toolbarPlugin,
  headingsPlugin,
  listsPlugin,
  linkPlugin,
  quotePlugin,
  thematicBreakPlugin,
  markdownShortcutPlugin,
  codeBlockPlugin,
  diffSourcePlugin,
  directivesPlugin,
  DiffSourceToggleWrapper,
  UndoRedo,
  BoldItalicUnderlineToggles,
  BlockTypeSelect,
  CreateLink,
  ListsToggle,
  CodeToggle,
  RealmProvider,
} from "@mdxeditor/editor";
import {
  type PopupState,
  // usePopupState,
  bindDialog,
} from "material-ui-popup-state/hooks";
import { useTranslation } from "react-i18next";
import { AddUserMentionIconButton } from "../UserMention1";
import {
  mentionDirectiveDescriptor,
  MessageEditorState,
  useMessageEditorState,
  useUserSelectHandler1,
} from "../messageEditorUtils";
import { UserSelectionPopup } from "../UserSelectionPopup";
import { SendMessageButton, SendMessageFab } from "./Utils";

import "@mdxeditor/editor/style.css";
import "../../../mdx-editor.css";

export interface MessageMDXEditorProps {
  editorRef: React.RefObject<MDXEditorMethods>;
  initialTextContent: string;
  setTextContent: (text: string) => void;
  handleMentionButtonClick: (event: React.MouseEvent<HTMLElement>) => void;
}

export interface MessageEditorDialogProps {
  popupState: PopupState;
  editorState: MessageEditorState;
}

export const MessageMDXEditor: React.FC<MessageMDXEditorProps> = ({
  initialTextContent,
  setTextContent,
  editorRef,
  handleMentionButtonClick,
}) => {
  const { t } = useTranslation("messages");
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === "dark";

  return (
    <MDXEditor
      className={isDarkMode ? "dark-mdx-editor" : ""}
      ref={editorRef}
      markdown={initialTextContent}
      placeholder={t("message_editing_placeholder")}
      onChange={(content) => {
        setTextContent(content);
        // onMentionKeyDown(new KeyboardEvent("keydown"));
      }}
      contentEditableClassName="prose"
      plugins={[
        headingsPlugin(),
        listsPlugin(),
        linkPlugin(),
        quotePlugin(),
        codeBlockPlugin(),
        thematicBreakPlugin(),
        markdownShortcutPlugin(),
        diffSourcePlugin({ viewMode: "rich-text", readOnlyDiff: true }),
        directivesPlugin({
          directiveDescriptors: [mentionDirectiveDescriptor],
        }),
        toolbarPlugin({
          toolbarContents: () => (
            <>
              <AddUserMentionIconButton onClick={handleMentionButtonClick} />
              <DiffSourceToggleWrapper options={["rich-text", "source"]}>
                <UndoRedo />
                <BoldItalicUnderlineToggles />
                <BlockTypeSelect />
                <CreateLink />
                <ListsToggle />
                <CodeToggle />
              </DiffSourceToggleWrapper>
            </>
          ),
        }),
      ]}
    />
  );
};

export const MessageEditorFullscreenDialog: React.FC<
  MessageEditorDialogProps
> = ({ editorState, popupState }) => {
  const { t } = useTranslation(["common", "messages"]);

  const isSubmitDisabled = !editorState.checkIfIsSubmittable();

  const dialogProps = bindDialog(popupState);
  const handleClose = () => {
    editorState.setTextContent(editorState.textContent);
    popupState.close();
  };
  const handleSubmit = () => {
    editorState.handleSubmit();
    handleClose();
  };

  return (
    <Dialog
      maxWidth="xl"
      fullWidth
      scroll="paper"
      open={dialogProps.open}
      onClose={handleClose}
    >
      <DialogTitle>{t("messages:new_message")}</DialogTitle>
      <Tooltip title={t("common:close_fullscreen")}>
        <IconButton
          aria-label="close"
          onClick={popupState.close}
          sx={(theme) => ({
            position: "absolute",
            right: theme.spacing(1),
            top: theme.spacing(1),
            color: theme.palette.text.secondary,
          })}
        >
          <CloseIcon />
        </IconButton>
      </Tooltip>
      <DialogContent>
        <MessageMDXEditor
          editorRef={editorState.editorRef}
          initialTextContent={editorState.textContent}
          setTextContent={editorState.setTextContent}
          handleMentionButtonClick={editorState.handleMentionButtonClick}
        />
      </DialogContent>
      <DialogActions>
        <SendMessageButton
          onClick={handleSubmit}
          isPending={editorState.sendMessageMutation.isPending}
          isDisabled={isSubmitDisabled}
        />
      </DialogActions>
    </Dialog>
  );
};

export const MessageEditor: React.FC<{ chatId: string }> = ({ chatId }) => {
  // const { t } = useTranslation(["common"]);
  const editorState = useMessageEditorState(chatId);
  const {
    editorRef,
    textContent,
    setTextContent,
    handleMentionButtonClick,
    handleMentionPopupClose,
    checkIfIsSubmittable,
    handleSubmit,
    mentionPopupAnchor,
    sendMessageMutation,
    handleKeyDown,
  } = editorState;
  const handleUserSelect = useUserSelectHandler1({ editorRef, setTextContent });
  const isSubmitDisabled = !checkIfIsSubmittable();
  // const popupState = usePopupState({
  //   variant: "dialog",
  //   popupId: "message-editor-dialog",
  // });

  return (
    <Paper
      variant="outlined"
      sx={{
        p: 1,
        minHeight: 100,
        maxHeight: 300,
        width: "100%",
        overflowY: "auto",
        flexGrow: 1,
      }}
      onKeyDown={handleKeyDown}
    >
      <Grid
        container
        component="form"
        direction="row"
        wrap="nowrap"
        spacing={1}
        justifyContent="space-around"
        alignItems="center"
      >
        <Grid sx={{ flexGrow: 1 }}>
          <MessageMDXEditor
            editorRef={editorRef}
            initialTextContent={textContent}
            setTextContent={setTextContent}
            handleMentionButtonClick={handleMentionButtonClick}
          />
        </Grid>
        <Grid container>
          {/* Disable fullscreen mode until figuring out how to sync two MDXEditor instances. */}
          {/*
          <Grid>
            <Tooltip title={t("common:open_fullscreen")}>
              <IconButton size="small" onClick={popupState.open}>
                <OpenInFullIcon />
              </IconButton>
            </Tooltip>
          </Grid>
          */}
          <Grid>
            <SendMessageFab
              onClick={handleSubmit}
              isPending={sendMessageMutation.isPending}
              isDisabled={isSubmitDisabled}
            />
          </Grid>
        </Grid>
      </Grid>

      {/* Mention suggestions popper */}
      <UserSelectionPopup
        open={Boolean(mentionPopupAnchor)}
        anchorEl={mentionPopupAnchor}
        onClose={handleMentionPopupClose}
        chatId={chatId}
        onSelectUser={handleUserSelect}
      />
      {/* TODO: figure out how to sync two MDXEditor instances. Until then, disable fullscreen mode. */}
      {/*
      <MessageEditorFullscreenDialog
        editorState={editorState}
        popupState={popupState}
      />
      */}
    </Paper>
  );
};

export const MessageEditorInRealmProvider: React.FC<{ chatId: string }> = ({
  chatId,
}) => {
  return (
    <RealmProvider>
      <MessageEditor chatId={chatId} />
    </RealmProvider>
  );
};
