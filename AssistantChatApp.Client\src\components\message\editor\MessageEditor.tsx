import React from "react";
import {
  Box,
  Paper,
  Grid2 as Grid,
  useTheme,
  Dialog,
  DialogContent,
  DialogActions,
  DialogTitle,
  IconButton,
  <PERSON>lt<PERSON>,
  Stack,
} from "@mui/material";
import { type PopupState, bindDialog } from "material-ui-popup-state/hooks";
import {
  Close as CloseIcon,
  // OpenInFull as OpenInFullIcon,
} from "@mui/icons-material";
import {
  type MDXEditorProps,
  MDXEditor,
  toolbarPlugin,
  headingsPlugin,
  listsPlugin,
  linkPlugin,
  quotePlugin,
  thematicBreakPlugin,
  markdownShortcutPlugin,
  codeBlockPlugin,
  diffSourcePlugin,
  directivesPlugin,
  DiffSourceToggleWrapper,
  UndoRedo,
  BoldItalicUnderlineToggles,
  BlockTypeSelect,
  CreateLink,
  ListsToggle,
  CodeToggle,
  codeMirrorPlugin,
} from "@mdxeditor/editor";
import { useTranslation } from "react-i18next";
import { useAtom, useSetAtom } from "jotai";
import { AddUserMentionIconButton } from "../UserMention1";
import {
  MessageEditorState,
  useMessageEditorState,
  UseMessageEditorStateProps,
} from "./useMessageEditorState";
import { useUserSelectHandler1, mentionDirectiveDescriptor } from "./messageEditorUtils";
import { UserSelectionListDialog } from "../../user/UserSelectionListDialog";
import { SendMessageButton, SendMessageFab } from "./Utils";
import { useGetChat } from "../../../queries/chat";
import { MessageEditorMainToolbar } from "./MessageEditorMainToolbar";
import {
  useInteractionSettingsValuesFromContext,
  type InteractionSettingsValues,
} from "../interactionSettings";
import "@mdxeditor/editor/style.css";
import "@/mdx-editor.css";
import * as Atoms from "./atoms";
import { MDXEditorRef } from "./types";

export interface MessageMDXEditorProps {
  editorRef: MDXEditorRef;
  textContent: string;
  setTextContent: (text: string) => void;
}

export interface MessageEditorDialogProps {
  popupState: PopupState;
  editorState: MessageEditorState;
}

export interface MessageEditorProps {
  chatId: string;
  editorRef: MDXEditorRef;
  interactionSettingsValues: InteractionSettingsValues;
  setInteractionSettingsValues: React.Dispatch<
    React.SetStateAction<InteractionSettingsValues>
  >;
}

export interface OldMessageEditorProps extends MessageEditorState {
  chatId: string;
  renderMDXEditorToolbar: () => React.ReactNode;
}

export interface MessageMDXEditorToolbarProps {
  handleMentionButtonClick: (event: React.MouseEvent<HTMLElement>) => void;
  isToolbarVisible: boolean;
}

export interface MessageEditorStandaloneProps
  extends UseMessageEditorStateProps {
  renderMDXEditorToolbar: () => React.ReactNode;
}

export const MDXEditorFormattingToolbar: React.FC = () => {
  return [
    <UndoRedo />,
    <BoldItalicUnderlineToggles />,
    <BlockTypeSelect />,
    <CreateLink />,
    <ListsToggle />,
    <CodeToggle />,
  ];
};

export const MDXEditorAdvancedFormattingToolbar: React.FC = () => {
  return (
    <DiffSourceToggleWrapper options={["rich-text", "source"]}>
      <MDXEditorFormattingToolbar />
    </DiffSourceToggleWrapper>
  );
};

const MessageEditorMDXEditorToolbar: React.FC = () => {
  const { showFormattingToolbar } = useInteractionSettingsValuesFromContext();
  return (
    <Box sx={{ display: showFormattingToolbar ? "flex" : "none" }}>
      <MDXEditorAdvancedFormattingToolbar />
    </Box>
  );
};

export const MessageMDXEditorToolbar: React.FC<
  MessageMDXEditorToolbarProps
> = ({ handleMentionButtonClick, isToolbarVisible }) => {
  return (
    isToolbarVisible && (
      <>
        <AddUserMentionIconButton onClick={handleMentionButtonClick} />
        <MDXEditorFormattingToolbar />
      </>
    )
  );
};

export const MessageMDXEditor: React.FC<MessageMDXEditorProps> = ({
  textContent,
  setTextContent,
  editorRef,
}) => {
  const { t } = useTranslation("messages");
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === "dark";

  const handleChange: MDXEditorProps["onChange"] = React.useCallback(
    (markdown: string) => {
      if (markdown === textContent) return;
      setTextContent(markdown);
    },
    [textContent, setTextContent]
  );

  React.useEffect(() => {
    if (editorRef.current) {
      editorRef.current.setMarkdown(textContent);
    }
  }, [editorRef, textContent]);

  const className = "full-height" + (isDarkMode ? " dark-mdx-editor" : "");

  return (
    <MDXEditor
      className={className}
      ref={editorRef}
      markdown={textContent}
      placeholder={t("message_editing_placeholder")}
      onChange={handleChange}
      contentEditableClassName="prose"
      autoFocus
      plugins={[
        headingsPlugin(),
        listsPlugin(),
        linkPlugin(),
        quotePlugin(),
        codeBlockPlugin(),
        codeMirrorPlugin({
          codeBlockLanguages: {
            js: "JavaScript",
            ts: "TypeScript",
            py: "Python",
            html: "HTML",
            md: "Markdown",
            json: "JSON",
            xml: "XML",
            csharp: "C#",
            fsharp: "F#",
            txt: "text",
            "": "text",
          },
        }),
        thematicBreakPlugin(),
        markdownShortcutPlugin(),
        diffSourcePlugin({ viewMode: "rich-text", readOnlyDiff: true }),
        directivesPlugin({
          directiveDescriptors: [mentionDirectiveDescriptor],
        }),
        toolbarPlugin({
          toolbarContents: () => <MessageEditorMDXEditorToolbar />,
        }),
      ]}
    />
  );
};

export const MessageEditorFullscreenDialog: React.FC<
  MessageEditorDialogProps
> = ({ editorState, popupState }) => {
  const { t } = useTranslation(["common", "messages"]);

  const isSubmitDisabled = !editorState.checkIfIsSubmittable();

  const dialogProps = bindDialog(popupState);
  const handleClose = () => {
    editorState.setTextContent(editorState.textContent);
    popupState.close();
  };
  const handleSubmit = () => {
    editorState.handleSubmit();
    handleClose();
  };

  return (
    <Dialog
      maxWidth="xl"
      fullWidth
      scroll="paper"
      open={dialogProps.open}
      onClose={handleClose}
    >
      <DialogTitle>{t("messages:new_message")}</DialogTitle>
      <Tooltip title={t("common:close_fullscreen")}>
        <IconButton
          aria-label="close"
          onClick={popupState.close}
          sx={(theme) => ({
            position: "absolute",
            right: theme.spacing(1),
            top: theme.spacing(1),
            color: theme.palette.text.secondary,
          })}
        >
          <CloseIcon />
        </IconButton>
      </Tooltip>
      <DialogContent>
        <MessageMDXEditor
          editorRef={editorState.editorRef}
          textContent={editorState.textContent}
          setTextContent={editorState.setTextContent}
        />
      </DialogContent>
      <DialogActions>
        <SendMessageButton
          onClick={handleSubmit}
          isPending={editorState.sendMessageMutation.isPending}
          isDisabled={isSubmitDisabled}
        />
      </DialogActions>
    </Dialog>
  );
};

export const MessageEditorWithSubmitButton: React.FC<OldMessageEditorProps> = ({
  chatId,
  editorRef,
  textContent,
  setTextContent,
  handleMentionPopupClose,
  checkIfIsSubmittable,
  handleSubmit,
  mentionPopupAnchor,
  sendMessageMutation,
  handleKeyDown,
}) => {
  const { data: chat } = useGetChat(chatId);
  const participants = React.useMemo(
    () => chat?.participants ?? [],
    [chat?.participants]
  );
  const handleUserSelect = useUserSelectHandler1({ editorRef, setTextContent });
  const isSubmitDisabled = !checkIfIsSubmittable();
  // const popupState = usePopupState({
  //   variant: "dialog",
  //   popupId: "message-editor-dialog",
  // });

  return (
    <Paper
      variant="outlined"
      sx={{
        display: "flex",
        flexDirection: "column",
        p: 1,
        minHeight: 100,
        width: "100%",
        flexGrow: 1,
      }}
      onKeyDown={handleKeyDown}
    >
      <Grid
        container
        component="form"
        direction="row"
        wrap="nowrap"
        spacing={1}
        justifyContent="space-around"
        alignItems="center"
        sx={{ height: "100%", flex: 1 }}
      >
        {/*
        The `minHeight: 0` is needed to override flexbox's default behavior where flex items have an implicit `min-height: auto`.

        By default, flex items won't shrink below their content size, even when you set `overflow: hidden` or `height: 100%`. This means:

        - Without `minHeight: 0`: The Grid containing the editor will expand to fit all the MDXEditor content, ignoring the parent's height constraints.
        - With `minHeight: 0`: The Grid can shrink below its content size, allowing the `overflow: auto` on the inner `Box` to actually work.

        This is a common flexbox gotcha when you want scrollable content within a flex container. The minHeight: 0 essentially tells the flex item "you're allowed to be smaller than your content" which enables the scrolling behavior you want.

        And `height: "100%"` is needed to make the Grid fill the available height within its parent. Otherwise, the Grid would overflow its parent height.
        */}
        <Grid
          sx={{ flexGrow: 1, overflow: "hidden", minHeight: 0, height: "100%" }}
        >
          <Box sx={{ height: "100%", overflowY: "auto" }}>
            <MessageMDXEditor
              editorRef={editorRef}
              textContent={textContent}
              setTextContent={setTextContent}
            />
          </Box>
        </Grid>
        <Grid container>
          {/* Disable fullscreen mode until figuring out how to sync two MDXEditor instances. */}
          {/*
          <Grid>
            <Tooltip title={t("common:open_fullscreen")}>
              <IconButton size="small" onClick={popupState.open}>
                <OpenInFullIcon />
              </IconButton>
            </Tooltip>
          </Grid>
          */}
          <Grid>
            <SendMessageFab
              onClick={handleSubmit}
              isPending={sendMessageMutation.isPending}
              isDisabled={isSubmitDisabled}
            />
          </Grid>
        </Grid>
      </Grid>

      {/* Mention suggestions popper */}
      <UserSelectionListDialog
        open={Boolean(mentionPopupAnchor)}
        users={participants}
        onClose={handleMentionPopupClose}
        onSelectUser={handleUserSelect}
      />
      {/* TODO: figure out how to sync two MDXEditor instances. Until then, disable fullscreen mode. */}
      {/*
      <MessageEditorFullscreenDialog
        editorState={editorState}
        popupState={popupState}
      />
      */}
    </Paper>
  );
};

export const MessageEditorWithSubmitButtonStandalone: React.FC<
  MessageEditorStandaloneProps
> = ({ renderMDXEditorToolbar, ...props }) => {
  const editorState = useMessageEditorState(props);

  return (
    <MessageEditorWithSubmitButton
      chatId={props.chatId}
      renderMDXEditorToolbar={renderMDXEditorToolbar}
      {...editorState}
    />
  );
};

export const MessageEditor: React.FC<MessageEditorProps> = ({
  chatId,
  editorRef,
  interactionSettingsValues,
  setInteractionSettingsValues,
}) => {
  const [textContent, setTextContent] = useAtom(Atoms.messageTextContentAtom);
  const handleKeyDownAtom = useSetAtom(Atoms.handleEditorKeyDownAtom);

  const handleKeyDown = (event: React.KeyboardEvent) =>
    handleKeyDownAtom(chatId, editorRef, interactionSettingsValues, event);

  return (
    <Stack direction="column" justifyContent="stretch" sx={{ height: "100%" }}>
      <Box sx={{ flex: 1, overflow: "auto" }} onKeyDown={handleKeyDown}>
        <MessageMDXEditor
          editorRef={editorRef}
          textContent={textContent}
          setTextContent={setTextContent}
        />
      </Box>
      <MessageEditorMainToolbar
        chatId={chatId}
        editorRef={editorRef}
        interactionSettingsValues={interactionSettingsValues}
        setInteractionSettingsValues={setInteractionSettingsValues}
      />
    </Stack>
  );
};
