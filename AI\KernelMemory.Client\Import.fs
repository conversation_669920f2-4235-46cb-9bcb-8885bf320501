module Enixar.KernelMemory.Client.Import.Import

open System
open System.Threading.Tasks
open Microsoft.KernelMemory


let importDocumentAsync
    (memory: IKernelMemory)
    (document: DocumentImportEntry)
    =
    task {
        let tags =
            document.Tags
            |> Option.map (fun tags ->
                let tagCollection: TagCollection = TagCollection()
                for key, value in tags do
                    tagCollection.Add(key, value)
                tagCollection
            )
        return! memory.ImportDocumentAsync(
            filePath = document.FilePath,
            documentId = document.Id,
            ?index = document.Index,
            ?tags = tags
        )
    }

let importDocumentsAsync
    (memory: IKernelMemory)
    (documents: seq<DocumentImportEntry>)
    =
    task {
        let! results =
            documents
            |> Seq.map (importDocumentAsync memory)
            |> Task.WhenAll
        return results
    }

let importAllAsync
    (memory: IKernelMemory)
    (entries: seq<ImportEntry>)
    =
    task {
        let batches =
            entries
            |> Seq.map (fun entry -> entry.ToDocumentImportEntryList())
        let! batchResults =
            batches
            |> Seq.map (importDocumentsAsync memory)
            |> Task.WhenAll
        return batchResults |> Array.collect id
    }
