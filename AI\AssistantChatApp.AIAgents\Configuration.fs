namespace AssistantChatApp.AIAgents

open System
open System.ComponentModel.DataAnnotations
open System.Collections.Generic
open Microsoft.Extensions.Configuration
open Microsoft.Extensions.DependencyInjection
open Microsoft.Extensions.Options
open DocumentFormat.OpenXml.Wordprocessing

type ApiProviderId = string
type AgentId = string
type ModelId = string
type KernelMemoryInstanceId = string

[<CLIMutable>]
type ApiProviderSettings = {
    //Id: ApiProviderId
    [<Required>]
    ApiEndpoint: string
    [<Required>]
    ApiKey: string
}

type ApiProvidersMap = Map<ApiProviderId, ApiProviderSettings>
type ApiProvidersDict = Dictionary<ApiProviderId, ApiProviderSettings>

[<CLIMutable>]
type KnowledgeBaseSettings = {
    Enabled: bool
    [<Required>]
    RootDirPath: string
}

[<CLIMutable>]
type KernelMemoryPluginSearchSettings = {
    Limit: Nullable<int>
    /// 0.0 - 1.0
    MinRelevance: Nullable<float>
}

[<CLIMutable>]
type KernelMemoryPluginSettings = {
    [<Required>]
    Enabled: bool
    SearchSettings: KernelMemoryPluginSearchSettings
}

[<CLIMutable>]
type AgentSettings = {
    //[<Required>]
    //Id: AgentId
    Name: string
    [<Required>]
    ProviderId: ApiProviderId
    [<Required>]
    ModelId: ModelId
    //KernelMemoryInstanceId: KernelMemoryInstanceId option

    MainSystemPromptFilePath: string | null
    KbSettings: KnowledgeBaseSettings | null
    KernelMemoryPluginSettings: KernelMemoryPluginSettings | null
}

type AgentsMap = Map<AgentId, AgentSettings>
type AgentsDict = Dictionary<AgentId, AgentSettings>

[<CLIMutable>]
type KernelMemoryAccessSettings = {
    KernelMemoryServiceUrl: string | null
}

[<CLIMutable>]
type AgentServiceSettings = {
    [<Required>]
    ApiProviders: ApiProvidersDict
    [<Required>]
    Agents: AgentsDict
    KernelMemoryAccessSettings: KernelMemoryAccessSettings | null
}

type AgentFullSettings = {
    AgentId: AgentId
    Agent: AgentSettings
    ApiProvider: ApiProviderSettings
}
with
    member this.ApiProviderId = this.Agent.ProviderId

type AgentFullSettingsMap = Map<AgentId, AgentFullSettings>

type KernelMemoryInstanceConfigs = Map<KernelMemoryInstanceId, IConfiguration>

module Configuration =

    /// Concatenates two strings with a colon, e.g. `s1 + ":" + s2`
    let inline (@+) (s1: string) (s2: string) = s1 + ":" + s2

    module Keys =

        let [<Literal>] ApiProviders = "ApiProviders"
        let [<Literal>] Agents = "Agents"

    let getApiProviders (config: IConfiguration) =
        config.GetSection(Keys.ApiProviders).Get<ApiProvidersDict>()

    let getAgents (config: IConfiguration) =
        config.GetSection(Keys.Agents).Get<AgentsDict>()

    let getApiProviderSettings (providerId: ApiProviderId) (config: IConfiguration) =
        config.GetRequiredSection(Keys.ApiProviders @+ providerId).Get<ApiProviderSettings>()

    let getAgentSettings (agentId: AgentId) (config: IConfiguration) =
        config.GetRequiredSection(Keys.Agents @+ agentId).Get<AgentSettings>()

    let getApiProvidersAndAgentsSettings (config: IConfiguration) =
        config.Get<AgentServiceSettings>()
        |> Option.ofObj
        |> Option.defaultWith (fun () ->
            failwith $"Missing {nameof AgentServiceSettings} configuration"
        )

    let getApiProvidersAndAgentsSettingsSnapshot (sp: IServiceProvider) =
        sp.GetRequiredService<IOptionsSnapshot<AgentServiceSettings>>().Value
    
    let private addAgentServiceSettingsOptions (rootSectionName: string) (services: IServiceCollection) =
        services.AddOptions<AgentServiceSettings>()
            .ValidateDataAnnotations()
            .BindConfiguration(rootSectionName)
        |> fun _ -> services

    let private getAgentProviderSettings (settings: AgentServiceSettings) =
        query {
            for p in settings.ApiProviders do
            for a in settings.Agents do
            where (p.Key = a.Value.ProviderId)
            select {
                AgentId = a.Key
                ApiProvider = p.Value
                Agent = a.Value
            }
        }

    let private addAgentFullSettingsMapScoped (services: IServiceCollection) =
        services.AddScoped<AgentFullSettingsMap>(fun sp ->
            let settings = getApiProvidersAndAgentsSettingsSnapshot sp
            getAgentProviderSettings settings
            |> Seq.map (fun x -> x.AgentId, x)
            |> Map
        )

    let getFullAgentSettings (config: IConfiguration) =
        config
        |> getApiProvidersAndAgentsSettings
        |> getAgentProviderSettings

    let configureFullAgentSettings rootSectionName (services: IServiceCollection) =
        services
        |> addAgentServiceSettingsOptions rootSectionName
        |> addAgentFullSettingsMapScoped

    let getKernelMemoryAccessSettingsFromOptions (sp: IServiceProvider) =
        sp.GetRequiredService<IOptions<AgentServiceSettings>>()
            .Value
            .KernelMemoryAccessSettings
        |> Option.ofObj

    (*
    // Relevant only if multiple Kernel Memory instances support is implemented
    let getKernelMemoryInstanceConfigs (config: IConfiguration)
        : KernelMemoryInstanceConfigs
        =
        config.GetSection("KernelMemoryInstances").GetChildren()
        |> Seq.map (fun x -> x.Key, x :> IConfiguration)
        |> Map
    *)
