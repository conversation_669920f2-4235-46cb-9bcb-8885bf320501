import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  keyframes
} from '@mui/material';
import CreateOutlinedIcon from '@mui/icons-material/CreateOutlined';

interface TypingIndicatorProps {
  userName: string;
  color?: string;
}

const dotAnimation = keyframes`
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
`;

const highlightAnimation = keyframes`
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
`;

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  userName,
  color = 'primary.main'
}) => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev =>
        prev.length < 3 ? prev + '.' : ''
      );
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        background: `linear-gradient(90deg,
          transparent,
          ${color},
          transparent)`,
        backgroundSize: '400% 100%',
        animation: `${highlightAnimation} 2s infinite linear`,
        p: 1,
        borderRadius: 2
      }}
    >
      <CreateOutlinedIcon
        sx={{
          color,
          animation: `${dotAnimation} 1.5s infinite`
        }}
      />
      <Typography variant="body2">
        {userName} is responding{dots}
      </Typography>
    </Box>
  );
};
