//#r @"C:\Users\<USER>\.nuget\packages\microsoft.semantickernel\1.51.0\lib\net8.0\Microsoft.SemanticKernel.dll"

#r "nuget: Microsoft.SemanticKernel, 1.54"

open System
open System.ComponentModel
open Microsoft.SemanticKernel

type MathPlugin() =

    [<KernelFunction "logarithm">]
    member this.Logarithm(``base``: float, argument: float) =
        Math.Log(argument, ``base``)

    [<KernelFunction "natural_logarithm">]
    member this.NaturalLogarithm(argument: float) =
        log argument


type TimePlugin() =

    [<KernelFunction "get_current_datetime">]
    member this.GetCurrentDateTime() =
        DateTimeOffset.Now

    [<KernelFunction "add_timespan_to_datetime">]
    [<Description "Returns DateTime value in ISO-8601 format">]
    member this.AddTimeSpanToDateTime(
        [<Description "DateTime string value in ISO-8601 format">]
        dateTime: string,
        [<Description "Time span (a period of time) string value in the format `HH:mm:ss`">]
        timeSpan: string
    ) =
        let dt = DateTime.Parse dateTime
        let t = TimeSpan.Parse timeSpan
        (dt + t).ToString("O")

    [<KernelFunction "subtract_timespan_from_datetime">]
    [<Description "Returns DateTime value in ISO-8601 format">]
    member this.SubtractTimeSpanFromDateTime(
        [<Description "DateTime string value in ISO-8601 format">]
        dateTime: string,
        [<Description "Time span (a period of time) string value in the format `HH:mm:ss`">]
        timeSpan: string
    ) =
        let dt = DateTime.Parse dateTime
        let t = TimeSpan.Parse timeSpan
        (dt - t).ToString("O")
