## Frontend app info

Root directory (relative to the workspace root): `AssistantChatApp.Client`
App overview file path: `AssistantChatApp.Client\docs\app_overview.md`

## Frontend Tech Stack

- Language: TypeScript
- Framework:React.js
- Basic UI components: MUI (`@mui/material`)
- Async state management: TanStack Query (`@tanstack/react-query`)
- Global state management: <PERSON><PERSON> (`jotai`) or <PERSON><PERSON><PERSON> (`zustand`)
- Client routing: TanStack Router (`@tanstack/react-router`)
- Forms management: TanStack Form (`@tanstack/react-form`)
- Rich-text (Markdown) editing: MDXEditor (`@mdxeditor/editor`)
- Resizable layout: React Resizable Panels (`react-resizable-panels`)
- Markdown rendering: React Markdown (`react-markdown`)

## Security

DO NOT read or modify:
- *.env files
