## Frontend app info

Root directory (relative to the workspace root): `AssistantChatApp.Client`
App overview file path: `AssistantChatApp.Client\docs\app_overview.md`

## Frontend Tech Stack

- Language: TypeScript
- Framework:React.js
- Basic UI components: MUI (`@mui/material`)
- Async state management: TanStack Query (`@tanstack/react-query`)
- Global state management: <PERSON><PERSON> (`jotai`) or <PERSON><PERSON><PERSON> (`zustand`)
- Client routing: TanStack Router (`@tanstack/react-router`)
- Forms management: TanStack Form (`@tanstack/react-form`)
- Rich-text (Markdown) editing: MDXEditor (`@mdxeditor/editor`)
- Resizable layout: React Resizable Panels (`react-resizable-panels`)
- Markdown rendering: React Markdown (`react-markdown`)

## Coding Guidelines


- In TypeScript, avoid using `any` type. Try to assign a proper narrowed type. If the type truly cannot be determined at compile time, use `unknown` instead.
- When designing React component, avoid defining a lot of disparate logic directly in a single component. Instead, define several narrowly focused hooks and components, and then use them to compose the necessary more complex components.
- In TypeScript, prefer named exports over default exports.
- For styling, stick to MUI's theme-based styling and, if necessary, `sx` prop for inline styles. For setting colors, sizes, etc., use MUI theme helpers (`theme.palette`, `theme.spacing`, etc.) instead of hard-coded values.
- When defining UI titles, captions, labels, etc., use `react-i18next` library instead of hard-coded strings. I.e., use `useTranslation` hook and `t` function returned by it in places of UI-targeted string literals. When you introduce new translation keys, provide the corresponding const JSON-like dictionary object (with translations to English) above the component definition. Also, in a block comment above the object, include translation JSON for Russian, so that I'd be able to quickly add it later to the dedicated translation JSON files.

## Security

DO NOT read or modify:
- *.env files
