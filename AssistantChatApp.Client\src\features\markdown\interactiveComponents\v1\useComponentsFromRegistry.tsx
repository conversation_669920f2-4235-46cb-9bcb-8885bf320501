/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react";
import type { ExtraProps } from "react-markdown";
import { v4 as uuidv4 } from "uuid";
import { ComponentRegistry } from "./componentRegistry";
import type { ComponentAction, ComponentContext, ComponentName } from "./types";
// import type { Components } from "react-markdown"
import type { Element as HastNode } from "hast";
import {
  tryParseJsonObject,
  tryExtractTextFromRootOrFirstChild,
} from "../utils";

// interface InteractiveMarkdownProps {
//   componentRegistry: ComponentRegistry
//   children: string
//   messageId: string
//   onComponentAction?: (action: ComponentAction<unknown>) => void
// }

interface RegisteredComponentProps<TComponentName extends string = ComponentName, TData = unknown> {
  componentRegistry: ComponentRegistry;
  componentName: string;
  messageId: string;
  handleAction: (
    action: Omit<ComponentAction<TComponentName, TData>, "messageId" | "componentId">,
    componentId: string
  ) => void;
  handleUpdate: (data: TData, componentId: string) => void;
  children?: React.ReactNode;
  node?: HastNode;
  [key: string]: unknown;
}

const usePropsParsedFromChildren = (children: React.ReactNode) =>
  React.useMemo(() => {
    const text = tryExtractTextFromRootOrFirstChild(children);
    if (text) {
      return tryParseJsonObject(text);
    }
    return undefined;
  }, [children]);

// eslint-disable-next-line react-refresh/only-export-components
const RegisteredComponent: React.FC<RegisteredComponentProps> = (
  props
) => {
  const {
    componentRegistry,
    messageId,
    componentName,
    handleAction,
    handleUpdate,
    children,
    node,
    ...otherProps
  } = props;
  const componentId = React.useMemo(() => uuidv4(), []);

  // Initialize component if needed
  React.useEffect(() => {
    componentRegistry.initializeComponent(
      messageId,
      componentId,
      componentName
    );
  }, [componentId, componentName, messageId, componentRegistry]);

  const registration = componentRegistry.getComponent(componentName);

  const context: ComponentContext = {
    messageId,
    componentId,
    onAction: (action) => handleAction(action, componentId),
    onUpdate: (data) => handleUpdate(data, componentId),
    data: componentRegistry.getComponentState(messageId, componentId),
  };

  const propsFromChildren = usePropsParsedFromChildren(children);

  // Merge props from directive attributes and parsed from children
  const finalProps = React.useMemo(
    () => ({
      ...otherProps,
      ...propsFromChildren,
    }),
    [otherProps, propsFromChildren]
  );

  if (!registration) return null;

  const Component = registration.component;
  return (
    <Component {...finalProps} context={context}>
      {children}
    </Component>
  );
};

export function useComponentsFromRegistry(
  componentRegistry: ComponentRegistry,
  messageId: string,
  onComponentAction?: (action: ComponentAction) => void
) {
  // Set up action handler
  React.useEffect(() => {
    console.debug("Setting up action handler", messageId, componentRegistry)
    if (onComponentAction) {
      componentRegistry.setActionHandler(messageId, onComponentAction);
    }
    return () => {
      componentRegistry.cleanup(messageId);
    };
  }, [messageId, onComponentAction, componentRegistry]);

  const handleAction = React.useCallback(
    (
      action: Omit<ComponentAction, "messageId" | "componentId">,
      componentId: string
    ) => {
      console.debug("On component action", messageId, componentId, action, componentRegistry)

      const fullAction: ComponentAction = {
        ...action,
        messageId,
        componentId,
      };
      componentRegistry.handleAction(fullAction);
    },
    [messageId, componentRegistry]
  );

  const handleUpdate = React.useCallback(
    (data: unknown, componentId: string) => {
      componentRegistry.setComponentState(messageId, componentId, data);
    },
    [messageId, componentRegistry]
  );

  const components = React.useMemo(() => {
    const result: { [key: string]: React.ComponentType<object & ExtraProps> } =
      {};

    // Create wrapper components for each registered component
    const registeredComponents = Array.from(
      componentRegistry["components"].keys()
    );

    registeredComponents.forEach((componentName) => {
      console.debug("Registering component", componentName);
      result[componentName] = (props) => (
        <RegisteredComponent
          componentRegistry={componentRegistry}
          componentName={componentName}
          messageId={messageId}
          handleAction={handleAction}
          handleUpdate={handleUpdate}
          {...props}
        />
      );
    });
    console.debug("Registered components found: ", result)
    return result;
  }, [handleAction, handleUpdate, messageId, componentRegistry]);

  return components;
};
