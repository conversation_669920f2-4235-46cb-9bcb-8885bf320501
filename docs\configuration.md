# Chat Server ASP.NET Core

- AI Agent Service Base URL
- JWT Settings:
    - Secret
    - Issuer
    - Audience
- DB Connection String


# AI Agent Service

- Host URL(s)
- AI API providers. For each provider:
    - Provider ID
    - API Base URL
    - AI Provider API Key
- Kernel Memory access settings
    - KM endpoint (if ommitted, fallbacks to the KM service alias defined in the .NET Aspire)
- Agent configurations. For each agent:
    - Agent ID
    - API provider ID
    - Model ID
    - System Prompt File Path
    - KB:
        - KB Root Dir Path
        - Main System Prompt Content
        - Additional prompt files
