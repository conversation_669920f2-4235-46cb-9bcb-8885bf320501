import { Router } from 'express';
import { loginController, getMeController } from '../controllers/auth';
import { validate } from '../middleware/validation';
import { loginSchema } from '../validation/auth';
import { authenticate } from '../auth/middleware';

const router = Router();

// POST /auth/login - Login user
router.post('/login', validate(loginSchema), loginController);

// GET /auth/me - Get current user
router.get('/me', authenticate, getMeController);

export default router;
