/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from 'react';
import {
  Avatar,
  Box,
  Typography,
  Paper,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  Tooltip,
  Stack,
} from '@mui/material';
import {
  MoreH<PERSON>zontal as MoreIcon,
  Trash2 as TrashIcon,
  Copy as CopyIcon
} from 'lucide-react';
import { Message } from '../../types';
import { MessageAdditionalContentAccordionView as MessageAdditionalContentView } from "./MessageAdditionalContentView";
import { useDeleteMessage } from "../../mutations/message";
import { DeleteConfirmationDialog } from '../common/DeleteConfirmationDialog';
import { useTranslation } from 'react-i18next';
import { useDateFnsLocale, formatAsTimeInHoursAndMinutes } from '../common/dateTimeUtils';
import { ReactMarkdownMessageView } from './MessageMarkdownView';

interface MessageItemProps {
  message: Message;
  chatId: string;
  isConsecutive?: boolean;
}

interface MessageContentViewProps {
  message: Message;
}

const MessageContentView: React.FC<MessageContentViewProps> = ({ message }) => {
  // const textContent =
  //     content && content.length > 0
  //     ? replaceMentionDirectivesWithMarkdownLinks(content)
  //     : undefined;
  return (
    <>
      {message.content && <ReactMarkdownMessageView message={message} />}
      {message.otherContent && <MessageAdditionalContentView content={message.otherContent} />}
    </>
  )
}

const MessageItem: React.FC<MessageItemProps> = ({ message, chatId, isConsecutive = false }) => {
  const { t } = useTranslation("messages");
  const dateFnsLocale = useDateFnsLocale();
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const messageCreatedAtStr =
    formatAsTimeInHoursAndMinutes(new Date(message.createdAt), dateFnsLocale);

  const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setMenuAnchorEl(null);
  };

  const handleCopyMessage = () => {
    navigator.clipboard.writeText(message.content);
    handleCloseMenu();
  };

  const menuItemHandler = (action: () => void) => () => {
    action();
    handleCloseMenu();
  };

  const deleteMessageMutation = useDeleteMessage();
  const [isDeleteDialogOpened, setDeleteDialogOpened] = useState(false);
  const openDeleteDialog = () => setDeleteDialogOpened(true);
  const closeDeleteDialog = () => setDeleteDialogOpened(false);

  const confirmDeleteMessage = () => {
    deleteMessageMutation.mutate({ chatId, messageId: message.id });
  };

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          mb: 1,
          position: 'relative',
          pl: 7,
        }}
      >
        {!isConsecutive && (
          <Avatar
            src={message.author.avatar}
            alt={message.author.displayName}
            sx={{
              position: 'absolute',
              left: 0,
              top: 0,
              width: 40,
              height: 40,
            }}
          />
        )}

        <Box sx={{ flex: 1, maxWidth: 'calc(100% - 50px)' }}>
          {!isConsecutive && (
            <Stack direction="row" alignItems="center" mb={0.5}>
              <Typography variant="subtitle2" fontWeight="medium">
                {message.author.displayName}
              </Typography>
              {message.author.isAI && (
                <Typography
                  variant="caption"
                  fontWeight={600}
                  sx={{
                    ml: 1,
                    bgcolor: 'primary.main',
                    color: 'primary.contrastText',
                    px: 0.8,
                    py: 0.2,
                    borderRadius: 1,
                    fontSize: '0.65rem'
                  }}
                >
                  {t('ai_badge')}
                </Typography>
              )}
              <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                {messageCreatedAtStr}
              </Typography>
            </Stack>
          )}

          <Box sx={{ display: 'flex', alignItems: 'flex-start' }} className="message-container">
            <Paper
              elevation={0}
              sx={{
                p: 0.5,
                maxWidth: '100%',
                bgcolor: message.author.isAI ? 'primary.50' : 'secondary.100',
                borderRadius: 2,
              }}
            >
              <Box
                sx={{
                  '& > p:first-of-type': { mt: 0 },
                  '& > p:last-child': { mb: 0 },
                  overflow: 'hidden',
                  wordBreak: 'break-word',
                }}
              >
                <MessageContentView message={message} />
              </Box>
            </Paper>

            <Tooltip title={t('message_options')}>
              <IconButton
                size="small"
                onClick={handleOpenMenu}
                sx={{
                  ml: 0.5,
                  opacity: 0,
                  transition: 'opacity 0.2s',
                  '&:hover': { opacity: 1 },
                  '.message-container:hover &': { opacity: 0.6 },
                }}
              >
                <MoreIcon size={16} />
              </IconButton>
            </Tooltip>

            <Menu
              anchorEl={menuAnchorEl}
              open={Boolean(menuAnchorEl)}
              onClose={handleCloseMenu}
              transformOrigin={{ horizontal: 'right', vertical: 'top' }}
              anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
              <MenuItem onClick={handleCopyMessage}>
                <ListItemIcon>
                  <CopyIcon size={18} />
                </ListItemIcon>
                {t('copy_message')}
              </MenuItem>
              {/* <MenuItem onClick={handleCloseMenu}>
                <ListItemIcon>
                  <EditIcon size={18} />
                </ListItemIcon>
                {t('edit')}
              </MenuItem> */}
              <MenuItem
                onClick={menuItemHandler(openDeleteDialog)}
                sx={{ color: 'error.main' }}
              >
                <ListItemIcon>
                  <TrashIcon size={18} color="#d32f2f" />
                </ListItemIcon>
                {t('delete')}
              </MenuItem>
            </Menu>
          </Box>
        </Box>
      </Box>
      <DeleteConfirmationDialog
        isOpened={isDeleteDialogOpened}
        onClose={closeDeleteDialog}
        onConfirm={confirmDeleteMessage}
        message={t('delete_message_confirmation')}
        title={t('delete_message_title')}
      />
    </>
  );
};

export default MessageItem;
