#r "nuget: FSharp.Control.TaskSeq, 0.4.0"
#r "nuget: FSharp.Control.AsyncSeq, 3.2.1"
#r "nuget: Microsoft.SemanticKernel, 1.54"
#r "nuget: Microsoft.SemanticKernel.Agents.Core, 1.54"
#r "nuget: Microsoft.Extensions.Logging.Console, 9.0.5"
#r "nuget: Microsoft.Extensions.Http, 9.0.5"

#load "Config.fsx"

open System
open System.Net.Http
open System.Collections.Generic
open Microsoft.Extensions.DependencyInjection
open Microsoft.Extensions.Logging
open Microsoft.Extensions.Http
open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.ChatCompletion
open Microsoft.SemanticKernel.Agents
open FSharp.Control
open Config

let ignoreMany (services: seq<obj>) = ignore services

let addTogetherOpenAIChatCompletion httpClient modelId (builder: IKernelBuilder) =
    builder.AddOpenAIChatCompletion(
        modelId = modelId,
        endpoint = Uri TogetherAI.ApiEndpoint,
        apiKey = TogetherAI.apiKey,
        httpClient = httpClient
    )

let addOpenRouterChatCompletion httpClient modelId (builder: IKernelBuilder) =
    builder.AddOpenAIChatCompletion(
        modelId = modelId,
        endpoint = Uri OpenRouter.ApiEndpoint,
        apiKey = OpenRouter.apiKey,
        httpClient = httpClient
    )

let makeStreamingRequestWithRetryLoop
    retryAttempts
    retryPause
    retryPauseMultiplier
    (requestStream: unit -> TaskSeq<'T>)
    =
    let rec retryLoop (attempt: int) (pause: TimeSpan) = taskSeq {
        let handleRateLimitError () = taskSeq {
            if attempt < retryAttempts then
                printfn $"Rate limit is reached. Pausing for {pause.TotalSeconds} s, then retrying..."
                do! System.Threading.Tasks.Task.Delay(pause)
                yield! retryLoop (attempt + 1) (pause * retryPauseMultiplier)
        }
        try
            yield! requestStream ()
        with
        | :? System.ClientModel.ClientResultException as e when e.Status = 429 ->
            yield! handleRateLimitError ()
        | :? System.AggregateException as e when
            e.InnerExceptions
            |> Seq.exists (function
                | :? System.ClientModel.ClientResultException as inner when inner.Status = 429 -> true
                | _ -> false)
            ->
            yield! handleRateLimitError ()
    }
    retryLoop 1 retryPause
