{"version": "81300", "data": {"id": "2d0a0ea0-dea6-4e56-b614-bc2da1c9a716", "offsetX": -21.89087455508397, "offsetY": -504.1515300393347, "zoom": 90.23968789942111, "gridSize": 15, "layers": [{"id": "367f52de-9af8-431e-8071-54b5ba8c1a01", "type": "diagram-links", "isSvg": true, "transformed": true, "models": {"dda52abd-d20f-422a-a228-8d6684a0e16b": {"id": "dda52abd-d20f-422a-a228-8d6684a0e16b", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "d9b63777-90f5-41bb-a048-83b7fcb8d0ea", "sourcePort": "8441c443-2e14-4b81-bb67-e5a7c7847ff9", "target": "23e89499-ff86-4824-ad16-3540565c9be4", "targetPort": "ec0b10aa-b7f1-4f4f-8578-fe2852b9edbf", "points": [{"id": "3746befb-4202-4f9d-8dff-b9cecf49186d", "type": "point", "x": 165.9145652553161, "y": 738.6037346042019}, {"id": "7d910df5-f962-4c0b-bda7-61ad04a7ff69", "type": "point", "x": 154.62851517523097, "y": 738.6037346042019}, {"id": "3ab1992d-a6e0-4154-a37d-287ecd5a6b4d", "type": "point", "x": 154.62851517523097, "y": 764.3062030827344}, {"id": "e8b8152e-6917-4dc0-a000-9b5d9c70542e", "type": "point", "x": 144.09116410335218, "y": 764.3062030827344}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "23e89499-ff86-4824-ad16-3540565c9be4", "local_column_attnum": 2, "referenced_table_uid": "d9b63777-90f5-41bb-a048-83b7fcb8d0ea", "referenced_column_attnum": 1}}, "99ed4e79-7fa0-4477-be21-5d4417fcad79": {"id": "99ed4e79-7fa0-4477-be21-5d4417fcad79", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "69b59a42-859f-403a-ae03-8a0b94443c1e", "sourcePort": "80c28158-584f-485a-8ec8-d2f2cc2964bf", "target": "793215f8-8027-468d-93c0-3731f85f8858", "targetPort": "845649d8-51c4-4fd1-a022-c11a9c78da29", "points": [{"id": "ff9d53bb-2cca-4e2d-91b5-c888c0e2e02f", "type": "point", "x": 675.************, "y": 738.6037346042019}, {"id": "eb3d4575-9a1c-44dd-bda1-c77a9004de34", "type": "point", "x": 651.7672104280753, "y": 738.6037346042019}, {"id": "45d3ea09-5370-4ddc-8b84-0bb00cc5b459", "type": "point", "x": 651.7672104280753, "y": 914.3004304375031}, {"id": "e71af382-f55f-4fe4-9016-73caa1da2966", "type": "point", "x": 639.0739903868348, "y": 914.3004304375031}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "793215f8-8027-468d-93c0-3731f85f8858", "local_column_attnum": 2, "referenced_table_uid": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced_column_attnum": 1}}, "0bfefbf7-2f6b-4cb3-870d-4915a4483a4d": {"id": "0bfefbf7-2f6b-4cb3-870d-4915a4483a4d", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "69b59a42-859f-403a-ae03-8a0b94443c1e", "sourcePort": "80c28158-584f-485a-8ec8-d2f2cc2964bf", "target": "e0b0e202-fc1c-4bf2-a748-c9b0019f16ee", "targetPort": "fd470224-8938-41c9-892a-2bae28633e8f", "points": [{"id": "434d21eb-f150-41f3-84d1-634b472199e6", "type": "point", "x": 675.************, "y": 738.6037346042019}, {"id": "69b95049-d7c6-455f-9cf0-ba102b4f1edc", "type": "point", "x": 666.9910910737971, "y": 738.6037346042019}, {"id": "801e645c-7a22-4b26-8537-c6a05ec1697c", "type": "point", "x": 666.9910910737971, "y": 1175.7109031195437}, {"id": "b6fa6a59-5223-4449-900e-1b77b0ad6cd8", "type": "point", "x": 639.0739903868348, "y": 1175.7109031195437}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "e0b0e202-fc1c-4bf2-a748-c9b0019f16ee", "local_column_attnum": 4, "referenced_table_uid": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced_column_attnum": 1}}, "871999d7-69ab-4bc5-9b56-41cc00a6af5a": {"id": "871999d7-69ab-4bc5-9b56-41cc00a6af5a", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "d9b63777-90f5-41bb-a048-83b7fcb8d0ea", "sourcePort": "2a0118ac-7943-411e-98e5-443b1e8c6f52", "target": "7e094b40-622f-455d-8cb2-7bf8bcc6d52c", "targetPort": "33990caa-2c6f-4fde-994f-d19e2d662143", "points": [{"id": "8d46b371-6d5a-4948-91fe-9c6747d5eb33", "type": "point", "x": 399.0797161073534, "y": 738.6037346042019}, {"id": "9d64226f-5301-41c7-ab2e-d4af7ad1fe3b", "type": "point", "x": 400.66736101591107, "y": 738.6037346042019}, {"id": "4f7f21b3-7e6b-4e0c-86bb-64bfcd928592", "type": "point", "x": 400.66736101591107, "y": 764.3062030827344}, {"id": "1e3d311a-7d42-4c0c-bde1-9209568d1281", "type": "point", "x": 405.9088624229806, "y": 764.3062030827344}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "7e094b40-622f-455d-8cb2-7bf8bcc6d52c", "local_column_attnum": 2, "referenced_table_uid": "d9b63777-90f5-41bb-a048-83b7fcb8d0ea", "referenced_column_attnum": 1}}, "d2b5af83-86ad-445b-8526-5cedec389a21": {"id": "d2b5af83-86ad-445b-8526-5cedec389a21", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "69b59a42-859f-403a-ae03-8a0b94443c1e", "sourcePort": "80c28158-584f-485a-8ec8-d2f2cc2964bf", "target": "7e094b40-622f-455d-8cb2-7bf8bcc6d52c", "targetPort": "3d50f75c-0b99-409c-b5e8-7d663ff8971f", "points": [{"id": "9de07ad7-a7bd-4ce0-8e74-47b2cd779dbc", "type": "point", "x": 675.************, "y": 738.6037346042019}, {"id": "d75939e6-cab7-46ed-8263-f5f1181f2afe", "type": "point", "x": 617.3340269453679, "y": 738.6037346042019}, {"id": "db17652d-0bf3-4b68-9d6f-505b6a800896", "type": "point", "x": 617.3340269453679, "y": 738.6037346042019}, {"id": "28d7355a-3c02-4988-8e9b-1cc001551558", "type": "point", "x": 639.0739903868347, "y": 738.6037346042019}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "7e094b40-622f-455d-8cb2-7bf8bcc6d52c", "local_column_attnum": 1, "referenced_table_uid": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced_column_attnum": 1}}, "b1c869ef-d8ec-48ad-80d1-3052e650f7e9": {"id": "b1c869ef-d8ec-48ad-80d1-3052e650f7e9", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "69b59a42-859f-403a-ae03-8a0b94443c1e", "sourcePort": "80c28158-584f-485a-8ec8-d2f2cc2964bf", "target": "0848a98a-839a-469c-9089-5bb3f01bf1a0", "targetPort": "4a85e378-5c04-44b5-bcaa-510e08b1f92d", "points": [{"id": "d8ba3237-b94a-4d2a-bcec-7e53b06228a2", "type": "point", "x": 675.************, "y": 738.6037346042019}, {"id": "b7dd03c2-4dde-438e-bf71-9ac31bd5a868", "type": "point", "x": 684.8530408126434, "y": 738.6037346042019}, {"id": "afe7a560-f942-46b9-80f2-6147f80bd3e1", "type": "point", "x": 684.8530408126434, "y": 1308.************}, {"id": "138512f5-6808-43f7-bb77-5237c7be60df", "type": "point", "x": 639.0824965229085, "y": 1308.************}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "0848a98a-839a-469c-9089-5bb3f01bf1a0", "local_column_attnum": 1, "referenced_table_uid": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced_column_attnum": 1}}, "cd4880e1-70a0-4f16-a779-493bc2c273ed": {"id": "cd4880e1-70a0-4f16-a779-493bc2c273ed", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "69b59a42-859f-403a-ae03-8a0b94443c1e", "sourcePort": "af2d6ca8-d566-44f5-bbaf-3adb9cff040a", "target": "31daa55d-0035-4943-af9a-ddf9e13a03f7", "targetPort": "03b2d7ad-9c00-4d07-ace2-3a55bc075486", "points": [{"id": "d734a893-299f-4458-b473-2e97b4449d6d", "type": "point", "x": 909.071056565333, "y": 738.6037346042019}, {"id": "28a69845-f670-46c0-b03c-d7fac1d99e06", "type": "point", "x": 904.6368423969475, "y": 738.6037346042019}, {"id": "51ef95bb-3671-41af-8cd2-ed13c69ffe5a", "type": "point", "x": 904.6368423969475, "y": 1004.2975435407143}, {"id": "d75611ad-0fd1-4279-8d04-0132d42470df", "type": "point", "x": 945.************1, "y": 1004.2975435407143}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "31daa55d-0035-4943-af9a-ddf9e13a03f7", "local_column_attnum": 2, "referenced_table_uid": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced_column_attnum": 1}}, "589d2e71-ff6b-41dc-9789-b377369773d4": {"id": "589d2e71-ff6b-41dc-9789-b377369773d4", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "sourcePort": "59edf89f-c6c2-4101-8c43-c590924f3d9e", "target": "31daa55d-0035-4943-af9a-ddf9e13a03f7", "targetPort": "1aa8188a-cd04-4141-bef0-cf947da53ee5", "points": [{"id": "979b0b1b-ec2a-4ca8-a290-bd2ad048e1e7", "type": "point", "x": 1179.0825751388802, "y": 1173.************}, {"id": "b9dd9d6e-f5b3-400d-ab19-d439d0be9de5", "type": "point", "x": 1191.1509221453982, "y": 1173.************}, {"id": "79d47350-a3ba-40f8-a725-54a611e6e65a", "type": "point", "x": 1191.1509221453982, "y": 978.5951208385482}, {"id": "f981b043-e42f-4be4-9259-59f18c362a1a", "type": "point", "x": 1179.0739777550261, "y": 978.5951208385482}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "31daa55d-0035-4943-af9a-ddf9e13a03f7", "local_column_attnum": 1, "referenced_table_uid": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "referenced_column_attnum": 1}}, "f6349b9e-c7ce-47df-9cac-704f931b9335": {"id": "f6349b9e-c7ce-47df-9cac-704f931b9335", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "sourcePort": "59edf89f-c6c2-4101-8c43-c590924f3d9e", "target": "526ca1c0-dbde-4b7c-afaf-51cbde24044a", "targetPort": "6b3fcd66-454b-4d83-81e6-6f330d05a126", "points": [{"id": "50b35a48-96c2-4fb7-a25d-3bf040116f68", "type": "point", "x": 1179.0825751388802, "y": 1173.************}, {"id": "dd3f42b6-5eff-44d4-9235-70a03aacc6b8", "type": "point", "x": 1178.9611636743134, "y": 1173.************}, {"id": "69e6f4ce-1524-4f1e-a4ea-3f946596910f", "type": "point", "x": 1178.9611636743134, "y": 1308.6008854453557}, {"id": "c3167b7f-2a74-498a-92b4-aa630be301b2", "type": "point", "x": 1230.************, "y": 1308.6008854453557}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "526ca1c0-dbde-4b7c-afaf-51cbde24044a", "local_column_attnum": 1, "referenced_table_uid": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "referenced_column_attnum": 1}}, "ddfd3e5e-1028-4216-9a1d-f06d40c28e97": {"id": "ddfd3e5e-1028-4216-9a1d-f06d40c28e97", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "69b59a42-859f-403a-ae03-8a0b94443c1e", "sourcePort": "af2d6ca8-d566-44f5-bbaf-3adb9cff040a", "target": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "targetPort": "60e548ca-466e-4abd-b09d-6ffcde345f87", "points": [{"id": "270731b5-f39c-4c82-8789-4818c49bedbe", "type": "point", "x": 909.071056565333, "y": 738.6037346042019}, {"id": "ee183696-4cf3-4189-bf74-9c717fbe5abf", "type": "point", "x": 935.1390657086706, "y": 738.6037346042019}, {"id": "03857f44-6d27-43aa-a592-195793515156", "type": "point", "x": 935.1390657086706, "y": 1340.************}, {"id": "00a90430-4a35-4cf7-8ce0-683f6f9f03b9", "type": "point", "x": 945.************, "y": 1340.************}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "local_column_attnum": 6, "referenced_table_uid": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced_column_attnum": 1}}, "9e099b54-e3fb-4ff0-9381-b44774826f44": {"id": "9e099b54-e3fb-4ff0-9381-b44774826f44", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "69b59a42-859f-403a-ae03-8a0b94443c1e", "sourcePort": "af2d6ca8-d566-44f5-bbaf-3adb9cff040a", "target": "5a741f44-4b56-415d-95a4-abf8c6cdc3d9", "targetPort": "c2ecd42f-3ac8-4380-a1d2-9dcb98403cc4", "points": [{"id": "96b7a366-0b55-47ae-ae4a-6b847954f29c", "type": "point", "x": 909.071056565333, "y": 738.6037346042019}, {"id": "4c4bc0d3-eac6-4b71-a50a-7bd0ce52692d", "type": "point", "x": 1231.1548260189402, "y": 738.6037346042019}, {"id": "2502a022-c51b-48f7-821a-c41386b7e983", "type": "point", "x": 1231.1548260189402, "y": 779.************}, {"id": "fbfcf545-c2d3-4b60-ad40-ffc46b2832a4", "type": "point", "x": 1230.************, "y": 779.************}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "5a741f44-4b56-415d-95a4-abf8c6cdc3d9", "local_column_attnum": 2, "referenced_table_uid": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced_column_attnum": 1}}, "44ff7bdb-fccf-41c0-ba1b-5f162d4183e3": {"id": "44ff7bdb-fccf-41c0-ba1b-5f162d4183e3", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "81950116-7e04-463c-8247-8699c1336edc", "sourcePort": "a0259055-c778-402a-980f-b540bbe48e98", "target": "5a741f44-4b56-415d-95a4-abf8c6cdc3d9", "targetPort": "cbdd5914-e93d-4ba4-97e9-3d02f4421abd", "points": [{"id": "502f94cb-f28f-479f-be96-2d076d72312a", "type": "point", "x": 1464.0739777550261, "y": 903.************}, {"id": "08e2c2aa-fdc6-4c45-a8b1-1c8775a27b6c", "type": "point", "x": 1489.1307849273844, "y": 903.************}, {"id": "3637135b-45bc-45f5-93f0-6abb09927b9b", "type": "point", "x": 1489.1307849273844, "y": 753.5951277286254}, {"id": "a6cf9724-21a7-4ab2-8928-78fe53e7fbff", "type": "point", "x": 1464.0739777550261, "y": 753.5951277286254}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "5a741f44-4b56-415d-95a4-abf8c6cdc3d9", "local_column_attnum": 1, "referenced_table_uid": "81950116-7e04-463c-8247-8699c1336edc", "referenced_column_attnum": 1}}, "5a6e47ab-2483-407b-8198-66390e8ecf27": {"id": "5a6e47ab-2483-407b-8198-66390e8ecf27", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "69b59a42-859f-403a-ae03-8a0b94443c1e", "sourcePort": "af2d6ca8-d566-44f5-bbaf-3adb9cff040a", "target": "81950116-7e04-463c-8247-8699c1336edc", "targetPort": "40ace589-fb34-43bc-8dde-2917cff3038a", "points": [{"id": "64fe0a9b-798d-4f35-a48a-efc031ea3e5f", "type": "point", "x": 909.071056565333, "y": 738.6037346042019}, {"id": "fc5b5c12-3160-4707-92e0-4edc61b5d47e", "type": "point", "x": 1183.3938031183443, "y": 738.6037346042019}, {"id": "979fac3d-8c41-4c34-be76-fb4a85509f4d", "type": "point", "x": 1183.3938031183443, "y": 954.9999708362652}, {"id": "ad49dfcf-34b1-48a0-bb4f-1240814482ba", "type": "point", "x": 1230.************, "y": 954.9999708362652}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "81950116-7e04-463c-8247-8699c1336edc", "local_column_attnum": 3, "referenced_table_uid": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced_column_attnum": 1}}, "a4737442-e39c-4e7b-8ac5-5fc211eff0ec": {"id": "a4737442-e39c-4e7b-8ac5-5fc211eff0ec", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "sourcePort": "59edf89f-c6c2-4101-8c43-c590924f3d9e", "target": "81950116-7e04-463c-8247-8699c1336edc", "targetPort": "7f3e2cf0-d41a-4d80-bc4e-9134531a80f7", "points": [{"id": "61e317b3-6055-4725-b68e-3b6f0101b26e", "type": "point", "x": 1179.0825751388802, "y": 1173.************}, {"id": "4c948213-dc82-4dd7-a981-887d005615c0", "type": "point", "x": 1223.************, "y": 1173.************}, {"id": "25d8c058-ca1b-48fb-bafc-69e83e982ca4", "type": "point", "x": 1223.************, "y": 929.2975939104656}, {"id": "8905cd23-75c1-40a3-826e-9e96aa6b4311", "type": "point", "x": 1230.************, "y": 929.2975939104656}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "81950116-7e04-463c-8247-8699c1336edc", "local_column_attnum": 2, "referenced_table_uid": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "referenced_column_attnum": 1}}}}, {"id": "32cc196b-b1bb-4441-8e28-fe3554ed08e9", "type": "diagram-nodes", "isSvg": false, "transformed": true, "models": {"23e89499-ff86-4824-ad16-3540565c9be4": {"id": "23e89499-ff86-4824-ad16-3540565c9be4", "type": "table", "selected": false, "x": -60, "y": 645, "ports": [{"id": "2e8417fd-f182-4952-a2bd-c3892ecb76ee", "type": "one<PERSON><PERSON>", "x": -59.07396386050189, "y": 764.3062030827344, "name": "coll-port-2-left", "alignment": "left", "parentNode": "23e89499-ff86-4824-ad16-3540565c9be4", "links": []}, {"id": "ec0b10aa-b7f1-4f4f-8578-fe2852b9edbf", "type": "one<PERSON><PERSON>", "x": 114.09116410335217, "y": 764.3062030827344, "name": "coll-port-2-right", "alignment": "right", "parentNode": "23e89499-ff86-4824-ad16-3540565c9be4", "links": ["dda52abd-d20f-422a-a228-8d6684a0e16b"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["2e8417fd-f182-4952-a2bd-c3892ecb76ee", "ec0b10aa-b7f1-4f4f-8578-fe2852b9edbf"], "otherInfo": {"data": {"columns": [{"name": "Id", "atttypid": 23, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "d", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "integer", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "i", "genexpr": null, "relname": "AspNetRoleClaims", "is_view_only": false, "attcompression": null, "seqrelid": 18340, "seqtypid": 23, "seqstart": "1", "seqincrement": "1", "seqmax": "2147483647", "seqmin": "1", "seqcache": "1", "seqcycle": false, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"], "cid": "c16"}, {"name": "RoleId", "atttypid": 25, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetRoleClaims", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"], "cid": "c17"}, {"name": "ClaimType", "atttypid": 25, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetRoleClaims", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"], "cid": "c18"}, {"name": "ClaimV<PERSON>ue", "atttypid": 25, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetRoleClaims", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text"], "cid": "c19"}], "name": "AspNetRoleClaims", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18346, "name": "PK_AspNetRoleClaims", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "Id", "cid": "c13"}], "include": [], "cid": "c12"}], "foreign_key": [{"name": "FK_AspNetRoleClaims_AspNetRoles_RoleId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [2], "confkey": [1], "confrelid": 18326, "fknsp": "public", "fktab": "AspNetRoleClaims", "refnspoid": 2200, "refnsp": "public", "reftab": "AspNetRoles", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "RoleId", "references": "d9b63777-90f5-41bb-a048-83b7fcb8d0ea", "referenced": "Id", "references_table_name": "public.AspNetRoles", "cid": "c15"}], "remote_schema": "public", "remote_table": "AspNetRoles", "coveringindex": "IX_AspNetRoleClaims_RoleId", "autoindex": true, "hasindex": true, "cid": "c14"}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "d9b63777-90f5-41bb-a048-83b7fcb8d0ea": {"id": "d9b63777-90f5-41bb-a048-83b7fcb8d0ea", "type": "table", "selected": false, "x": 195, "y": 645, "ports": [{"id": "8441c443-2e14-4b81-bb67-e5a7c7847ff9", "type": "one<PERSON><PERSON>", "x": 195.9145652553161, "y": 738.6037346042019, "name": "coll-port-1-left", "alignment": "left", "parentNode": "d9b63777-90f5-41bb-a048-83b7fcb8d0ea", "links": ["dda52abd-d20f-422a-a228-8d6684a0e16b"]}, {"id": "2a0118ac-7943-411e-98e5-443b1e8c6f52", "type": "one<PERSON><PERSON>", "x": 369.0797161073534, "y": 738.6037346042019, "name": "coll-port-1-right", "alignment": "right", "parentNode": "d9b63777-90f5-41bb-a048-83b7fcb8d0ea", "links": ["871999d7-69ab-4bc5-9b56-41cc00a6af5a"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["8441c443-2e14-4b81-bb67-e5a7c7847ff9", "2a0118ac-7943-411e-98e5-443b1e8c6f52"], "otherInfo": {"data": {"columns": [{"name": "Id", "atttypid": 25, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetRoles", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "Name", "atttypid": 1043, "attlen": "256", "attnum": 2, "attndims": 0, "atttypmod": 260, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(256)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetRoles", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "NormalizedName", "atttypid": 1043, "attlen": "256", "attnum": 3, "attndims": 0, "atttypmod": 260, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(256)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetRoles", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "ConcurrencyStamp", "atttypid": 25, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetRoles", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"]}], "name": "AspNetRoles", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18331, "name": "PK_AspNetRoles", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "Id"}], "include": []}], "unique_constraint": [], "foreign_key": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "793215f8-8027-468d-93c0-3731f85f8858": {"id": "793215f8-8027-468d-93c0-3731f85f8858", "type": "table", "selected": false, "x": 435, "y": 795, "ports": [{"id": "9c018dff-a89f-4aa3-a2a6-0000e804555e", "type": "one<PERSON><PERSON>", "x": 435.9088624229805, "y": 914.3004304375031, "name": "coll-port-2-left", "alignment": "left", "parentNode": "793215f8-8027-468d-93c0-3731f85f8858", "links": []}, {"id": "845649d8-51c4-4fd1-a022-c11a9c78da29", "type": "one<PERSON><PERSON>", "x": 609.0739903868348, "y": 914.3004304375031, "name": "coll-port-2-right", "alignment": "right", "parentNode": "793215f8-8027-468d-93c0-3731f85f8858", "links": ["99ed4e79-7fa0-4477-be21-5d4417fcad79"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["9c018dff-a89f-4aa3-a2a6-0000e804555e", "845649d8-51c4-4fd1-a022-c11a9c78da29"], "otherInfo": {"data": {"columns": [{"name": "Id", "atttypid": 23, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "d", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "integer", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "i", "genexpr": null, "relname": "AspNetUserClaims", "is_view_only": false, "attcompression": null, "seqrelid": 18353, "seqtypid": 23, "seqstart": "1", "seqincrement": "1", "seqmax": "2147483647", "seqmin": "1", "seqcache": "1", "seqcycle": false, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "UserId", "atttypid": 25, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserClaims", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "ClaimType", "atttypid": 25, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserClaims", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"]}, {"name": "ClaimV<PERSON>ue", "atttypid": 25, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserClaims", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text"]}], "name": "AspNetUserClaims", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18359, "name": "PK_AspNetUserClaims", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "Id"}], "include": []}], "foreign_key": [{"name": "FK_AspNetUserClaims_AspNetUsers_UserId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [2], "confkey": [1], "confrelid": 18333, "fknsp": "public", "fktab": "AspNetUserClaims", "refnspoid": 2200, "refnsp": "public", "reftab": "AspNetUsers", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "UserId", "references": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced": "Id", "references_table_name": "public.AspNetUsers"}], "remote_schema": "public", "remote_table": "AspNetUsers", "coveringindex": "IX_AspNetUserClaims_UserId", "autoindex": true, "hasindex": true}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "e0b0e202-fc1c-4bf2-a748-c9b0019f16ee": {"id": "e0b0e202-fc1c-4bf2-a748-c9b0019f16ee", "type": "table", "selected": false, "x": 435, "y": 1005, "ports": [{"id": "eed9b067-b2ba-418c-8a0e-76effe9057f4", "type": "one<PERSON><PERSON>", "x": 435.9088624229805, "y": 1175.7109031195437, "name": "coll-port-4-left", "alignment": "left", "parentNode": "e0b0e202-fc1c-4bf2-a748-c9b0019f16ee", "links": []}, {"id": "fd470224-8938-41c9-892a-2bae28633e8f", "type": "one<PERSON><PERSON>", "x": 609.0739903868348, "y": 1175.7109031195437, "name": "coll-port-4-right", "alignment": "right", "parentNode": "e0b0e202-fc1c-4bf2-a748-c9b0019f16ee", "links": ["0bfefbf7-2f6b-4cb3-870d-4915a4483a4d"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["eed9b067-b2ba-418c-8a0e-76effe9057f4", "fd470224-8938-41c9-892a-2bae28633e8f"], "otherInfo": {"data": {"columns": [{"name": "<PERSON>gin<PERSON><PERSON><PERSON>", "atttypid": 25, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserLogins", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "Provide<PERSON><PERSON><PERSON>", "atttypid": 25, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserLogins", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"]}, {"name": "ProviderDisplayName", "atttypid": 25, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserLogins", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text"]}, {"name": "UserId", "atttypid": 25, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserLogins", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text", "text"]}], "name": "AspNetUserLogins", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18371, "name": "PK_AspNetUserLogins", "col_count": 2, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "<PERSON>gin<PERSON><PERSON><PERSON>"}, {"column": "Provide<PERSON><PERSON><PERSON>"}], "include": []}], "foreign_key": [{"name": "FK_AspNetUserLogins_AspNetUsers_UserId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [4], "confkey": [1], "confrelid": 18333, "fknsp": "public", "fktab": "AspNetUserLogins", "refnspoid": 2200, "refnsp": "public", "reftab": "AspNetUsers", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "UserId", "references": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced": "Id", "references_table_name": "public.AspNetUsers"}], "remote_schema": "public", "remote_table": "AspNetUsers", "coveringindex": "IX_AspNetUserLogins_UserId", "autoindex": true, "hasindex": true}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "7e094b40-622f-455d-8cb2-7bf8bcc6d52c": {"id": "7e094b40-622f-455d-8cb2-7bf8bcc6d52c", "type": "table", "selected": false, "x": 435, "y": 645, "ports": [{"id": "33990caa-2c6f-4fde-994f-d19e2d662143", "type": "one<PERSON><PERSON>", "x": 435.9088624229806, "y": 764.3062030827344, "name": "coll-port-2-left", "alignment": "left", "parentNode": "7e094b40-622f-455d-8cb2-7bf8bcc6d52c", "links": ["871999d7-69ab-4bc5-9b56-41cc00a6af5a"]}, {"id": "5cccaed7-82b4-493e-bfdf-9f8be1d21fe0", "type": "one<PERSON><PERSON>", "x": 609.0739903868347, "y": 764.3062030827344, "name": "coll-port-2-right", "alignment": "right", "parentNode": "7e094b40-622f-455d-8cb2-7bf8bcc6d52c", "links": []}, {"id": "a5985631-f1a5-47df-8091-6dc708e7345f", "type": "one<PERSON><PERSON>", "x": 435.9088624229806, "y": 738.6037346042019, "name": "coll-port-1-left", "alignment": "left", "parentNode": "7e094b40-622f-455d-8cb2-7bf8bcc6d52c", "links": []}, {"id": "3d50f75c-0b99-409c-b5e8-7d663ff8971f", "type": "one<PERSON><PERSON>", "x": 609.0739903868347, "y": 738.6037346042019, "name": "coll-port-1-right", "alignment": "right", "parentNode": "7e094b40-622f-455d-8cb2-7bf8bcc6d52c", "links": ["d2b5af83-86ad-445b-8526-5cedec389a21"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["33990caa-2c6f-4fde-994f-d19e2d662143", "5cccaed7-82b4-493e-bfdf-9f8be1d21fe0", "a5985631-f1a5-47df-8091-6dc708e7345f", "3d50f75c-0b99-409c-b5e8-7d663ff8971f"], "otherInfo": {"data": {"columns": [{"name": "UserId", "atttypid": 25, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserRoles", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "RoleId", "atttypid": 25, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserRoles", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"]}], "name": "AspNetUserRoles", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18383, "name": "PK_AspNetUserRoles", "col_count": 2, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "UserId"}, {"column": "RoleId"}], "include": []}], "foreign_key": [{"name": "FK_AspNetUserRoles_AspNetRoles_RoleId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [2], "confkey": [1], "confrelid": 18326, "fknsp": "public", "fktab": "AspNetUserRoles", "refnspoid": 2200, "refnsp": "public", "reftab": "AspNetRoles", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "RoleId", "references": "d9b63777-90f5-41bb-a048-83b7fcb8d0ea", "referenced": "Id", "references_table_name": "public.AspNetRoles"}], "remote_schema": "public", "remote_table": "AspNetRoles", "coveringindex": "IX_AspNetUserRoles_RoleId", "autoindex": true, "hasindex": true}, {"name": "FK_AspNetUserRoles_AspNetUsers_UserId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [1], "confkey": [1], "confrelid": 18333, "fknsp": "public", "fktab": "AspNetUserRoles", "refnspoid": 2200, "refnsp": "public", "reftab": "AspNetUsers", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "UserId", "references": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced": "Id", "references_table_name": "public.AspNetUsers"}], "remote_schema": "public", "remote_table": "AspNetUsers", "coveringindex": null, "autoindex": false, "hasindex": false}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "0848a98a-839a-469c-9089-5bb3f01bf1a0": {"id": "0848a98a-839a-469c-9089-5bb3f01bf1a0", "type": "table", "selected": false, "x": 435, "y": 1215, "ports": [{"id": "bc7a8ae3-4453-4807-bf3a-11d880517265", "type": "one<PERSON><PERSON>", "x": 435.91746011178725, "y": 1308.************, "name": "coll-port-1-left", "alignment": "left", "parentNode": "0848a98a-839a-469c-9089-5bb3f01bf1a0", "links": []}, {"id": "4a85e378-5c04-44b5-bcaa-510e08b1f92d", "type": "one<PERSON><PERSON>", "x": 609.0824965229085, "y": 1308.************, "name": "coll-port-1-right", "alignment": "right", "parentNode": "0848a98a-839a-469c-9089-5bb3f01bf1a0", "links": ["b1c869ef-d8ec-48ad-80d1-3052e650f7e9"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["bc7a8ae3-4453-4807-bf3a-11d880517265", "4a85e378-5c04-44b5-bcaa-510e08b1f92d"], "otherInfo": {"data": {"columns": [{"name": "UserId", "atttypid": 25, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2 3", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserTokens", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "<PERSON>gin<PERSON><PERSON><PERSON>", "atttypid": 25, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2 3", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserTokens", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"]}, {"name": "Name", "atttypid": 25, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2 3", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserTokens", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text"]}, {"name": "Value", "atttypid": 25, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2 3", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUserTokens", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text", "text"]}], "name": "AspNetUserTokens", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18400, "name": "PK_AspNetUserTokens", "col_count": 3, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "UserId"}, {"column": "<PERSON>gin<PERSON><PERSON><PERSON>"}, {"column": "Name"}], "include": []}], "foreign_key": [{"name": "FK_AspNetUserTokens_AspNetUsers_UserId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [1], "confkey": [1], "confrelid": 18333, "fknsp": "public", "fktab": "AspNetUserTokens", "refnspoid": 2200, "refnsp": "public", "reftab": "AspNetUsers", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "UserId", "references": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced": "Id", "references_table_name": "public.AspNetUsers"}], "remote_schema": "public", "remote_table": "AspNetUsers", "coveringindex": null, "autoindex": false, "hasindex": false}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "69b59a42-859f-403a-ae03-8a0b94443c1e": {"id": "69b59a42-859f-403a-ae03-8a0b94443c1e", "type": "table", "selected": false, "x": 705, "y": 645, "ports": [{"id": "80c28158-584f-485a-8ec8-d2f2cc2964bf", "type": "one<PERSON><PERSON>", "x": 705.************, "y": 738.6037346042019, "name": "coll-port-1-left", "alignment": "left", "parentNode": "69b59a42-859f-403a-ae03-8a0b94443c1e", "links": ["99ed4e79-7fa0-4477-be21-5d4417fcad79", "0bfefbf7-2f6b-4cb3-870d-4915a4483a4d", "d2b5af83-86ad-445b-8526-5cedec389a21", "b1c869ef-d8ec-48ad-80d1-3052e650f7e9"]}, {"id": "af2d6ca8-d566-44f5-bbaf-3adb9cff040a", "type": "one<PERSON><PERSON>", "x": 879.071056565333, "y": 738.6037346042019, "name": "coll-port-1-right", "alignment": "right", "parentNode": "69b59a42-859f-403a-ae03-8a0b94443c1e", "links": ["cd4880e1-70a0-4f16-a779-493bc2c273ed", "ddfd3e5e-1028-4216-9a1d-f06d40c28e97", "9e099b54-e3fb-4ff0-9381-b44774826f44", "5a6e47ab-2483-407b-8198-66390e8ecf27"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["80c28158-584f-485a-8ec8-d2f2cc2964bf", "af2d6ca8-d566-44f5-bbaf-3adb9cff040a"], "otherInfo": {"data": {"columns": [{"name": "Id", "atttypid": 25, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "DisplayName", "atttypid": 25, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"]}, {"name": "Avatar", "atttypid": 25, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text"]}, {"name": "CreatedAt", "atttypid": 1184, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "timestamp with time zone", "displaytypname": "timestamp with time zone", "cltype": "timestamp with time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1184, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "information_schema.time_stamp", "time with time zone", "time without time zone", "timestamp with time zone", "timestamp with time zone", "timestamp without time zone"]}, {"name": "IsAI", "atttypid": 16, "attlen": null, "attnum": 5, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "boolean", "displaytypname": "boolean", "cltype": "boolean", "inheritedfrom": null, "inheritedid": null, "elemoid": 16, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["boolean", "character", "character varying", "text"]}, {"name": "UserName", "atttypid": 1043, "attlen": "256", "attnum": 6, "attndims": 0, "atttypmod": 260, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(256)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "NormalizedUserName", "atttypid": 1043, "attlen": "256", "attnum": 7, "attndims": 0, "atttypmod": 260, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(256)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "Email", "atttypid": 1043, "attlen": "256", "attnum": 8, "attndims": 0, "atttypmod": 260, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(256)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "NormalizedEmail", "atttypid": 1043, "attlen": "256", "attnum": 9, "attndims": 0, "atttypmod": 260, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(256)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "EmailConfirmed", "atttypid": 16, "attlen": null, "attnum": 10, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "boolean", "displaytypname": "boolean", "cltype": "boolean", "inheritedfrom": null, "inheritedid": null, "elemoid": 16, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["boolean", "boolean", "character", "character varying", "text"]}, {"name": "PasswordHash", "atttypid": 25, "attlen": null, "attnum": 11, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text", "text"]}, {"name": "SecurityStamp", "atttypid": 25, "attlen": null, "attnum": 12, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text", "text", "text"]}, {"name": "ConcurrencyStamp", "atttypid": 25, "attlen": null, "attnum": 13, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text", "text", "text", "text"]}, {"name": "PhoneNumber", "atttypid": 25, "attlen": null, "attnum": 14, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text", "text", "text", "text", "text"]}, {"name": "PhoneNumberConfirmed", "atttypid": 16, "attlen": null, "attnum": 15, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "boolean", "displaytypname": "boolean", "cltype": "boolean", "inheritedfrom": null, "inheritedid": null, "elemoid": 16, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["boolean", "boolean", "boolean", "character", "character varying", "text"]}, {"name": "TwoFactorEnabled", "atttypid": 16, "attlen": null, "attnum": 16, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "boolean", "displaytypname": "boolean", "cltype": "boolean", "inheritedfrom": null, "inheritedid": null, "elemoid": 16, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["boolean", "boolean", "boolean", "boolean", "character", "character varying", "text"]}, {"name": "LockoutEnd", "atttypid": 1184, "attlen": null, "attnum": 17, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "timestamp with time zone", "displaytypname": "timestamp with time zone", "cltype": "timestamp with time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1184, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "information_schema.time_stamp", "time with time zone", "time without time zone", "timestamp with time zone", "timestamp with time zone", "timestamp with time zone", "timestamp without time zone"]}, {"name": "LockoutEnabled", "atttypid": 16, "attlen": null, "attnum": 18, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "boolean", "displaytypname": "boolean", "cltype": "boolean", "inheritedfrom": null, "inheritedid": null, "elemoid": 16, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["boolean", "boolean", "boolean", "boolean", "boolean", "character", "character varying", "text"]}, {"name": "AccessFailedCount", "atttypid": 23, "attlen": null, "attnum": 19, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "integer", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "AspNetUsers", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}], "name": "AspNetUsers", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18338, "name": "PK_AspNetUsers", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "Id"}], "include": []}], "unique_constraint": [], "foreign_key": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "31daa55d-0035-4943-af9a-ddf9e13a03f7": {"id": "31daa55d-0035-4943-af9a-ddf9e13a03f7", "type": "table", "selected": false, "x": 975, "y": 885, "ports": [{"id": "03b2d7ad-9c00-4d07-ace2-3a55bc075486", "type": "one<PERSON><PERSON>", "x": 975.************1, "y": 1004.2975435407143, "name": "coll-port-2-left", "alignment": "left", "parentNode": "31daa55d-0035-4943-af9a-ddf9e13a03f7", "links": ["cd4880e1-70a0-4f16-a779-493bc2c273ed"]}, {"id": "1f8f40f1-adaa-44e9-8a4c-c7e169a7e3d9", "type": "one<PERSON><PERSON>", "x": 1149.0739777550261, "y": 1004.2975435407143, "name": "coll-port-2-right", "alignment": "right", "parentNode": "31daa55d-0035-4943-af9a-ddf9e13a03f7", "links": []}, {"id": "ab052340-7b1b-4993-9810-bf6ffa883987", "type": "one<PERSON><PERSON>", "x": 975.************1, "y": 978.5951208385482, "name": "coll-port-1-left", "alignment": "left", "parentNode": "31daa55d-0035-4943-af9a-ddf9e13a03f7", "links": []}, {"id": "1aa8188a-cd04-4141-bef0-cf947da53ee5", "type": "one<PERSON><PERSON>", "x": 1149.0739777550261, "y": 978.5951208385482, "name": "coll-port-1-right", "alignment": "right", "parentNode": "31daa55d-0035-4943-af9a-ddf9e13a03f7", "links": ["589d2e71-ff6b-41dc-9789-b377369773d4"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["03b2d7ad-9c00-4d07-ace2-3a55bc075486", "1f8f40f1-adaa-44e9-8a4c-c7e169a7e3d9", "ab052340-7b1b-4993-9810-bf6ffa883987", "1aa8188a-cd04-4141-bef0-cf947da53ee5"], "otherInfo": {"data": {"columns": [{"name": "ChatId", "atttypid": 25, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "ChatParticipants", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "UserId", "atttypid": 25, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "ChatParticipants", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"]}, {"name": "JoinedAt", "atttypid": 1184, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "timestamp with time zone", "displaytypname": "timestamp with time zone", "cltype": "timestamp with time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1184, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "ChatParticipants", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "information_schema.time_stamp", "time with time zone", "time without time zone", "timestamp with time zone", "timestamp with time zone", "timestamp without time zone"]}], "name": "ChatParticipants", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18424, "name": "PK_ChatParticipants", "col_count": 2, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "ChatId"}, {"column": "UserId"}], "include": []}], "foreign_key": [{"name": "FK_ChatParticipants_AspNetUsers_UserId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [2], "confkey": [1], "confrelid": 18333, "fknsp": "public", "fktab": "ChatParticipants", "refnspoid": 2200, "refnsp": "public", "reftab": "AspNetUsers", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "UserId", "references": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced": "Id", "references_table_name": "public.AspNetUsers"}], "remote_schema": "public", "remote_table": "AspNetUsers", "coveringindex": "IX_ChatParticipants_UserId", "autoindex": true, "hasindex": true}, {"name": "FK_ChatParticipants_Chats_ChatId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [1], "confkey": [1], "confrelid": 18407, "fknsp": "public", "fktab": "ChatParticipants", "refnspoid": 2200, "refnsp": "public", "reftab": "Chats", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "ChatId", "references": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "referenced": "Id", "references_table_name": "public.Chats"}], "remote_schema": "public", "remote_table": "Chats", "coveringindex": null, "autoindex": false, "hasindex": false}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "526ca1c0-dbde-4b7c-afaf-51cbde24044a": {"id": "526ca1c0-dbde-4b7c-afaf-51cbde24044a", "type": "table", "selected": false, "x": 1260, "y": 1215, "ports": [{"id": "6b3fcd66-454b-4d83-81e6-6f330d05a126", "type": "one<PERSON><PERSON>", "x": 1260.************, "y": 1308.6008854453557, "name": "coll-port-1-left", "alignment": "left", "parentNode": "526ca1c0-dbde-4b7c-afaf-51cbde24044a", "links": ["f6349b9e-c7ce-47df-9cac-704f931b9335"]}, {"id": "3ca241ae-b64f-4e45-bf07-54ebc3f6334d", "type": "one<PERSON><PERSON>", "x": 1434.0739777550261, "y": 1308.6008854453557, "name": "coll-port-1-right", "alignment": "right", "parentNode": "526ca1c0-dbde-4b7c-afaf-51cbde24044a", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["6b3fcd66-454b-4d83-81e6-6f330d05a126", "3ca241ae-b64f-4e45-bf07-54ebc3f6334d"], "otherInfo": {"data": {"columns": [{"name": "ChatId", "atttypid": 25, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "ChatTags", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "TagName", "atttypid": 25, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "ChatTags", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"]}], "name": "ChatTags", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18441, "name": "PK_ChatTags", "col_count": 2, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "ChatId"}, {"column": "TagName"}], "include": []}], "foreign_key": [{"name": "FK_ChatTags_Chats_ChatId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [1], "confkey": [1], "confrelid": 18407, "fknsp": "public", "fktab": "ChatTags", "refnspoid": 2200, "refnsp": "public", "reftab": "Chats", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "ChatId", "references": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "referenced": "Id", "references_table_name": "public.Chats"}], "remote_schema": "public", "remote_table": "Chats", "coveringindex": null, "autoindex": false, "hasindex": false}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "1264f81d-1d8b-4f59-a5cd-c503bcb02581": {"id": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "type": "table", "selected": false, "x": 975, "y": 1080, "ports": [{"id": "2369c8ec-7229-42bb-922a-a080179b03b6", "type": "one<PERSON><PERSON>", "x": 975.************, "y": 1173.************, "name": "coll-port-1-left", "alignment": "left", "parentNode": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "links": []}, {"id": "59edf89f-c6c2-4101-8c43-c590924f3d9e", "type": "one<PERSON><PERSON>", "x": 1149.0825751388802, "y": 1173.************, "name": "coll-port-1-right", "alignment": "right", "parentNode": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "links": ["a4737442-e39c-4e7b-8ac5-5fc211eff0ec", "f6349b9e-c7ce-47df-9cac-704f931b9335", "589d2e71-ff6b-41dc-9789-b377369773d4"]}, {"id": "60e548ca-466e-4abd-b09d-6ffcde345f87", "type": "one<PERSON><PERSON>", "x": 975.************, "y": 1340.************, "name": "coll-port-6-left", "alignment": "left", "parentNode": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "links": ["ddfd3e5e-1028-4216-9a1d-f06d40c28e97"]}, {"id": "91081dd8-2a09-4762-9b72-f2a2b7f2b760", "type": "one<PERSON><PERSON>", "x": 1149.0825751388802, "y": 1340.************, "name": "coll-port-6-right", "alignment": "right", "parentNode": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["2369c8ec-7229-42bb-922a-a080179b03b6", "59edf89f-c6c2-4101-8c43-c590924f3d9e", "60e548ca-466e-4abd-b09d-6ffcde345f87", "91081dd8-2a09-4762-9b72-f2a2b7f2b760"], "otherInfo": {"data": {"columns": [{"name": "Id", "atttypid": 25, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Chats", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "Title", "atttypid": 25, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Chats", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"]}, {"name": "Description", "atttypid": 25, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Chats", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text"]}, {"name": "CreatedAt", "atttypid": 1184, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "timestamp with time zone", "displaytypname": "timestamp with time zone", "cltype": "timestamp with time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1184, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Chats", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "information_schema.time_stamp", "time with time zone", "time without time zone", "timestamp with time zone", "timestamp with time zone", "timestamp without time zone"]}, {"name": "UpdatedAt", "atttypid": 1184, "attlen": null, "attnum": 5, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "timestamp with time zone", "displaytypname": "timestamp with time zone", "cltype": "timestamp with time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1184, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Chats", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "information_schema.time_stamp", "time with time zone", "time without time zone", "timestamp with time zone", "timestamp with time zone", "timestamp with time zone", "timestamp without time zone"]}, {"name": "OwnerId", "atttypid": 25, "attlen": null, "attnum": 6, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Chats", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text", "text"]}], "name": "Chats", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18412, "name": "PK_<PERSON><PERSON>", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "Id"}], "include": []}], "foreign_key": [{"name": "FK_Chats_AspNetUsers_OwnerId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "r", "confmatchtype": false, "conkey": [6], "confkey": [1], "confrelid": 18333, "fknsp": "public", "fktab": "Chats", "refnspoid": 2200, "refnsp": "public", "reftab": "AspNetUsers", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "OwnerId", "references": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced": "Id", "references_table_name": "public.AspNetUsers"}], "remote_schema": "public", "remote_table": "AspNetUsers", "coveringindex": "IX_Chats_OwnerId", "autoindex": true, "hasindex": true}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "5a741f44-4b56-415d-95a4-abf8c6cdc3d9": {"id": "5a741f44-4b56-415d-95a4-abf8c6cdc3d9", "type": "table", "selected": false, "x": 1260, "y": 660, "ports": [{"id": "c2ecd42f-3ac8-4380-a1d2-9dcb98403cc4", "type": "one<PERSON><PERSON>", "x": 1260.************, "y": 779.************, "name": "coll-port-2-left", "alignment": "left", "parentNode": "5a741f44-4b56-415d-95a4-abf8c6cdc3d9", "links": ["9e099b54-e3fb-4ff0-9381-b44774826f44"]}, {"id": "a20df952-90a9-417c-89cd-9b9d0e5bbb62", "type": "one<PERSON><PERSON>", "x": 1434.0739777550261, "y": 779.************, "name": "coll-port-2-right", "alignment": "right", "parentNode": "5a741f44-4b56-415d-95a4-abf8c6cdc3d9", "links": []}, {"id": "5beb5ea6-071b-4457-b26b-84e2751240c9", "type": "one<PERSON><PERSON>", "x": 1260.************, "y": 753.5951277286254, "name": "coll-port-1-left", "alignment": "left", "parentNode": "5a741f44-4b56-415d-95a4-abf8c6cdc3d9", "links": []}, {"id": "cbdd5914-e93d-4ba4-97e9-3d02f4421abd", "type": "one<PERSON><PERSON>", "x": 1434.0739777550261, "y": 753.5951277286254, "name": "coll-port-1-right", "alignment": "right", "parentNode": "5a741f44-4b56-415d-95a4-abf8c6cdc3d9", "links": ["44ff7bdb-fccf-41c0-ba1b-5f162d4183e3"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["c2ecd42f-3ac8-4380-a1d2-9dcb98403cc4", "a20df952-90a9-417c-89cd-9b9d0e5bbb62", "5beb5ea6-071b-4457-b26b-84e2751240c9", "cbdd5914-e93d-4ba4-97e9-3d02f4421abd"], "otherInfo": {"data": {"columns": [{"name": "MessageId", "atttypid": 25, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "MessageMentions", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "UserId", "atttypid": 25, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "MessageMentions", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"]}], "name": "MessageMentions", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18470, "name": "PK_MessageMentions", "col_count": 2, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "MessageId"}, {"column": "UserId"}], "include": []}], "foreign_key": [{"name": "FK_MessageMentions_AspNetUsers_UserId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "r", "confmatchtype": false, "conkey": [2], "confkey": [1], "confrelid": 18333, "fknsp": "public", "fktab": "MessageMentions", "refnspoid": 2200, "refnsp": "public", "reftab": "AspNetUsers", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "UserId", "references": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced": "Id", "references_table_name": "public.AspNetUsers"}], "remote_schema": "public", "remote_table": "AspNetUsers", "coveringindex": "IX_MessageMentions_UserId", "autoindex": true, "hasindex": true}, {"name": "FK_MessageMentions_Messages_MessageId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [1], "confkey": [1], "confrelid": 18448, "fknsp": "public", "fktab": "MessageMentions", "refnspoid": 2200, "refnsp": "public", "reftab": "Messages", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "MessageId", "references": "81950116-7e04-463c-8247-8699c1336edc", "referenced": "Id", "references_table_name": "public.Messages"}], "remote_schema": "public", "remote_table": "Messages", "coveringindex": null, "autoindex": false, "hasindex": false}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "81950116-7e04-463c-8247-8699c1336edc": {"id": "81950116-7e04-463c-8247-8699c1336edc", "type": "table", "selected": false, "x": 1260, "y": 810, "ports": [{"id": "45dd353b-0345-4d4f-a6cc-9ed860cf03eb", "type": "one<PERSON><PERSON>", "x": 1260.************, "y": 903.************, "name": "coll-port-1-left", "alignment": "left", "parentNode": "81950116-7e04-463c-8247-8699c1336edc", "links": []}, {"id": "a0259055-c778-402a-980f-b540bbe48e98", "type": "one<PERSON><PERSON>", "x": 1434.0739777550261, "y": 903.************, "name": "coll-port-1-right", "alignment": "right", "parentNode": "81950116-7e04-463c-8247-8699c1336edc", "links": ["44ff7bdb-fccf-41c0-ba1b-5f162d4183e3"]}, {"id": "40ace589-fb34-43bc-8dde-2917cff3038a", "type": "one<PERSON><PERSON>", "x": 1260.************, "y": 954.9999708362652, "name": "coll-port-3-left", "alignment": "left", "parentNode": "81950116-7e04-463c-8247-8699c1336edc", "links": ["5a6e47ab-2483-407b-8198-66390e8ecf27"]}, {"id": "692663bd-0b7f-4d0a-ae59-78d379faf17f", "type": "one<PERSON><PERSON>", "x": 1434.0739777550261, "y": 954.9999708362652, "name": "coll-port-3-right", "alignment": "right", "parentNode": "81950116-7e04-463c-8247-8699c1336edc", "links": []}, {"id": "7f3e2cf0-d41a-4d80-bc4e-9134531a80f7", "type": "one<PERSON><PERSON>", "x": 1260.************, "y": 929.2975939104656, "name": "coll-port-2-left", "alignment": "left", "parentNode": "81950116-7e04-463c-8247-8699c1336edc", "links": ["a4737442-e39c-4e7b-8ac5-5fc211eff0ec"]}, {"id": "296a9f2e-6207-4617-b7a6-c8e0b59dcdbe", "type": "one<PERSON><PERSON>", "x": 1434.0739777550261, "y": 929.2975939104656, "name": "coll-port-2-right", "alignment": "right", "parentNode": "81950116-7e04-463c-8247-8699c1336edc", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["45dd353b-0345-4d4f-a6cc-9ed860cf03eb", "a0259055-c778-402a-980f-b540bbe48e98", "40ace589-fb34-43bc-8dde-2917cff3038a", "692663bd-0b7f-4d0a-ae59-78d379faf17f", "7f3e2cf0-d41a-4d80-bc4e-9134531a80f7", "296a9f2e-6207-4617-b7a6-c8e0b59dcdbe"], "otherInfo": {"data": {"columns": [{"name": "Id", "atttypid": 25, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "ChatId", "atttypid": 25, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text"]}, {"name": "AuthorId", "atttypid": 25, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text"]}, {"name": "Content", "atttypid": 25, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text", "text", "text", "text"]}, {"name": "CreatedAt", "atttypid": 1184, "attlen": null, "attnum": 5, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "timestamp with time zone", "displaytypname": "timestamp with time zone", "cltype": "timestamp with time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1184, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "information_schema.time_stamp", "time with time zone", "time without time zone", "timestamp with time zone", "timestamp with time zone", "timestamp without time zone"]}, {"name": "UpdatedAt", "atttypid": 1184, "attlen": null, "attnum": 6, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "timestamp with time zone", "displaytypname": "timestamp with time zone", "cltype": "timestamp with time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1184, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "Messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "information_schema.time_stamp", "time with time zone", "time without time zone", "timestamp with time zone", "timestamp with time zone", "timestamp with time zone", "timestamp without time zone"]}], "name": "Messages", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18453, "name": "PK_Messages", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "Id"}], "include": []}], "foreign_key": [{"name": "FK_Messages_AspNetUsers_AuthorId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "r", "confmatchtype": false, "conkey": [3], "confkey": [1], "confrelid": 18333, "fknsp": "public", "fktab": "Messages", "refnspoid": 2200, "refnsp": "public", "reftab": "AspNetUsers", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "AuthorId", "references": "69b59a42-859f-403a-ae03-8a0b94443c1e", "referenced": "Id", "references_table_name": "public.AspNetUsers"}], "remote_schema": "public", "remote_table": "AspNetUsers", "coveringindex": "IX_Messages_AuthorId", "autoindex": true, "hasindex": true}, {"name": "FK_Messages_Chats_ChatId", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [2], "confkey": [1], "confrelid": 18407, "fknsp": "public", "fktab": "Messages", "refnspoid": 2200, "refnsp": "public", "reftab": "Chats", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "ChatId", "references": "1264f81d-1d8b-4f59-a5cd-c503bcb02581", "referenced": "Id", "references_table_name": "public.Chats"}], "remote_schema": "public", "remote_table": "Chats", "coveringindex": "IX_Messages_ChatId", "autoindex": true, "hasindex": true}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "db8c2b93-7940-4517-ad44-86dfaae34d32": {"id": "db8c2b93-7940-4517-ad44-86dfaae34d32", "type": "table", "selected": false, "x": -45, "y": 1230, "ports": [], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": [], "otherInfo": {"data": {"columns": [{"name": "MigrationId", "atttypid": 1043, "attlen": "150", "attnum": 1, "attndims": 0, "atttypmod": 154, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(150)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "__EFMigrationsHistory", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "ProductVersion", "atttypid": 1043, "attlen": "32", "attnum": 2, "attndims": 0, "atttypmod": 36, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(32)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "__EFMigrationsHistory", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}], "name": "__EFMigrationsHistory", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18324, "name": "PK___EFMigrationsHistory", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "MigrationId"}], "include": []}], "unique_constraint": [], "foreign_key": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}}}]}}