import React from 'react';
import { Box, useMediaQuery, useTheme } from '@mui/material';
import { Outlet } from '@tanstack/react-router';
import Header from './Header';
import { useAuth } from '../../contexts/AuthContext';

const Layout: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Show header only for authenticated users */}
      {!isLoading && isAuthenticated && <Header />}

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          ...(isAuthenticated && !isMobile
            ? { mt: '64px' } // Match header height
            : isAuthenticated && isMobile
            ? { mt: '56px' } // Mobile header height
            : {})
        }}
      >
        <Outlet />
      </Box>
    </Box>
  );
};

export default Layout;
