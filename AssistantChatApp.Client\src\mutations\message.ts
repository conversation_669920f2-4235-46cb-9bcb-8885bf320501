import { mutationOptions, useMutation, useQueryClient, type QueryClient } from "@tanstack/react-query";
import { chatApi } from "../api/chatApi";
import { messageQueryKeys } from "../queries/message";
import { Message, SendMessageRequest, SendMessageResponse } from "../types";


export interface SendMessageMutationOptions {
  onSuccess?: (response: SendMessageResponse) => void;
}

export const createSendMessageMutationOptions = (
  queryClient: QueryClient,
  onSuccess?: (response: SendMessageResponse) => void,
) => {
  return mutationOptions({
    mutationKey: ["sendMessage"],
    mutationFn: ({ chatId, textContent, mentions }: SendMessageRequest) =>
      chatApi.sendMessage(chatId, textContent, mentions),
    onSuccess: (rsp) => {
      queryClient.invalidateQueries({
        queryKey: messageQueryKeys.allChatMessages(rsp.originalMessage.chatId),
      });
      if (onSuccess) {
        onSuccess(rsp);
      }
    },
  })
}

export const useSendMessage = (
  onSuccess?: (response: SendMessageResponse) => void,
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ chatId, textContent, mentions }: SendMessageRequest) =>
      chatApi.sendMessage(chatId, textContent, mentions),
    onSuccess: (rsp) => {
      queryClient.invalidateQueries({
        queryKey: messageQueryKeys.allChatMessages(rsp.originalMessage.chatId),
      });
      if (onSuccess) {
        onSuccess(rsp);
      }
    },
  });
};

export const useUpdateMessage = (onSuccess?: (message: Message) => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      chatId,
      messageId,
      content,
      mentions,
    }: {
      chatId: string;
      messageId: string;
      content: string;
      mentions: string[];
    }) => chatApi.updateMessage(chatId, messageId, content, mentions),
    onSuccess: (message) => {
      queryClient.invalidateQueries({
        queryKey: messageQueryKeys.allChatMessages(message.chatId),
      });
      if (onSuccess) {
        onSuccess(message);
      }
    },
  });
};

export const useDeleteMessage = (
  onSuccess?: (chatId: string, messageId: string) => void,
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      chatId,
      messageId,
    }: {
      chatId: string;
      messageId: string;
    }) => chatApi.deleteMessage(chatId, messageId),
    onSuccess: (data, { chatId, messageId }) => {
      queryClient.invalidateQueries({
        queryKey: messageQueryKeys.allChatMessages(chatId),
      });
      if (onSuccess) {
        onSuccess(chatId, messageId);
      }
    },
  });
};
