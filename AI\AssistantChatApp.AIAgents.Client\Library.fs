namespace AssistantChatApp.AIAgents.Client

open System
open System.Net.Http
open System.Runtime.CompilerServices
open System.Threading.Tasks
open Microsoft.Extensions.DependencyInjection

open AssistantChatApp.AIAgents.Shared
open AssistantChatApp.AIAgents.Shared.Api
open Microsoft.Extensions.Hosting

module Http =

    open Microsoft.Extensions.Http.Resilience
    open Fable.Remoting.DotnetClient
    open Serilog.HttpClient.Extensions

    module ClientNames =

        let [<Literal>] AgentApi = "AgentApiClient"

    let createRemotingClientProxy<'Api> (clientFactory: IHttpClientFactory) clientName (baseUrl: string) =
        let client = clientFactory.CreateClient(clientName)
        Remoting.createApi baseUrl
        |> Remoting.withRouteBuilder Routing.apiRouteBuilder
        |> Remoting.withHttpClient client
        |> Remoting.buildProxy<'Api>

    let createAgentClientProxy clientFactory baseUrl =
        createRemotingClientProxy<AgentApi> clientFactory ClientNames.AgentApi baseUrl

    let addAgentHttpClient (host: IHostApplicationBuilder) =
        host.Services.AddHttpClient(ClientNames.AgentApi)
            .RemoveAllLoggers()
            .LogRequestResponse()
            .RemoveAllResilienceHandlers()
            .AddStandardResilienceHandler(fun options ->
                if host.Environment.IsDevelopment() then
                    options.Retry.Delay <- TimeSpan.FromSeconds 1
                    options.TotalRequestTimeout.Timeout <- TimeSpan.FromMinutes 20
                    options.CircuitBreaker.SamplingDuration <- TimeSpan.FromMinutes 20
                    options.AttemptTimeout.Timeout <- TimeSpan.FromMinutes 10
                else
                    // `CircuitBreaker.SamplingDuration` has to be at least 2 times greater than the `AttemptTimeout`
                    options.TotalRequestTimeout.Timeout <- TimeSpan.FromMinutes 4
                    options.AttemptTimeout.Timeout <- TimeSpan.FromMinutes 2
                    options.CircuitBreaker.SamplingDuration <- TimeSpan.FromMinutes 4
            )

module ChatAppRpc =

    open Fable.Remoting.Server
    open Fable.Remoting.AspNetCore
    open Microsoft.AspNetCore.Builder
    open Microsoft.Extensions.Logging
    open Microsoft.AspNetCore.Http

    let configure
        (loggerFactory: ILoggerFactory)
        (getApi: HttpContext -> ChatAppApi)
        =
        let logger = loggerFactory.CreateLogger<ChatAppApi>()
        Remoting.createApi()
        |> Remoting.withRouteBuilder Routing.apiRouteBuilder
        |> Remoting.withErrorHandler (fun error routeInfo -> ErrorResult.Ignore)
        #if DEBUG
        |> Remoting.withDiagnosticsLogger logger.LogDebug
        #endif
        |> Remoting.fromContext getApi

    let useRemoting getApi (app: IApplicationBuilder) =
        let loggerFactory = app.ApplicationServices.GetRequiredService<ILoggerFactory>()
        configure loggerFactory getApi
        |> app.UseRemoting

open Microsoft.AspNetCore.Http
open Microsoft.AspNetCore.Builder
open Microsoft.Extensions.DependencyInjection

type AgentClientProxy(clientFactory: IHttpClientFactory, baseUrl: string) =
    let api = Http.createAgentClientProxy clientFactory baseUrl
    member _.GetChatCompletion(agentUser, inquirerUser, messages: ChatMessage []) =
        api.GetChatCompletion agentUser inquirerUser messages
        |> Async.StartAsTask

type IHostApplicationBuilderExtensions =
    [<Extension>]
    static member AddAgentHttpClient(host: IHostApplicationBuilder) =
        Http.addAgentHttpClient host

    [<Extension>]
    static member AddAgentClientProxy(
        host: IHostApplicationBuilder,
        getBaseUrl: Func<IServiceProvider, string>
    ) =
        host.Services.AddSingleton<AgentClientProxy>(fun sp ->
            let clientFactory = sp.GetRequiredService<IHttpClientFactory>()
            let baseUrl = getBaseUrl.Invoke(sp)
            AgentClientProxy(clientFactory, baseUrl)
        )

type IApplicationBuilderExtensions =
    [<Extension>]
    static member UseChatAppRpc(
        app: IApplicationBuilder,
        sendMessagesAsync: Func<HttpContext, ChatMessage [], Task<bool []>>
    ) =
        let getApi ctx =
            {
                SendMessages = fun messages ->
                    sendMessagesAsync.Invoke(ctx, messages)
                    |> Async.AwaitTask
            }
        ChatAppRpc.useRemoting getApi app
        app


//type IHttpClientFactoryExtensions =
//    [<Extension>]
//    static member CreateAgentClientProxy(clientFactory: IHttpClientFactory, baseUrl: string) =
//        Http.createAgentClientProxy clientFactory baseUrl
