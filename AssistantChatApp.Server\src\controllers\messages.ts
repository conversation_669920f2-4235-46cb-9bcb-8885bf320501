import { Response } from 'express';
import { getMessages, sendMessage, updateMessage, deleteMessage } from '../services/messages';
import { AuthRequest } from '../types';
import { parsePaginationParams } from '../utils/pagination';

/**
 * Get messages controller
 * @route GET /chats/:chatId/messages
 */
export async function getMessagesController(req: AuthRequest, res: Response) {
  if (!req.userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }
  
  const { chatId } = req.params;
  const { page, limit } = parsePaginationParams(req.query);
  
  const result = await getMessages(chatId, req.userId, page, limit);
  
  res.status(200).json(result);
}

/**
 * Send message controller
 * @route POST /chats/:chatId/messages
 */
export async function sendMessageController(req: AuthRequest, res: Response) {
  if (!req.userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }
  
  const { chatId } = req.params;
  const { content, mentions } = req.body;
  
  const message = await sendMessage(
    chatId,
    req.userId,
    content,
    mentions || []
  );
  
  res.status(201).json(message);
}

/**
 * Update message controller
 * @route PATCH /chats/:chatId/messages/:messageId
 */
export async function updateMessageController(req: AuthRequest, res: Response) {
  if (!req.userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }
  
  const { chatId, messageId } = req.params;
  const { content, mentions } = req.body;
  
  const message = await updateMessage(
    chatId,
    messageId,
    req.userId,
    content,
    mentions || []
  );
  
  res.status(200).json(message);
}

/**
 * Delete message controller
 * @route DELETE /chats/:chatId/messages/:messageId
 */
export async function deleteMessageController(req: AuthRequest, res: Response) {
  if (!req.userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }
  
  const { chatId, messageId } = req.params;
  
  await deleteMessage(chatId, messageId, req.userId);
  
  res.status(204).send();
}
