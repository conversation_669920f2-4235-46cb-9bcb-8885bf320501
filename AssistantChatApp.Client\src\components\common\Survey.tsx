import React, { useState } from 'react';
import {
  Box,
  Typography,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormGroup,
  Checkbox,
  Button,
  Paper,
  Alert
} from '@mui/material';
import { useTranslation } from 'react-i18next';

export interface SurveyComponentProps {
  question: string;
  answers: Record<string, string>;
  multiple: boolean;
  showKeys: boolean;
  onSubmit: (answers: string[]) => void; // Submits keys of selected answers
}

export const SurveyComponent: React.FC<SurveyComponentProps> = ({
  question,
  answers,
  multiple,
  showKeys,
  onSubmit
}) => {
  const { t } = useTranslation();
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [showError, setShowError] = useState(false);

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedAnswers([event.target.value]);
    setShowError(false);
  };

  const handleCheckboxChange = (key: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelectedAnswers(prev => [...prev, key]);
    } else {
      setSelectedAnswers(prev => prev.filter(answer => answer !== key));
    }
    setShowError(false);
  };

  const handleSubmit = () => {
    if (selectedAnswers.length === 0) {
      setShowError(true);
      return;
    }
    onSubmit(selectedAnswers);
  };

  const formatAnswerText = (key: string, value: string) => {
    return showKeys ? `${key}: ${value}` : value;
  };

  const answersArray = Object.entries(answers);

  return (
    <Paper elevation={2} sx={{ p: 3, maxWidth: 600, margin: 'auto' }}>
      <Typography variant="h6" component="h2" gutterBottom>
        {question}
      </Typography>

      {showError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Please select at least one answer before submitting.
        </Alert>
      )}

      <FormControl component="fieldset" fullWidth>
        {multiple ? (
          <FormGroup>
            {answersArray.map(([key, value]) => (
              <FormControlLabel
                key={key}
                control={
                  <Checkbox
                    checked={selectedAnswers.includes(key)}
                    onChange={handleCheckboxChange(key)}
                    color="primary"
                  />
                }
                label={formatAnswerText(key, value)}
              />
            ))}
          </FormGroup>
        ) : (
          <RadioGroup
            value={selectedAnswers[0] || ''}
            onChange={handleRadioChange}
          >
            {answersArray.map(([key, value]) => (
              <FormControlLabel
                key={key}
                value={key}
                control={<Radio color="primary" />}
                label={formatAnswerText(key, value)}
              />
            ))}
          </RadioGroup>
        )}
      </FormControl>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSubmit}
          disabled={selectedAnswers.length === 0}
        >
          {t("submit")}
        </Button>
      </Box>
    </Paper>
  );
};
