import React, { Suspense } from "react";
import type { QueryClient } from "@tanstack/react-query";
import { createRootRoute, Link as TabStackLink, Outlet, createRootRouteWithContext } from "@tanstack/react-router";
import { Container, Stack, AppBar, Toolbar } from "@mui/material";
import { Link } from "../components/Link";
import { TanStackRouterDevtools } from "@tanstack/router-devtools";
import type { RouterContext } from "../types";

// const TanStackRouterDevtools =
//   process.env.NODE_ENV === 'production'
//     ? () => null // Render nothing in production
//     : React.lazy(() =>
//         // Lazy load in development
//         import('@tanstack/router-devtools').then((res) => ({
//           default: res.TanStackRouterDevtools,
//           // For Embedded Mode
//           // default: res.TanStackRouterDevtoolsPanel
//         })),
//       );

export const Route = createRootRouteWithContext<RouterContext>()({
    component: () => (
        <>
            <Stack direction="row" spacing={2}>
                <Link to="/">Home</Link>
                <Link to="/about">About</Link>
            </Stack>
            <hr />
            <Container maxWidth={false}>
                <Outlet />
            </Container>
            <Suspense>
                <TanStackRouterDevtools />
            </Suspense>
        </>
    ),
});
