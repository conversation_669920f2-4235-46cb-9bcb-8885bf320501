import React from 'react';
import { Rnd } from 'react-rnd';
import {
  Paper,
  Box,
  Collapse,
} from '@mui/material';
import { ArtifactViewerProps, DEFAULT_MIN_SIZE, DEFAULT_MAX_SIZE } from './types';
import { ArtifactViewerHeader, CompactArtifactViewerHeader } from './ArtifactViewerHeader';
import { useArtifactViewer, useResponsiveBreakpoints } from './hooks';
import {
  constrainPosition,
  constrainSize,
  getFullscreenSize,
  getFullscreenPosition,
  getResponsiveSize,
  getResponsivePosition,
} from './utils';

/**
 * Main ArtifactViewer component - a draggable and resizable modal for displaying artifacts
 */
export const ArtifactViewer: React.FC<ArtifactViewerProps> = ({
  artifactId,
  title,
  description,
  children,
  initialPosition,
  initialSize,
  onClose,
  onFocus,
  zIndex: customZIndex,
  autoFocus = false,
  resizable = true,
  draggable = true,
  minSize = DEFAULT_MIN_SIZE,
  maxSize = DEFAULT_MAX_SIZE,
  className,
}) => {
  const { isMobile } = useResponsiveBreakpoints();
  const rndRef = React.useRef<Rnd>(null);

  const {
    state,
    isOpen,
    close,
    focus,
    updateState,
    updatePosition,
    viewportSize,
  } = useArtifactViewer(artifactId, title, description, initialPosition, initialSize);

  // Handle focus when clicked
  const handleFocus = React.useCallback(() => {
    focus();
    onFocus?.();
  }, [focus, onFocus]);

  // Handle close
  const handleClose = React.useCallback(() => {
    close();
    onClose?.();
  }, [close, onClose]);

  // Auto-focus if requested
  React.useEffect(() => {
    if (autoFocus && isOpen) {
      handleFocus();
    }
  }, [autoFocus, isOpen, handleFocus]);

  // Handle escape key
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && state?.isFocused) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, state?.isFocused, handleClose]);

  // Handle viewport resize
  React.useEffect(() => {
    if (state && isOpen) {
      const handleResize = () => {
        if (state.isFullscreen) {
          const fullscreenSize = getFullscreenSize(viewportSize);
          const fullscreenPosition = getFullscreenPosition();
          updateState({
            size: fullscreenSize,
            position: fullscreenPosition,
          });
        } else {
          const responsiveSize = getResponsiveSize(state.size);
          const responsivePosition = getResponsivePosition(state.position, responsiveSize);
          updateState({
            size: responsiveSize,
            position: responsivePosition,
          });
        }
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [state, isOpen, viewportSize, updateState]);

  if (!state || !isOpen) {
    return null;
  }

  const effectiveZIndex = customZIndex || state.zIndex;
  const isEffectivelyDraggable = draggable && !isMobile && !state.isFullscreen;
  const isEffectivelyResizable = resizable && !isMobile && !state.isFullscreen;

  // Calculate fullscreen size and position
  const fullscreenSize = getFullscreenSize(viewportSize);
  const fullscreenPosition = getFullscreenPosition();

  const currentSize = state.isFullscreen ? fullscreenSize : state.size;
  const currentPosition = state.isFullscreen ? fullscreenPosition : state.position;

  return (
    <Rnd
      ref={rndRef}
      size={currentSize}
      position={currentPosition}
      onDragStop={(e, d) => {
        if (isEffectivelyDraggable) {
          updatePosition({ x: d.x, y: d.y });
        }
      }}
      onResizeStop={(e, direction, ref, delta, position) => {
        if (isEffectivelyResizable) {
          const newSize = {
            width: parseInt(ref.style.width, 10),
            height: parseInt(ref.style.height, 10),
          };
          const constrainedSize = constrainSize(newSize, viewportSize, minSize, maxSize);
          const constrainedPosition = constrainPosition(position, constrainedSize, viewportSize);

          updateState({
            size: constrainedSize,
            position: constrainedPosition,
          });
        }
      }}
      disableDragging={!isEffectivelyDraggable}
      enableResizing={isEffectivelyResizable ? {
        top: true,
        right: true,
        bottom: true,
        left: true,
        topRight: true,
        bottomRight: true,
        bottomLeft: true,
        topLeft: true,
      } : false}
      dragHandleClassName="artifact-viewer-header"
      minWidth={minSize.width}
      minHeight={minSize.height}
      maxWidth={maxSize.width}
      maxHeight={maxSize.height}
      bounds="window"
      style={{
        zIndex: effectiveZIndex,
      }}
      className={className}
    >
      <Paper
        elevation={state.isFocused ? 8 : 4}
        onClick={handleFocus}
        sx={theme => ({
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          backgroundColor: 'background.paper',
          border: state.isFocused ? 2 : 1,
          borderColor: state.isFocused ? 'primary.main' : 'divider',
          borderRadius: state.isFullscreen ? 0 : 2,
          transition: theme.transitions.create([
            'box-shadow',
            'border-color',
            'border-width',
          ], {
            duration: theme.transitions.duration.short,
          }),
        })}
      >
        {/* Header */}
        {isMobile ? (
          <CompactArtifactViewerHeader
            state={state}
            onStateUpdate={updateState}
            onClose={handleClose}
          />
        ) : (
          <ArtifactViewerHeader
            state={state}
            onStateUpdate={updateState}
            onClose={handleClose}
            showSizeSwitcher={!state.isFullscreen}
          />
        )}

        {/* Content */}
        <Collapse in={!state.isCollapsed} timeout="auto">
          <Box
            sx={{
              flex: 1,
              overflow: 'auto',
              p: 2,
              backgroundColor: 'background.default',
            }}
          >
            {children}
          </Box>
        </Collapse>
      </Paper>
    </Rnd>
  );
};

export default ArtifactViewer;
