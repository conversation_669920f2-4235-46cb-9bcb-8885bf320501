import React from 'react';
import {
  <PERSON>,
  Typography,
  <PERSON>con<PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Toolt<PERSON>,
  Divider,
} from '@mui/material';
import {
  Close as CloseIcon,
  Minimize as CollapseIcon,
  Maximize as ExpandIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  DragIndicator as DragIcon,
} from '@mui/icons-material';
import { ArtifactViewerHeaderBaseProps, ArtifactViewerHeaderProps, DEFAULT_SIZES } from './types';
import { SizeSwitcher } from './SizeSwitcher';

/**
 * Header component for the ArtifactViewer modal
 * Contains title, controls, and optional size switcher
 */
export const ArtifactViewerHeader: React.FC<ArtifactViewerHeaderProps> = ({
  state,
  onStateUpdate,
  onClose,
  showSizeSwitcher = true,
  availableSizes = DEFAULT_SIZES,
}) => {
  const handleToggleCollapse = () => {
    onStateUpdate({ isCollapsed: !state.isCollapsed });
  };

  const handleToggleFullscreen = () => {
    onStateUpdate({ isFullscreen: !state.isFullscreen });
  };

  const handleSizeChange = (newSize: { width: number; height: number }) => {
    onStateUpdate({
      size: newSize,
      isFullscreen: false, // Exit fullscreen when changing size
    });
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        px: 2,
        py: 1,
        backgroundColor: 'background.paper',
        borderBottom: state.isCollapsed ? 'none' : '1px solid',
        borderColor: 'divider',
        cursor: 'move', // Indicates draggable area
        userSelect: 'none',
        minHeight: 48,
      }}
      className="artifact-viewer-header"
    >
      {/* Left side - Drag handle and title */}
      <Stack direction="row" alignItems="center" spacing={1} sx={{ flex: 1, minWidth: 0 }}>
        <DragIcon
          sx={{
            color: 'text.disabled',
            fontSize: 16,
            cursor: 'move',
          }}
        />

        <Box sx={{ minWidth: 0, flex: 1 }}>
          <Typography
            variant="subtitle2"
            component="h2"
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {state.title}
          </Typography>

          {state.description && !state.isCollapsed && (
            <Typography
              variant="caption"
              sx={{
                color: 'text.secondary',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                display: 'block',
                lineHeight: 1.2,
              }}
            >
              {state.description}
            </Typography>
          )}
        </Box>
      </Stack>

      {/* Right side - Controls */}
      <Stack direction="row" alignItems="center" spacing={0.5}>
        {/* Size switcher */}
        {showSizeSwitcher && !state.isFullscreen && (
          <>
            <SizeSwitcher
              currentSize={state.size}
              availableSizes={availableSizes}
              onSizeChange={handleSizeChange}
              size="small"
            />
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5, height: 20 }} />
          </>
        )}

        {/* Collapse/Expand button */}
        <Tooltip title={state.isCollapsed ? 'Expand' : 'Collapse'} placement="bottom">
          <IconButton
            onClick={handleToggleCollapse}
            size="small"
            sx={{
              color: 'text.secondary',
              '&:hover': {
                color: 'text.primary',
                backgroundColor: 'action.hover',
              },
            }}
          >
            {state.isCollapsed ? <ExpandIcon fontSize="small" /> : <CollapseIcon fontSize="small" />}
          </IconButton>
        </Tooltip>

        {/* Fullscreen button */}
        <Tooltip title={state.isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'} placement="bottom">
          <IconButton
            onClick={handleToggleFullscreen}
            size="small"
            sx={{
              color: 'text.secondary',
              '&:hover': {
                color: 'text.primary',
                backgroundColor: 'action.hover',
              },
            }}
          >
            {state.isFullscreen ? (
              <FullscreenExitIcon fontSize="small" />
            ) : (
              <FullscreenIcon fontSize="small" />
            )}
          </IconButton>
        </Tooltip>

        {/* Close button */}
        <Tooltip title="Close" placement="bottom">
          <IconButton
            onClick={onClose}
            size="small"
            sx={{
              color: 'text.secondary',
              '&:hover': {
                color: 'error.main',
                backgroundColor: 'error.50',
              },
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Stack>
    </Box>
  );
};

/**
 * Compact header variant for mobile or small screens
 */
export const CompactArtifactViewerHeader: React.FC<ArtifactViewerHeaderBaseProps> = ({
  state,
  onStateUpdate,
  onClose,
}) => {
  const handleToggleCollapse = () => {
    onStateUpdate({ isCollapsed: !state.isCollapsed });
  };

  const handleToggleFullscreen = () => {
    onStateUpdate({ isFullscreen: !state.isFullscreen });
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        px: 1.5,
        py: 0.5,
        backgroundColor: 'background.paper',
        borderBottom: state.isCollapsed ? 'none' : '1px solid',
        borderColor: 'divider',
        userSelect: 'none',
        minHeight: 40,
      }}
      className="artifact-viewer-header-compact"
    >
      {/* Title */}
      <Box sx={{ minWidth: 0, flex: 1 }}>
        <Typography
          variant="body2"
          component="h2"
          sx={{
            fontWeight: 600,
            color: 'text.primary',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {state.title}
        </Typography>
      </Box>

      {/* Controls */}
      <Stack direction="row" alignItems="center" spacing={0.5}>
        {/* Collapse/Expand button */}
        <IconButton
          onClick={handleToggleCollapse}
          size="small"
          sx={{ color: 'text.secondary' }}
        >
          {state.isCollapsed ? <ExpandIcon fontSize="small" /> : <CollapseIcon fontSize="small" />}
        </IconButton>

        {/* Fullscreen button */}
        <IconButton
          onClick={handleToggleFullscreen}
          size="small"
          sx={{ color: 'text.secondary' }}
        >
          {state.isFullscreen ? (
            <FullscreenExitIcon fontSize="small" />
          ) : (
            <FullscreenIcon fontSize="small" />
          )}
        </IconButton>

        {/* Close button */}
        <IconButton
          onClick={onClose}
          size="small"
          sx={{
            color: 'text.secondary',
            '&:hover': { color: 'error.main' },
          }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      </Stack>
    </Box>
  );
};

export default ArtifactViewerHeader;
