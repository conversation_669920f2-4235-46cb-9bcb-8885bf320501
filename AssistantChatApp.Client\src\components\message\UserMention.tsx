import React from "react";
import {
    <PERSON><PERSON>,
    Box,
    Card,
    CardContent,
    Chip,
    Fade,
    Paper,
    Popover,
    Popper,
    Skeleton,
    Stack,
    Typography,
} from "@mui/material";
import { useSuspenseQuery } from "@tanstack/react-query";
import { userApi } from "../../api/userApi";
import { User } from "../../types";
import { useTranslation } from "react-i18next";
import {
  formatAsDateTimeUpToMinutes,
  i18nLocaleToDateFnsLocale
} from "../common/dateTimeUtils";

interface UserMentionProps {
    userId: string;
}

interface UserSummaryProps {
    user: User;
}

interface UserPopupProps extends UserSummaryProps {
    isOpen: boolean;
    onClose: () => void;
    anchorEl: HTMLElement | null; // Trigger element of the popup
}

interface UserMentionWithPopupProps extends UserPopupProps {
    onOpen: (event: React.MouseEvent<HTMLElement>) => void;
}

const JoinedAtCaption: React.FC<{ dateTime: string }> = ({ dateTime }) => {
  const { t, i18n } = useTranslation(["common", "users"]);
  const dateFnsLocale = i18nLocaleToDateFnsLocale(i18n.language);
  const dateStr = formatAsDateTimeUpToMinutes(new Date(dateTime), dateFnsLocale);
  const caption = t("users:joinedAt") + " " + dateStr;

  return (
    <Typography variant="caption" color="text.secondary">
      {caption}
    </Typography>
  )
}

export const UserSummaryCard1: React.FC<UserSummaryProps> = ({ user }) => {
    const { t } = useTranslation("users");
    const userName = user.displayName;

    return (
        <Paper sx={{ p: 2 }}>
            {user ? (
                <>
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 1.5,
                        }}
                    >
                        <Avatar
                            src={user.avatar}
                            alt={userName}
                            sx={{ width: 40, height: 40, mr: 1.5 }}
                        />
                        <Stack
                            direction="column"
                            spacing={0.5}
                            sx={{ maxWidth: 240 }}
                            alignItems="flex-start"
                        >
                            <Typography variant="subtitle2">
                                {userName}
                            </Typography>
                            <Typography
                                variant="caption"
                                color="text.secondary"
                                noWrap
                                overflow="clip"
                                sx={{ maxWidth: "100%" }}
                            >
                                {user.id}
                            </Typography>
                            {user.isAI && (
                                <Chip
                                    label={t("ai")}
                                    size="small"
                                    color="primary"
                                />
                            )}
                        </Stack>
                    </Box>

                    <JoinedAtCaption dateTime={user.createdAt} />
                </>
            ) : (
                <Typography color="error">{t("user_not_found")}</Typography>
            )}
        </Paper>
    );
};

export const UserSummaryCard2: React.FC<
    UserSummaryProps & { cardRef?: React.RefObject<HTMLDivElement> }
> = ({ user, cardRef }) => {
    const { t } = useTranslation("users");

    return (
        <Card
            ref={cardRef}
            sx={(theme) => ({
                maxWidth: 400,
                width: "100%",
                borderRadius: 2,
                boxShadow: 6,
                border: "1px solid",
                borderColor: theme.palette.divider,
                cursor: "default",
                "&:focus": {
                    outline: "none",
                },
                [theme.breakpoints.down("sm")]: {
                    maxWidth: "90%",
                    width: "90%",
                },
            })}
        >
            <CardContent>
                <Stack direction="row" alignItems="center" spacing={2} mb={2}>
                    <Avatar
                        alt={user.displayName}
                        src={user.avatar}
                        sx={{ width: 40, height: 40 }}
                    />
                    <Stack
                        direction="row"
                        spacing={1}
                        sx={{ alignItems: "center" }}
                    >
                        <Typography variant="h6" component="span">
                            {user.displayName}
                        </Typography>
                        {user.isAI && (
                            <Chip
                                label={t("ai")}
                                size="small"
                                color="primary"
                                clickable={false}
                            />
                        )}
                    </Stack>
                </Stack>

                <JoinedAtCaption dateTime={user.createdAt} />
            </CardContent>
        </Card>
    );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const UserPopperPopup: React.FC<UserPopupProps> = ({
    user,
    isOpen,
    onClose,
    anchorEl,
}) => {
    // const theme = useTheme();
    const cardRef = React.useRef<HTMLDivElement>(null);
    const popperRef = React.useRef(null);

    // Close popup when clicking outside the card or on the backdrop
    const handleClose = (event: React.MouseEvent<EventTarget>) => {
        if (
            cardRef.current &&
            cardRef.current.contains(event.target as HTMLElement)
        ) {
            return;
        }
        onClose();
    };

    // Optional: Add a backdrop by wrapping the Popper inside a div
    // This gives a subtle visual effect without overusing Modal
    React.useEffect(() => {
        if (isOpen && anchorEl) {
            // Focus to the first focusable element in the popup
            const focusableElements = cardRef.current?.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
            );
            if (focusableElements && focusableElements.length > 0) {
                (focusableElements[0] as HTMLElement).focus();
            }
        }
    }, [isOpen, anchorEl]);

    return (
        <Popper
            open={isOpen}
            anchorEl={anchorEl}
            placement="bottom-end"
            modifiers={[
                {
                    name: "preventOverflow",
                    options: { enabled: true },
                },
                {
                    name: "flip",
                    options: { enabled: true },
                },
            ]}
            style={{ zIndex: 1300 }} // Ensure proper z-index for visibility on top of chat
        >
            <div ref={popperRef} onClick={handleClose}>
                <UserSummaryCard2 user={user} cardRef={cardRef} />
            </div>
        </Popper>
    );
};

export const UserPopoverPopup: React.FC<UserPopupProps> = ({
    user,
    isOpen,
    onClose,
    anchorEl,
}) => {
    return (
        <Popover
            open={isOpen}
            anchorEl={anchorEl}
            onClose={onClose}
            anchorOrigin={{
                vertical: "bottom",
                horizontal: "center",
            }}
            transformOrigin={{
                vertical: "top",
                horizontal: "center",
            }}
            disableRestoreFocus
            TransitionComponent={Fade}
            transitionDuration={200}
            sx={{ pointerEvents: "none" }}
        >
            <UserSummaryCard1 user={user} />
        </Popover>
    );
};

export const UserMentionWithPopup: React.FC<UserMentionWithPopupProps> = ({
    user,
    isOpen,
    onOpen,
    onClose,
    anchorEl,
}) => (
    <>
        <Box
            component="span"
            onMouseEnter={onOpen}
            onMouseLeave={onClose}
            onClick={onOpen}
            sx={{
                display: "inline-flex",
                alignItems: "center",
                bgcolor: "rgba(58, 123, 213, 0.1)",
                borderRadius: "4px",
                px: 0.5,
                py: 0.2,
                mx: 0.5,
                cursor: "pointer",
                color: "primary.main",
                fontWeight: "medium",
                "&:hover": {
                    bgcolor: "rgba(58, 123, 213, 0.2)",
                },
            }}
        >
            @{user.displayName}
        </Box>
        {user && (
            // <UserPopperPopup
            <UserPopoverPopup
                user={user}
                isOpen={isOpen}
                onClose={onClose}
                anchorEl={anchorEl}
            />
        )}
    </>
);

export const UserMention: React.FC<UserMentionProps> = ({ userId }) => {
    const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);

    const { data: user } = useSuspenseQuery({
        queryKey: ["user", userId],
        queryFn: () => userApi.getUser(userId),
    });

    const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handlePopoverClose = () => {
        setAnchorEl(null);
    };

    const open = Boolean(anchorEl);

    return (
        <React.Suspense fallback={<Skeleton variant="text" width={100} />}>
            <UserMentionWithPopup
                user={user}
                isOpen={open}
                onOpen={handlePopoverOpen}
                onClose={handlePopoverClose}
                anchorEl={anchorEl}
            />
        </React.Suspense>
    );
};
