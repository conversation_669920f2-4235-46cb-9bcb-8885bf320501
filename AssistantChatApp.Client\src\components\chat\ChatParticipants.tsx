import React from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Divider,
  Chip,
} from '@mui/material';
import { useTranslation } from "react-i18next";
import { User } from '../../types';

interface ChatParticipantsProps {
  participants: User[];
}

const ChatParticipants: React.FC<ChatParticipantsProps> = ({ participants }) => {
  const { t } = useTranslation(["chats", "users"]);
  // Separate AI assistants from human users
  const assistants = participants.filter(p => p.isAI);
  const humans = participants.filter(p => !p.isAI);

  return (
    <Box sx={{ height: '100%', overflowY: 'auto', borderLeft: '1px solid', borderColor: 'divider' }}>
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          {t("participants")}
        </Typography>

        {assistants.length > 0 && (
          <>
            <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
              {t("users:aiAssistants")}
            </Typography>
            <List disablePadding>
              {assistants.map((assistant) => (
                <ListItem key={assistant.id} disablePadding sx={{ mb: 2 }}>
                  <ListItemAvatar>
                    <Avatar
                      alt={assistant.displayName}
                      src={assistant.avatar}
                      sx={{ bgcolor: 'primary.main' }}
                    />
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="body1" fontWeight="medium">
                          {assistant.displayName}
                        </Typography>
                        <Chip
                          label={t("users:ai")}
                          size="small"
                          color="primary"
                          sx={{ ml: 1, height: 20, fontSize: '0.625rem', fontWeight: 600 }}
                        />
                      </Box>
                    }
                    secondary={
                      <Typography variant="body2" color="text.secondary">
                        {t("users:aiAssistant")}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
            <Divider />
          </>
        )}

        <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
          {t("users:people")}
        </Typography>
        <List disablePadding>
          {humans.map((human) => (
            <ListItem key={human.id} disablePadding sx={{ mb: 2 }}>
              <ListItemAvatar>
                <Avatar
                  alt={human.displayName}
                  src={human.avatar}
                />
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography variant="body1" fontWeight="medium">
                    {human.displayName}
                  </Typography>
                }
                // secondary={
                //   <Typography variant="body2" color="text.secondary">
                //     {/* This could show user's status, role, etc. */}
                //     {t("users:active")}
                //   </Typography>
                // }
              />
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );
};

export default ChatParticipants;
