# Application Overview

Core Architecture:
- React-based SPA using TypeScript
- Uses Vite as the build tool
- Implements `@tanstack/react-router` for routing
- Uses `@tanstack/react-query` for data fetching
- MUI, aka Material-UI (`@mui/material`) for UI components
- Authentication system with protected routes

Main Features:

1. Authentication System
- Login/logout functionality
- Protected route wrapper
- Auth state management via Context API
- Token-based authentication stored in localStorage

2. Chat System
- Chat list view with filtering and search
- Individual chat view with:
  - Real-time messaging
  - Participant management
  - Resizable panels layout
  - Markdown message support
  - @mentions functionality
  - AI assistant integration

3. UI/UX
- Responsive design with mobile support
- Custom theme implementation
- Drawer navigation for mobile
- Avatar groups for chat participants
- Rich text editor for messages
- Loading states and error handling

Key Components Structure:
```
src/
├── components/
│   ├── chat/          # Chat-related components
│   ├── layout/        # Layout components (Header, Layout)
│   ├── message/       # Message-related components
│   ├── mdx/           # MDX-related components
│   └── common/        # Shared components
├── contexts/
│   └── AuthContext    # Authentication state management
├── api/
│   ├── apiClient      # API communication layer
│   └── mockApi        # Mock data implementation
├── types/             # TypeScript types
├── queries/           # React Query data fetching
├── mutations/         # React Query mutations
├── pages/             # Main route components
└── routes/            # Router configuration
```

Data Flow:
- Uses React Query for server state management
- Context API for auth state
- Mock API implementation for development
- Typed interfaces for data structures

Notable Technical Choices:
- Modern React patterns (hooks, context)
- Strong TypeScript integration
- Component-based architecture
- Material-UI theming system
- Markdown support for messages
- Resizable panels for layout flexibility

The application appears to be a modern chat platform with AI assistant integration, following current best practices in React development.
