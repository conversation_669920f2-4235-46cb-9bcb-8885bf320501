module Enixar.KernelMemory.Client.Import.Json

open System.IO
open Thoth.Json.Core
open Thoth.Json.System.Text.Json

module Decode =
    
    let tags: Decoder<list<string * string>> =
        Decode.list (
            Decode.keyValuePairs Decode.string
            |> Decode.andThen (function
                | [k, v] -> Decode.succeed (k, v)
                | _ -> Decode.fail "Singleton object expected"
            )
        )
    
    let documentImportEntry: Decoder<DocumentImportEntry> =
        Decode.object (fun get -> {
            Id = get.Required.Field "id" Decode.string
            FilePath = get.Required.Field "filePath" Decode.string
            Index = get.Optional.Field "index" Decode.string
            Tags = get.Optional.Field "tags" tags
        })

    let documentGroupImportEntry: Decoder<DocumentGroupImportEntry> =
        Decode.object (fun get -> {
            Documents =
                get.Required.Field "documents" (Decode.map' Decode.string Decode.string)
            Index = get.Optional.Field "index" Decode.string
            Tags = get.Optional.Field "tags" tags
        })

    let importEntry: Decoder<ImportEntry> =
        Decode.oneOf [
            Decode.map DocumentImportEntry documentImportEntry
            Decode.map DocumentGroupImportEntry documentGroupImportEntry
        ]

    let importEntryList: Decoder<ImportEntry list> =
        importEntry |> Decode.list

let readImportJsonFile (jsonFilePath: string) =
    File.ReadAllText(jsonFilePath)
    |> Decode.fromString Decode.importEntryList
