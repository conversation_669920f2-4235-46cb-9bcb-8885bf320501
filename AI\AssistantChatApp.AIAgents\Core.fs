module AssistantChatApp.AIAgents.Core

open System
open System.Net.Http
open Microsoft.Extensions.Logging
open Microsoft.Extensions.DependencyInjection
open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.ChatCompletion
open AssistantChatApp.AIAgents.Shared
open AssistantChatApp.AIAgents.Shared.Dotnet


module Http =

    let getApiProviderHttpClientName (providerId: ApiProviderId) =
        providerId + "_HttpClient"

    let getApiProviderHttpClient (providerId: ApiProviderId) (httpClientFactory: IHttpClientFactory) =
        getApiProviderHttpClientName providerId
        |> httpClientFactory.CreateClient

    let configureApiProviderHttpClient
        (providerId: ApiProviderId)
        config
        (services: IServiceCollection)
        =
        let name = getApiProviderHttpClientName providerId
        Http.configureHttpClient name config services

    /// Reads the "ApiProviders" section of the configuration and
    /// configures named Http<PERSON>lient for each ApiProvider using the provider's ID.
    let configureHttpClientForApiProviders config (logger: ILogger) (services: IServiceCollection) =
        let apiProviders =
            match Configuration.getApiProviders config with
            | null ->
                logger.LogError("No ApiProviders found in configuration")
                dict []
            | providers -> providers

        for KeyValue(providerId, _) in apiProviders do
            configureApiProviderHttpClient providerId config services |> ignore
        services


module KernelBuilder =

    let addCompletionServices
        (settings: seq<AgentFullSettings>)
        (httpClientFactory: IHttpClientFactory)
        (builder: IKernelBuilder)
        =
        for s in settings do
            let httpClient = Http.getApiProviderHttpClient s.ApiProviderId httpClientFactory
            builder.AddOpenAIChatCompletion(
                s.Agent.ModelId,
                Uri s.ApiProvider.ApiEndpoint,
                apiKey = s.ApiProvider.ApiKey,
                httpClient = httpClient,
                serviceId = s.AgentId
            ) |> ignore
        builder


module FunctionCall =

    open System.Text.Json

    type FunctionCallAlike<'T when
        'T : (member PluginName: string) and
        'T : (member FunctionName: string)
    > = 'T

    let toFunctionCallContent (fcall: FunctionCall) =
        let args = JsonSerializer.Deserialize<KernelArguments>(fcall.Arguments.Value)
        FunctionCallContent(fcall.FunctionName, fcall.PluginName, fcall.CallId, args)

    let inline private ofCallContentAlike
        (content: FunctionCallAlike<'T>)
        (callId: string)
        (arguments: KernelArguments)
        =
        let argsJson = JsonSerializer.Serialize(arguments)
        {
            CallId = callId
            PluginName = content.PluginName
            FunctionName = content.FunctionName
            Arguments = JsonText argsJson
        }

    let ofFunctionCallContent (content: FunctionCallContent) =
        ofCallContentAlike content content.Id content.Arguments

module FunctionCallResult =

    let toFunctionResultContent (fresult: Shared.FunctionCallResult) =
        let resultValue = fresult.Result.Value
        FunctionResultContent(fresult.FunctionName, fresult.PluginName, fresult.CallId, resultValue)

    let ofFunctionResultContent (content: FunctionResultContent) =
        {
            CallId = content.CallId
            PluginName = content.PluginName
            FunctionName = content.FunctionName
            Result = Serialization.Json.serialize content.Result |> JsonText
        }


module FunctionCallContent =

    open System.Text.Json

    let (|OpenAIToolContentJson|_|) (call: FunctionCallContent) =
        match call.InnerContent with
        | :? OpenAI.Chat.ChatToolCall as toolCall ->
            {|
                CallId = toolCall.Id
                Name = toolCall.FunctionName
                Arguments = toolCall.FunctionArguments.ToObjectFromJson<JsonElement>()
            |}
            |> Serialization.Json.serialize |> Some
        | _ -> None

module FunctionResultContent =

    let getOpenAIToolResultJson (result: FunctionResultContent) =
        {|
            CallId = result.CallId
            FunctionName = result.PluginName + "-" + result.FunctionName
            Result = result.Result
        |}
        |> Serialization.Json.serialize


module ChatMessage =

    type SkAuthorRole = Microsoft.SemanticKernel.ChatCompletion.AuthorRole
    type SkTextContent = Microsoft.SemanticKernel.TextContent

    let toChatMessageContent (m: ChatMessage) =
        let role =
            match m.AuthorRole with
            | AuthorRole.Assistant -> SkAuthorRole.Assistant
            | AuthorRole.User -> SkAuthorRole.User
            | AuthorRole.System -> SkAuthorRole.System
            | AuthorRole.Tool -> SkAuthorRole.Tool
        let contentItems =
            let c = ChatMessageContentItemCollection()
            m.Content
            |> Array.iter (function
                | TextContent text ->
                    c.Add (SkTextContent text)
                | FunctionCall fcall ->
                    c.Add (FunctionCall.toFunctionCallContent fcall)
                | FunctionResult fresult ->
                    c.Add (FunctionCallResult.toFunctionResultContent fresult)
            )
            c
        // Prepend author name to user messages
        let content =
            m.GetMergedTextContent()
            |> match m.AuthorRole with
                | AuthorRole.User ->
                    ChatMessage.prependWithAuthorName m.AuthorName
                | _ -> id
        ChatMessageContent(
            role,
            content,
            AuthorName = m.AuthorName,
            Items = contentItems
        )

    let ofChatMessageContent (m: ChatMessageContent): ChatMessage =
        let role, authorName =
            match m.Role with
            | r when r = SkAuthorRole.Assistant -> AuthorRole.Assistant, m.ModelId
            | r when r = SkAuthorRole.User -> AuthorRole.User, "User"
            | r when r = SkAuthorRole.System -> AuthorRole.System, "System"
            | r when r = SkAuthorRole.Tool -> AuthorRole.Tool, "Tool"
            | _ -> AuthorRole.User, ""
        let contentItems = [|
            for content in m.Items do
                match content with
                // Skip tool's text content, so that no repeated content is returned (tool result data should be contained in `FunctionResultContent` items)
                | :? TextContent when m.Role = SkAuthorRole.Tool -> ()
                | :? TextContent as text ->
                    yield MessageContent.TextContent text.Text
                | :? FunctionCallContent as funCall ->
                    yield
                        FunctionCall.ofFunctionCallContent funCall
                        |> MessageContent.FunctionCall
                | :? FunctionResultContent as funResult ->
                    yield
                        FunctionCallResult.ofFunctionResultContent funResult
                        |> MessageContent.FunctionResult
                | _ -> ()
        |]

        {
            AuthorRole = role
            Content = contentItems
            AuthorName = authorName
        }
