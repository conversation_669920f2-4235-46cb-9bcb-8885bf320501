# KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**answerQuestion**](#answerquestion) | **POST** /ask | Use the memories extracted from the files uploaded to generate an answer. The query can include filters to use only a subset of the memories available.|
|[**checkDocumentStatus**](#checkdocumentstatus) | **GET** /upload-status | Check the status of a file upload in progress. When uploading a document, which can consist of multiple files, each file goes through multiple steps. The status include details about which steps are completed.|
|[**deleteDocumentById**](#deletedocumentbyid) | **DELETE** /documents | Delete a document from the knowledge base. When deleting a document, which can consist of multiple files, all the memories previously extracted are deleted too.|
|[**deleteIndexByName**](#deleteindexbyname) | **DELETE** /indexes | Delete a container of documents (aka \&#39;index\&#39;) from the knowledge base. Indexes are collections of memories extracted from the documents uploaded.|
|[**listIndexes**](#listindexes) | **GET** /indexes | Get the list of containers (aka \&#39;indexes\&#39;) from the knowledge base. Each index has a unique name. Indexes are collections of memories extracted from the documents uploaded.|
|[**searchDocumentSnippets**](#searchdocumentsnippets) | **POST** /search | Search the knowledge base for relevant snippets of text. The search can include filters to use only a subset of the knowledge base.|
|[**uploadDocument**](#uploaddocument) | **POST** /upload | Upload a new document to the knowledge base|

# **answerQuestion**
> MemoryAnswer answerQuestion(memoryQuery)

Answer a user question using the internal knowledge base.

### Example

```typescript
import {
    KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi,
    Configuration,
    MemoryQuery
} from './api';

const configuration = new Configuration();
const apiInstance = new KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi(configuration);

let memoryQuery: MemoryQuery; //

const { status, data } = await apiInstance.answerQuestion(
    memoryQuery
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **memoryQuery** | **MemoryQuery**|  | |


### Return type

**MemoryAnswer**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**401** | Unauthorized |  -  |
|**403** | Forbidden |  -  |
|**503** | Service Unavailable |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **checkDocumentStatus**
> DataPipelineStatus checkDocumentStatus()

Check the status of a file upload in progress.

### Example

```typescript
import {
    KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi(configuration);

let documentId: string; // (default to undefined)
let index: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.checkDocumentStatus(
    documentId,
    index
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **documentId** | [**string**] |  | defaults to undefined|
| **index** | [**string**] |  | (optional) defaults to undefined|


### Return type

**DataPipelineStatus**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**400** | Bad Request |  -  |
|**401** | Unauthorized |  -  |
|**403** | Forbidden |  -  |
|**404** | Not Found |  -  |
|**413** | Content Too Large |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteDocumentById**
> DeleteAccepted deleteDocumentById()

Delete a document from the knowledge base.

### Example

```typescript
import {
    KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi(configuration);

let documentId: string; // (default to undefined)
let index: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.deleteDocumentById(
    documentId,
    index
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **documentId** | [**string**] |  | defaults to undefined|
| **index** | [**string**] |  | (optional) defaults to undefined|


### Return type

**DeleteAccepted**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**202** | Accepted |  -  |
|**401** | Unauthorized |  -  |
|**403** | Forbidden |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteIndexByName**
> DeleteAccepted deleteIndexByName()

Delete a container of documents (aka \'index\') from the knowledge base.

### Example

```typescript
import {
    KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi(configuration);

let index: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.deleteIndexByName(
    index
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **index** | [**string**] |  | (optional) defaults to undefined|


### Return type

**DeleteAccepted**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**202** | Accepted |  -  |
|**401** | Unauthorized |  -  |
|**403** | Forbidden |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **listIndexes**
> IndexCollection listIndexes()

Get the list of containers (aka \'indexes\') from the knowledge base.

### Example

```typescript
import {
    KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi(configuration);

const { status, data } = await apiInstance.listIndexes();
```

### Parameters
This endpoint does not have any parameters.


### Return type

**IndexCollection**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**401** | Unauthorized |  -  |
|**403** | Forbidden |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchDocumentSnippets**
> SearchResult searchDocumentSnippets(searchQuery)

Search the knowledge base for relevant snippets of text.

### Example

```typescript
import {
    KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi,
    Configuration,
    SearchQuery
} from './api';

const configuration = new Configuration();
const apiInstance = new KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi(configuration);

let searchQuery: SearchQuery; //

const { status, data } = await apiInstance.searchDocumentSnippets(
    searchQuery
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **searchQuery** | **SearchQuery**|  | |


### Return type

**SearchResult**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**401** | Unauthorized |  -  |
|**403** | Forbidden |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **uploadDocument**
> uploadDocument()

Upload a document consisting of one or more files to extract memories from. The extraction process happens asynchronously. If a document with the same ID already exists, it will be overwritten and the memories previously extracted will be updated.

### Example

```typescript
import {
    KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi(configuration);

let index: string; //Name of the index where to store memories generated by the files. (optional) (default to undefined)
let documentId: string; //Unique ID used for import pipeline and document ID. (optional) (default to undefined)
let tags: Array<string>; //Tags to apply to the memories extracted from the files. (optional) (default to undefined)
let steps: Array<string>; //How to process the files, e.g. how to extract/chunk etc. (optional) (default to undefined)
let files: Array<File>; //Files to process and extract memories from. (optional) (default to undefined)

const { status, data } = await apiInstance.uploadDocument(
    index,
    documentId,
    tags,
    steps,
    files
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **index** | [**string**] | Name of the index where to store memories generated by the files. | (optional) defaults to undefined|
| **documentId** | [**string**] | Unique ID used for import pipeline and document ID. | (optional) defaults to undefined|
| **tags** | **Array&lt;string&gt;** | Tags to apply to the memories extracted from the files. | (optional) defaults to undefined|
| **steps** | **Array&lt;string&gt;** | How to process the files, e.g. how to extract/chunk etc. | (optional) defaults to undefined|
| **files** | **Array&lt;File&gt;** | Files to process and extract memories from. | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**202** | Accepted |  -  |
|**400** | Bad Request |  -  |
|**401** | Unauthorized |  -  |
|**403** | Forbidden |  -  |
|**503** | Service Unavailable |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

