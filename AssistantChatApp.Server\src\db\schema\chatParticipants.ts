import { pgTable, uuid, primaryKey, timestamp } from 'drizzle-orm/pg-core';
import { users } from './users';
import { chats } from './chats';

export const chatParticipants = pgTable('chat_participants', {
  chatId: uuid('chat_id').notNull().references(() => chats.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  joinedAt: timestamp('joined_at').defaultNow().notNull(),
}, (table) => {
  return [
    primaryKey({ columns: [table.chatId, table.userId] }),
  ];
});
