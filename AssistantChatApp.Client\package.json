{"name": "ai-assistant-chat", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@lexical/react": "^0.32.1", "@mdx-js/mdx": "^3.1.0", "@mdx-js/react": "^3.1.0", "@mdxeditor/editor": "^3.42.0", "@mui/icons-material": "^6", "@mui/material": "^6", "@radix-ui/colors": "^3.0.0", "@tanstack/react-form": "^1.18.0", "@tanstack/react-query": "^5.84.1", "@tanstack/react-router": "^1.130.12", "@tanstack/zod-form-adapter": "^0.42.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "hastscript": "^9.0.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "immutable": "^5.1.3", "jotai": "^2.6.0", "jotai-devtools": "^0.12.0", "jotai-tanstack-query": "^0.11.0", "lexical": "^0.32.1", "lucide-react": "^0.537.0", "material-react-table": "^3.2.1", "material-ui-popup-state": "^5.3.6", "mdast-util-directive": "^3.1.0", "mdast-util-from-markdown": "^2.0.2", "mdast-util-to-hast": "^13.2.0", "react": "^19", "react-dom": "^19", "react-error-boundary": "^6.0.0", "react-i18next": "^15.6.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.4", "react-rnd": "^10.5.2", "react-shadow": "^20.6.0", "rehype-raw": "^7.0.0", "remark-directive": "^4.0.0", "remark-directive-rehype": "^0.4.2", "remark-gfm": "^4.0.0", "remark-mdx": "^3.1.0", "remark-rehype": "^11.1.2", "unist-util-visit": "^5.0.0", "uuid": "^11.1.0", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.0", "@mdx-js/rollup": "^3.1.0", "@tanstack/react-query-devtools": "^5.84.1", "@tanstack/router-devtools": "^1.130.13", "@tanstack/router-plugin": "^1.130.15", "@types/hast": "^3.0.4", "@types/mdast": "^4.0.4", "@types/react": "^19", "@types/react-dom": "^19", "@types/unist": "^3.0.3", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "vite": "^6"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}