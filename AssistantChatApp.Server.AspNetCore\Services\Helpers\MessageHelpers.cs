using AssistantChatApp.Server.AspNetCore.DTOs;
using AssistantChatApp.Server.AspNetCore.Models;
using AssistantChatApp.AIAgents.Shared.Dotnet.Serialization;

namespace AssistantChatApp.Server.AspNetCore.Services.Helpers;

public static class MessageHelpers
{
    public static MessageDto MapMessageToDto(Message message)
    {
        var contents = message.GetMessageContents();
        var otherContent = GetMessageNonTextContents(contents);

        return new MessageDto
        {
            Id = message.Id,
            ChatId = message.ChatId,
            Author = UserService.MapUserToDto(message.Author),
            Content = contents.TextContent,
            OtherContent = otherContent,
            CreatedAt = message.CreatedAt.ToString("o"),
            UpdatedAt = message.UpdatedAt.ToString("o"),
            Mentions = message.Mentions.Select(m => m.UserId).ToList(),
            Tags = message.Tags.Select(t => new TagDto { Id = t.Id, Name = t.Name }).ToList()
        };
    }

    public static MessageForAiDto MapMessageToMessageForAiDto(Message message)
    {
        var messageDto = MapMessageToDto(message);
        var contents = message.GetMessageContents();
        return new MessageForAiDto(messageDto, contents);
    }

    private static FunctionCallContentDto ToDto(this FunctionCallContent call) =>
        new()
        {
            Id = call.Id,
            CallId = call.CallId,
            PluginName = call.PluginName ?? "",
            FunctionName = call.FunctionName,
            Arguments = call.Arguments ?? ""
        };

    private static FunctionResultContentDto ToDto(this FunctionResultContent result) =>
        new()
        {
            Id = result.Id,
            CallId = result.CallId ?? "",
            Result = result.Result ?? "",
            FunctionCall = result.FunctionCall?.ToDto()
        };

    public static MessageContents GetMessageContents(this Message message)
    {
        string text = message.Content;
        // var contents = new List<ContentDto>();
        var calls = new List<FunctionCallContentDto>();
        var results = new List<FunctionResultContentDto>();

        foreach (var c in message.Contents)
        {
            if (c is FunctionCallContent call)
            {
                var callDto = call.ToDto();
                calls.Add(callDto);
            }
            else if (c is FunctionResultContent result)
            {
                var resultDto = result.ToDto();
                results.Add(resultDto);
            }
        }
        return new MessageContents(text, calls, results);
    }

    private static MessageNonTextContents? GetMessageNonTextContents(MessageContents contents)
    {
        // var calls = contents.OtherContents.OfType<FunctionCallContentDto>();
        // var results = contents.OtherContents.OfType<FunctionResultContentDto>();
        var calls = contents.FunctionCalls;
        var results = contents.FunctionResults;
        if ((calls?.Any() ?? false) || (results?.Any() ?? false))
        {
            var callsAndResults = new MessageNonTextContents();
            if (calls?.Any() ?? false)
            {
                callsAndResults.FunctionCalls = [.. calls];
            }
            if (results?.Any() ?? false)
            {
                callsAndResults.FunctionResults = [.. results];
            }
            return callsAndResults;
            //var mdWithJsonCodeBlock = $"```json\n{json}\n```";
            //return mdWithJsonCodeBlock;
        }
        else
        {
            return null;
        }
    }

    public static bool IsTaggedWith(this MessageDto m, string tagId) =>
        m.Tags?.Any(tag => tag.Id == tagId) ?? false;

    public static bool IsForAI(this Message message) =>
        message.Mentions.Any(mention => mention.User.IsAI);
}
