import { Request, Response } from 'express';
import { getUsers, getUserById, getAIAssistants } from '../services/users';
import { parsePaginationParams } from '../utils/pagination';

/**
 * Get users controller
 * @route GET /users
 */
export async function getUsersController(req: Request, res: Response) {
  const { page, limit } = parsePaginationParams(req.query);
  const search = req.query.search as string | undefined;
  
  const result = await getUsers(page, limit, search);
  
  res.status(200).json(result);
}

/**
 * Get user by ID controller
 * @route GET /users/:userId
 */
export async function getUserByIdController(req: Request, res: Response) {
  const { userId } = req.params;
  
  const user = await getUserById(userId);
  
  res.status(200).json(user);
}

/**
 * Get AI assistants controller
 * @route GET /users/ai-assistants
 */
export async function getAIAssistantsController(_req: Request, res: Response) {
  const aiAssistants = await getAIAssistants();
  
  res.status(200).json(aiAssistants);
}
