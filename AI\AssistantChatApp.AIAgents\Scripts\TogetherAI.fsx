//#r @"S:\Prog\AI\together-dotnet\Together\bin\Debug\net9.0\Together.dll"
//#r @"S:\Prog\AI\together-dotnet\Together.SemanticKernel\bin\Debug\net9.0\Together.SemanticKernel.dll"
//#r "nuget: Microsoft.Extensions.AI.Abstractions"

#r "nuget: Microsoft.SemanticKernel, 1.54.0"
#r "../bin/Debug/net9.0/Together.dll"
#r "../bin/Debug/net9.0/Together.SemanticKernel.dll"

#load "Env.fsx"

open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.ChatCompletion
open Together.SemanticKernel.Extensions
open Together.Models
open Together.Models.ChatCompletions

let [<Literal>] ApiEndpoint = "https://api.together.xyz/v1/"

module Models =
    let [<Literal>] ``Llama3.3_Free`` = "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free"
    let [<Literal>] LlamaVisionFree = "meta-llama/Llama-Vision-Free"
    let [<Literal>] DeepseekR1DistillLlama = "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free"
    let [<Literal>] ``Llama3.2-3B-Instruct-Turbo`` = "meta-llama/Llama-3.2-3B-Instruct-Turbo"


let apiKey = Env.env["TogetherAIApiKey"]

let togetherClient = lazy (Together.TogetherClient(apiKey))


let sendUserMessageAndPrintResultsViaTogetherClient (history: ChatHistory) (userMessage: string) =
    task {
        let client = togetherClient.Value
        do history.AddUserMessage userMessage
        printfn "USER:"
        printfn $"\t{userMessage}"
        printfn "---[END USER MESSAGE]---"
        printfn "ASSISTANT:"
        printf "\t"
        let sb = System.Text.StringBuilder()
        let messages = history.ToChatCompletionMessages()
            //[
            //    ChatCompletionMessage(Role = ChatRole.User, Content = userMessage)
            //]
        let req =
            ChatCompletionRequest(
                Model = Models.LlamaVisionFree,
                Messages = ResizeArray messages,
                Stream = true
            )
        let rspStream =
            client.ChatCompletions.CreateStreamAsync(req)

        let etor = rspStream.GetAsyncEnumerator()

        while! etor.MoveNextAsync() do
            //printfn "[SSE Part]: %A" [for c in etor.Current.Choices -> c.Delta.Content]
            etor.Current.Choices
            |> Seq.tryHead
            |> Option.iter (fun part ->
                let c = StreamingChatMessageContent(AuthorRole.Assistant, part.Delta.Content, null, part.Index.GetValueOrDefault())
                sb.Append(c.Content) |> ignore
                printf $"{c.Content}"
            )

        printfn ""
        printfn "---[END ASSISTANT MESSAGE]---"
        let aiMessage = sb.ToString()
        do history.AddAssistantMessage(aiMessage)
    }
    |> Async.AwaitTask
    |> Async.RunSynchronously
