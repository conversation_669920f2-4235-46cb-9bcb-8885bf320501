import { z } from 'zod';

export const createChatSchema = z.object({
  body: z.object({
    title: z.string().min(1, 'Title is required').max(100, 'Title must be at most 100 characters'),
    description: z.string().max(500, 'Description must be at most 500 characters').optional(),
    participants: z.array(z.string().uuid('Invalid participant ID')).optional(),
    tags: z.array(z.string().min(1).max(50)).optional(),
  }),
});

export const updateChatSchema = z.object({
  params: z.object({
    chatId: z.string().uuid('Invalid chat ID'),
  }),
  body: z.object({
    title: z.string().min(1, 'Title is required').max(100, 'Title must be at most 100 characters').optional(),
    description: z.string().max(500, 'Description must be at most 500 characters').optional(),
    participants: z.array(z.string().uuid('Invalid participant ID')).optional(),
    tags: z.array(z.string().min(1).max(50)).optional(),
  }),
});

export const getChatSchema = z.object({
  params: z.object({
    chatId: z.string().uuid('Invalid chat ID'),
  }),
});

export const deleteChatSchema = z.object({
  params: z.object({
    chatId: z.string().uuid('Invalid chat ID'),
  }),
});
