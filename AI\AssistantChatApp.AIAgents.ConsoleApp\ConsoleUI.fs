module ConsoleUI

open System
open System.Threading
open System.Threading.Tasks
open FSharp.Control
open Spectre.Console
open Spectre.Console.Cli
open Microsoft.Extensions.Hosting
open Microsoft.Extensions.Logging
open Microsoft.Extensions.DependencyInjection
open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.ChatCompletion
open Microsoft.SemanticKernel.Agents

open AssistantChatApp.AIAgents.SemanticKernelHelpers

type SkRole = Microsoft.SemanticKernel.ChatCompletion.AuthorRole

let stdLibShell
    (lifetime: IHostApplicationLifetime)
    (thread: ChatHistoryAgentThread)
    (agent: Agent)
    (stoppingToken: CancellationToken): Task =
    task {
        let mutable isRunning = true
        while isRunning do
            printfn "Your question:"
            printf "> "
            let input = Console.ReadLine()
            printfn "---"

            isRunning <- not (String.IsNullOrWhiteSpace input)
            if isRunning then
                printfn "AI:"
                thread.ChatHistory.AddUserMessage(input)
                let execSettings =
                    PromptExecutionSettings()
                    |> PromptExecutionSettings.withFunctionChoiceAuto true None None
                let args = KernelArguments(execSettings)
                let options = AgentInvokeOptions(KernelArguments = args)
                do! agent.InvokeAsync(thread, options, stoppingToken)
                    |> TaskSeq.iter (fun item ->
                        let msg = item.Message
                        printfn $"{msg.Role}: {msg.Content}"
                    )
        return lifetime.StopApplication()
    }

let spectreConsoleShell
    (lifetime: IHostApplicationLifetime)
    (thread: ChatHistoryAgentThread)
    (agent: Agent)
    (stoppingToken: CancellationToken): Task
    =
    task {
        // Show a nice header
        AnsiConsole.Write(
            Rule("[yellow]AI Assistant Console[/]")
                .RuleStyle("grey")
                .LeftJustified()
        )

        let mutable keepRunning = true

        while keepRunning && not stoppingToken.IsCancellationRequested do
            // Prompt the user for a question
            let question =
                AnsiConsole.Prompt(
                    TextPrompt<string>("[green]Your question[/]?")
                        .PromptStyle("cyan")
                        .Validate(fun q ->
                            if String.IsNullOrWhiteSpace q then ValidationResult.Error("[red]Please ask something or press ESC to quit[/]")
                            else ValidationResult.Success())
                )

            // If they pressed Esc on an empty prompt, we quit
            if String.IsNullOrWhiteSpace question then
                keepRunning <- false
            else
                // Add to history
                thread.ChatHistory.AddUserMessage(question)

                // Compose your agent invocation settings exactly as before
                let execSettings =
                    PromptExecutionSettings()
                    |> PromptExecutionSettings.withFunctionChoiceAuto true None None

                let args     = KernelArguments(execSettings)
                let options  = AgentInvokeOptions(KernelArguments = args)

                // Display a spinner while the AI is “thinking”
                let status = AnsiConsole.Status()
                let spinner = status.Spinner(Spinner.Known.Dots)
                let startAsync (context: StatusContext) =
                    task {
                        // Invoke the agent, stream the chunks as they arrive
                        do!
                            agent.InvokeAsync(thread, options, stoppingToken)
                            |> TaskSeq.iter (fun item ->
                                // When you get each chunk, print it out
                                let msg = item.Message
                                // You can color by role if you like:
                                let roleColor =
                                    match msg.Role with
                                    | r when r = SkRole.Assistant -> "yellow"
                                    | r when r = SkRole.Tool -> "grey"
                                    | r when r = SkRole.System -> "grey"
                                    | _ -> "white"

                                // Move out of the spinner so text isn't garbled
                                //context.Status($"[bold]{Char.ToUpper(msg.Role.ToString()[0]) + msg.Role.ToString().Substring(1)}[/]:")
                                context.Status($"[bold]{msg.Role}[/]:") |> ignore
                                AnsiConsole.MarkupLine($"[{roleColor}]{msg.Content}[/]")
                            )
                    }
                do!
                    spinner.StartAsync("Thinking…", startAsync)
                AnsiConsole.WriteLine() // blank line before next cycle

        // We're done, stop the host
        lifetime.StopApplication()
    }
