# DataPipelineStatus


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**completed** | **boolean** |  | [optional] [default to undefined]
**empty** | **boolean** |  | [optional] [default to undefined]
**index** | **string** |  | [optional] [default to undefined]
**document_id** | **string** |  | [optional] [default to undefined]
**tags** | **{ [key: string]: Array&lt;string&gt;; }** |  | [optional] [default to undefined]
**creation** | **string** |  | [optional] [default to undefined]
**last_update** | **string** |  | [optional] [default to undefined]
**steps** | **Array&lt;string&gt;** |  | [optional] [default to undefined]
**remaining_steps** | **Array&lt;string&gt;** |  | [optional] [default to undefined]
**completed_steps** | **Array&lt;string&gt;** |  | [optional] [default to undefined]

## Example

```typescript
import { DataPipelineStatus } from './api';

const instance: DataPipelineStatus = {
    completed,
    empty,
    index,
    document_id,
    tags,
    creation,
    last_update,
    steps,
    remaining_steps,
    completed_steps,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
