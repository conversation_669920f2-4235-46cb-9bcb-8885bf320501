using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AssistantChatApp.Server.AspNetCore.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace AssistantChatApp.Server.AspNetCore.Data
{
    public static class TestDbSeeder
    {
        public static async Task SeedAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();
            var context = services.GetRequiredService<ApplicationDbContext>();
            var userManager = services.GetRequiredService<UserManager<User>>();

            try
            {
                // Apply migrations if they are not applied
                await context.Database.MigrateAsync();

                // Seed users if none exist
                if (!await userManager.Users.AnyAsync())
                {
                    logger.LogInformation("Seeding users...");
                    await SeedUsersAsync(userManager);
                }

                // Seed AI assistants if none exist
                if (!await context.Users.AnyAsync(u => u.IsAI))
                {
                    logger.LogInformation("Seeding AI assistants...");
                    await SeedAIAssistantsAsync(userManager);
                }

                // Seed sample chats if none exist
                if (!await context.Chats.AnyAsync())
                {
                    logger.LogInformation("Seeding sample chats...");
                    await SeedSampleChatsAsync(context, userManager);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding the database.");
                throw;
            }
        }

        private static async Task SeedUsersAsync(UserManager<User> userManager)
        {
            var users = new List<User>
            {
                new User
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    DisplayName = "John Doe",
                    EmailConfirmed = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    Avatar = "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg"
                },
                new User
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    DisplayName = "Jane Smith",
                    EmailConfirmed = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-25),
                    Avatar = "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg"
                }
            };

            foreach (var user in users)
            {
                if (await userManager.FindByEmailAsync(user.Email!) == null)
                {
                    await userManager.CreateAsync(user, "Password123!");
                }
            }
        }

        private static async Task SeedAIAssistantsAsync(UserManager<User> userManager)
        {
            var aiAssistants = new List<User>
            {
                new User
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    DisplayName = "AI Assistant",
                    EmailConfirmed = true,
                    IsAI = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-60),
                    Avatar = "https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg"
                },
                new User
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    DisplayName = "AI Helper",
                    EmailConfirmed = true,
                    IsAI = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-45),
                    Avatar = "https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg"
                },
                new User
                {
                    UserName = "fake_ai",
                    Email = "<EMAIL>",
                    DisplayName = "Fake AI Helper",
                    EmailConfirmed = true,
                    IsAI = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-45),
                    Avatar = "https://images.pexels.com/photos/981588/pexels-photo-981588.jpeg?auto=compress&cs=tinysrgb&w=640&h=427&dpr=2"
                }
            };

            foreach (var assistant in aiAssistants)
            {
                if (await userManager.FindByEmailAsync(assistant.Email!) == null)
                {
                    await userManager.CreateAsync(assistant, "AIPassword123!");
                }
            }
        }

        private static async Task SeedSampleChatsAsync(ApplicationDbContext context, UserManager<User> userManager)
        {
            var users = await userManager.Users.ToListAsync();
            var regularUsers = users.Where(u => !u.IsAI).ToList();
            var aiAssistants = users.Where(u => u.IsAI).ToList();

            if (!regularUsers.Any() || !aiAssistants.Any())
            {
                return;
            }

            var chats = new List<Chat>
            {
                new Chat
                {
                    Title = "General Discussion",
                    Description = "A chat for general discussions",
                    OwnerId = regularUsers[0].Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-20),
                    UpdatedAt = DateTime.UtcNow.AddDays(-1)
                },
                new Chat
                {
                    Title = "AI Help",
                    Description = "Get help from AI assistants",
                    OwnerId = regularUsers[1].Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-15),
                    UpdatedAt = DateTime.UtcNow.AddDays(-2)
                }
            };

            // Add chats
            await context.Chats.AddRangeAsync(chats);
            await context.SaveChangesAsync();

            // Add participants to chats
            var chatParticipants = new List<ChatParticipant>();

            // Add all regular users to the first chat
            foreach (var user in regularUsers)
            {
                chatParticipants.Add(new ChatParticipant
                {
                    ChatId = chats[0].Id,
                    UserId = user.Id,
                    JoinedAt = DateTime.UtcNow.AddDays(-20)
                });
            }

            // Add all users (regular and AI) to the second chat
            foreach (var user in users)
            {
                chatParticipants.Add(new ChatParticipant
                {
                    ChatId = chats[1].Id,
                    UserId = user.Id,
                    JoinedAt = DateTime.UtcNow.AddDays(-15)
                });
            }

            await context.ChatParticipants.AddRangeAsync(chatParticipants);

            // Add tags to chats
            var chatTags = new List<ChatTag>
            {
                new ChatTag { ChatId = chats[0].Id, TagName = "general" },
                new ChatTag { ChatId = chats[0].Id, TagName = "discussion" },
                new ChatTag { ChatId = chats[1].Id, TagName = "ai" },
                new ChatTag { ChatId = chats[1].Id, TagName = "help" }
            };

            await context.ChatTags.AddRangeAsync(chatTags);

            // Add messages to chats
            var messages = new List<Message>
            {
                new Message
                {
                    ChatId = chats[0].Id,
                    AuthorId = regularUsers[0].Id,
                    Content = "Hello everyone! Welcome to the general discussion chat.",
                    CreatedAt = DateTime.UtcNow.AddDays(-20),
                    UpdatedAt = DateTime.UtcNow.AddDays(-20)
                },
                new Message
                {
                    ChatId = chats[0].Id,
                    AuthorId = regularUsers[1].Id,
                    Content = "Thanks for creating this chat!",
                    CreatedAt = DateTime.UtcNow.AddDays(-19),
                    UpdatedAt = DateTime.UtcNow.AddDays(-19)
                },
                new Message
                {
                    ChatId = chats[1].Id,
                    AuthorId = regularUsers[1].Id,
                    Content = "Hello AI assistants! Can you help me with something?",
                    CreatedAt = DateTime.UtcNow.AddDays(-15),
                    UpdatedAt = DateTime.UtcNow.AddDays(-15)
                },
                new Message
                {
                    ChatId = chats[1].Id,
                    AuthorId = aiAssistants[0].Id,
                    Content = "Of course! I'm here to help. What do you need assistance with?",
                    CreatedAt = DateTime.UtcNow.AddDays(-15).AddMinutes(5),
                    UpdatedAt = DateTime.UtcNow.AddDays(-15).AddMinutes(5)
                }
            };

            await context.Messages.AddRangeAsync(messages);

            // Add mentions
            var messageMentions = new List<MessageMention>
            {
                new MessageMention
                {
                    MessageId = messages[2].Id,
                    UserId = aiAssistants[0].Id
                }
            };

            await context.MessageMentions.AddRangeAsync(messageMentions);
            await context.SaveChangesAsync();
        }
    }
}
