namespace Enixar.KernelMemory.Client.Import

type Tags = (string * string) list

type ImportEntryMetadata = {
    Id: string
    Index: string option
    Tags: Tags option
}

type ImportEntryData =
    | Document of filePath: string
    | Text of textContent: string
    | WebPage of url: string

type ImportEntry = {
    Metadata: ImportEntryMetadata
    Data: ImportEntryData
}

// Extensions:

type ImportEntry with
    static member CreateDocument(id, filePath, ?index, ?tags) =
        {
            Metadata = { Id = id; Index = index; Tags = tags }
            Data = Document filePath
        }

    static member CreateManyDocuments(documents: seq<string * string>, ?index, ?tags) =
        [ for (id, filePath) in documents do
                ImportEntry.CreateDocument(id, filePath, ?index = index, ?tags = tags)
        ]
