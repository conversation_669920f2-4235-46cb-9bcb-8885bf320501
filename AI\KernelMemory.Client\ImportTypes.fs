namespace Enixar.KernelMemory.Client.Import

type DocumentImportEntry = {
    Id: string
    FilePath: string
    Index: string option
    Tags: (string * string) list option
}

// type DocumentGroupImportEntry = {
//     /// Document ID to File Path map
//     Documents: Map<string, string>
//     Index: string option
//     Tags: (string * string) list option
// }

type ImportEntry =
    internal
    | Document of entry: DocumentImportEntry
    // | DocumentGroupImportEntry of entry: DocumentGroupImportEntry


// Extensions:

type DocumentImportEntry with
    static member Create(id, filePath, ?index, ?tags) =
        {
            Id = id
            FilePath = filePath
            Index = index
            Tags = tags
        }

    static member CreateMany(documents: seq<string * string>, ?index, ?tags) =
        [ for (id, filePath) in documents do
                DocumentImportEntry.Create(id, filePath, ?index = index, ?tags = tags)
        ]


// type DocumentGroupImportEntry with
//     static member Create(documents, ?index, ?tags) =
//         {
//             Documents = documents
//             Index = index
//             Tags = tags
//         }


// type ImportEntryTemplate =

//     /// <summary>
//     /// Шаблон группы документов. Все документы имеют одинаковый индекс и теги.
//     /// </summary>
//     /// <param name="index">Индекс.</param>
//     /// <param name="tags">Теги.</param>
//     static member Group(?index, ?tags) =
//         fun documents ->
//             DocumentGroupImportEntry.Create(documents, ?index = index, ?tags = tags)
//             |> ImportEntry.DocumentGroupImportEntry

//     /// <summary>
//     /// Создание шаблона для документа. Шаблон определяет индекс и набор тегов.
//     /// </summary>
//     /// <param name="index">Имя индекса.</param>
//     /// <param name="tags">Список тегов в формате пар `(ключ, значение)`.</param>
//     static member Single(?index, ?tags) =
//         fun id filePath ->
//             DocumentImportEntry.Create(id, filePath, ?index = index, ?tags = tags)
//             |> ImportEntry.DocumentImportEntry


type ImportEntry with
    static member DocumentGroup(documents: seq<string * string>, ?index, ?tags) =
        documents
        |> Seq.map (fun (id, filePath) ->
            DocumentImportEntry.Create(id, filePath, ?index = index, ?tags = tags)
        )

    static member SingleDocument(id, filePath, ?index, ?tags) =
        DocumentImportEntry.Create(id, filePath, ?index = index, ?tags = tags)
        // |> ImportEntry.DocumentImportEntry

    // member entry.ToDocumentImportEntryList() =
    //     match entry with
    //     | DocumentImportEntry entry ->
    //         [entry]
    //     | DocumentGroupImportEntry entry ->
    //         [
    //             for (KeyValue (documentId, filePath)) in entry.Documents do
    //                 DocumentImportEntry.Create(
    //                     documentId, filePath,
    //                     ?index = entry.Index,
    //                     ?tags = entry.Tags
    //                 )
    //             ]
