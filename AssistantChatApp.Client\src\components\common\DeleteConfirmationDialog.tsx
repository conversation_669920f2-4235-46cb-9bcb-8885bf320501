import React from "react";
import { Dialog, DialogTitle, DialogContent, DialogActions, But<PERSON> } from "@mui/material";
import { useTranslation } from "react-i18next";

export interface DeleteConfirmationDialogProps {
  isOpened: boolean;
  onClose: () => void;
  onConfirm: () => void;
  message: string;
  title: string;
}

export const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({
  isOpened,
  onClose,
  onConfirm,
  message,
  title,
}) => {
  const { t } = useTranslation("common");
  const handleConfirm = () => {
    onConfirm();
    onClose();
  }
  return (
    <Dialog open={isOpened} onClose={onClose} maxWidth="sm">
      <DialogTitle>{title}</DialogTitle>
      <DialogContent>
        {message}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>{t("cancel")}</Button>
        <Button onClick={handleConfirm} color="error">
          {t("confirm_delete")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
