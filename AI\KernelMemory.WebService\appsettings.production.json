{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "KernelMemory": {"Service": {"OpenApiEnabled": true, "RunWebService": true}, "DocumentStorageType": "SimpleFileStorage", "TextGeneratorType": "Ollama", "DataIngestion": {"EmbeddingGenerationEnabled": true, "EmbeddingGeneratorTypes": ["Ollama"], "MemoryDbTypes": ["SimpleVectorDb"]}, "Retrieval": {"EmbeddingGeneratorType": "Ollama", "MemoryDbType": "SimpleVectorDb"}, "Services": {"SimpleFileStorage": {"StorageType": "Disk", "Directory": "./data/docs"}, "SimpleVectorDb": {"StorageType": "Disk", "Directory": "./data/docs"}, "Ollama": {"Endpoint": "http://localhost:11434", "TextModel": {"ModelName": "gemma3n:latest"}, "EmbeddingModel": {"ModelName": "nomic-embed-text:latest"}}}}}