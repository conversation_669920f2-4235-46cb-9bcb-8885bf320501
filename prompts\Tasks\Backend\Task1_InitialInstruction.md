# Instruction draft

## Core components:

A. Authentication System
- JWT token generation and validation
- User authentication middleware
- Password hashing and verification
- Session management

B. Database Design
- User table (matching User interface)
- Chat table (matching Chat interface)
- Message table (matching Message interface)
- Relationships:
  - Chat-User (many-to-many for participants)
  - Message-User (many-to-one for author)
  - Message-Chat (many-to-one)

C. Core Services
- AuthService: Handle login and user authentication
- ChatService: Manage chat operations
- MessageService: Handle message operations
- UserService: Manage user operations
- AIService: Handle AI assistant integration

D. API Controllers
- AuthController: /auth endpoints
- ChatController: /chats endpoints
- MessageController: /chats/{chatId}/messages endpoints
- UserController: /users endpoints

## Technical Decisions

A. Technology Stack
- Runtime: Node.js or .NET Core (both would work well)
- Database: PostgreSQL (good for relational data + JSON support)
- ORM: TypeORM/Prisma (Node.js) or Entity Framework (NET)
- API Framework: Express/NestJS (Node.js) or ASP.NET Core (NET)

B. Development Approach
- Start with database schema design
- Implement authentication first
- Build core CRUD operations
- Add AI integration last
- Use Docker for development environment

C. Testing Strategy
- Unit tests for services
- Integration tests for API endpoints
- Test database using in-memory database

## Implementation phases

Phase 1: Setup & Authentication
- Project initialization
- Database setup
- User model implementation
- JWT authentication system
- Auth endpoints

Phase 2: Core Features
- Chat CRUD operations
- Message handling
- User management
- Pagination implementation
- Search/filter functionality

Phase 3: AI Integration
- AI assistant user type
- AI message generation
- AI service integration
- Async message processing

Phase 4: Advanced Features
- Real-time updates
- Message mentions
- Rich text handling
- File attachments (if needed)

Phase 5: Testing & Documentation
- Unit tests
- Integration tests
- API documentation
- Deployment guide


# Actual instruction

## Technological stack:

- Runtime: Node.js
- Package manager: pnpm
- Database: PostgreSQL
- ORM: Drizzle
- API Framework: Express
- Authentication: JWT
- Testing: Jest
