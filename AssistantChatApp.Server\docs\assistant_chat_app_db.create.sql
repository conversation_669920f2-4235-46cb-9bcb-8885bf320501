-- This script was generated by the ERD tool in pgAdmin 4.
-- Please log an issue at https://github.com/pgadmin-org/pgadmin4/issues/new/choose if you find any bugs, including reproduction steps.
BEGIN;


CREATE TABLE IF NOT EXISTS public.chat_participants
(
    chat_id uuid NOT NULL,
    user_id uuid NOT NULL,
    joined_at timestamp without time zone NOT NULL DEFAULT now(),
    CONSTRAINT chat_participants_chat_id_user_id_pk PRIMARY KEY (chat_id, user_id)
);

CREATE TABLE IF NOT EXISTS public.chat_tags
(
    chat_id uuid NOT NULL,
    tag_id uuid NOT NULL,
    CONSTRAINT chat_tags_chat_id_tag_id_pk PRIMARY KEY (chat_id, tag_id)
);

CREATE TABLE IF NOT EXISTS public.chats
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    title character varying(100) COLLATE pg_catalog."default" NOT NULL,
    description character varying(500) COLLATE pg_catalog."default",
    created_at timestamp without time zone NOT NULL DEFAULT now(),
    updated_at timestamp without time zone NOT NULL DEFAULT now(),
    CONSTRAINT chats_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.mentions
(
    message_id uuid NOT NULL,
    user_id uuid NOT NULL,
    CONSTRAINT mentions_message_id_user_id_pk PRIMARY KEY (message_id, user_id)
);

CREATE TABLE IF NOT EXISTS public.messages
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    chat_id uuid NOT NULL,
    author_id uuid NOT NULL,
    content text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT now(),
    updated_at timestamp without time zone NOT NULL DEFAULT now(),
    CONSTRAINT messages_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.tags
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    name character varying(50) COLLATE pg_catalog."default" NOT NULL,
    CONSTRAINT tags_pkey PRIMARY KEY (id),
    CONSTRAINT tags_name_unique UNIQUE (name)
);

CREATE TABLE IF NOT EXISTS public.users
(
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    email character varying(255) COLLATE pg_catalog."default" NOT NULL,
    password character varying(255) COLLATE pg_catalog."default" NOT NULL,
    display_name character varying(100) COLLATE pg_catalog."default" NOT NULL,
    avatar character varying(255) COLLATE pg_catalog."default",
    created_at timestamp without time zone NOT NULL DEFAULT now(),
    updated_at timestamp without time zone NOT NULL DEFAULT now(),
    is_ai boolean NOT NULL DEFAULT false,
    CONSTRAINT users_pkey PRIMARY KEY (id),
    CONSTRAINT users_email_unique UNIQUE (email)
);

ALTER TABLE IF EXISTS public.chat_participants
    ADD CONSTRAINT chat_participants_chat_id_chats_id_fk FOREIGN KEY (chat_id)
    REFERENCES public.chats (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.chat_participants
    ADD CONSTRAINT chat_participants_user_id_users_id_fk FOREIGN KEY (user_id)
    REFERENCES public.users (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.chat_tags
    ADD CONSTRAINT chat_tags_chat_id_chats_id_fk FOREIGN KEY (chat_id)
    REFERENCES public.chats (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.chat_tags
    ADD CONSTRAINT chat_tags_tag_id_tags_id_fk FOREIGN KEY (tag_id)
    REFERENCES public.tags (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.mentions
    ADD CONSTRAINT mentions_message_id_messages_id_fk FOREIGN KEY (message_id)
    REFERENCES public.messages (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.mentions
    ADD CONSTRAINT mentions_user_id_users_id_fk FOREIGN KEY (user_id)
    REFERENCES public.users (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.messages
    ADD CONSTRAINT messages_author_id_users_id_fk FOREIGN KEY (author_id)
    REFERENCES public.users (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.messages
    ADD CONSTRAINT messages_chat_id_chats_id_fk FOREIGN KEY (chat_id)
    REFERENCES public.chats (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE CASCADE;

END;