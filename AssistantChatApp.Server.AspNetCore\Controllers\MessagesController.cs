using AssistantChatApp.Server.Contracts.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AssistantChatApp.Server.Services;

namespace AssistantChatApp.Server.AspNetCore.Controllers
{
    [ApiController]
    [Route("/api/chats/{chatId}/messages")]
    [Authorize]
    public class MessagesController : ControllerBase
    {
        private readonly MessageService _messageService;
        private readonly AIAssistantService _aiAssistantService;
        private readonly UserService _userService;
        private readonly IHostEnvironment _hostEnv;

        public MessagesController(
            IHostEnvironment hostEnv,
            MessageService messageService,
            AIAssistantService aiAssistantService,
            UserService userService
        )
        {
            _hostEnv = hostEnv;
            _messageService = messageService;
            _aiAssistantService = aiAssistantService;
            _userService = userService;
        }

        private IActionResult InvalidUserError() =>
            Unauthorized(new ErrorResponseDto { Message = "Invalid token" });

        private Task<string?> TryGetUserIdAsync() =>
            Task.FromResult(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value);

        [HttpGet]
        public async Task<IActionResult> GetMessages(string chatId, [FromQuery] int page = 1, [FromQuery] int limit = 50)
        {
            var userId = await TryGetUserIdAsync();

            if (string.IsNullOrEmpty(userId))
            {
                return InvalidUserError();
            }

            try
            {
                // Filter out system messages only in production
                var exclusionTagIds =
                    _hostEnv.IsProduction()
                    ? MessageTagSets.SystemTags.Select(t => t.Id)
                    : null;
                var messages =
                    await _messageService.GetMessagesAsync(
                        chatId, userId, page, limit,
                        exclusionTagIds
                    );
                return Ok(messages);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
        }

        [HttpGet("by-ids")]
        public async Task<IActionResult> GetMessagesByIds(string chatId, [FromQuery] List<string> messageIds)
        {
            var userId = await TryGetUserIdAsync();

            if (string.IsNullOrEmpty(userId))
            {
                return InvalidUserError();
            }

            try
            {
                var messages = await _messageService.GetMessagesByIdsAsync(chatId, userId, messageIds);
                return Ok(messages);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateMessage(string chatId, [FromBody] CreateMessageDto createMessageDto)
        {
            var userId = await TryGetUserIdAsync();

            if (string.IsNullOrEmpty(userId))
            {
                return InvalidUserError();
            }

            try
            {
                var requestMessage =
                    await _messageService.CreateMessageAsync(chatId, userId, createMessageDto);
                var responseMessages =
                    await _aiAssistantService.AskAllMentionedAsync(requestMessage);
                List<string> messageIds =
                    [requestMessage.Id, ..responseMessages.Select(m => m.Id)];
                return CreatedAtAction(
                    nameof(GetMessagesByIds),
                    new { chatId, messageIds },
                    new CreateMessageResponse(requestMessage, responseMessages)
                );
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
        }

        [HttpGet("{messageId}")]
        public async Task<IActionResult> GetMessage(string chatId, string messageId)
        {
            var userId = await TryGetUserIdAsync();

            if (string.IsNullOrEmpty(userId))
            {
                return InvalidUserError();
            }

            try
            {
                var message = await _messageService.GetMessageByIdAsync(chatId, messageId, userId);

                if (message == null)
                {
                    return NotFound(new ErrorResponseDto { Message = "Message not found" });
                }

                return Ok(message);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
        }

        [HttpPatch("{messageId}")]
        public async Task<IActionResult> UpdateMessage(string chatId, string messageId, [FromBody] UpdateMessageDto updateMessageDto)
        {
            var userId = await TryGetUserIdAsync();

            if (string.IsNullOrEmpty(userId))
            {
                return InvalidUserError();
            }

            var message = await _messageService.UpdateMessageAsync(chatId, messageId, userId, updateMessageDto);

            if (message == null)
            {
                return NotFound(new ErrorResponseDto { Message = "Message not found or you don't have permission to update it" });
            }

            return Ok(message);
        }

        [HttpDelete("{messageId}")]
        public async Task<IActionResult> DeleteMessage(string chatId, string messageId)
        {
            var userId = await TryGetUserIdAsync();

            if (string.IsNullOrEmpty(userId))
            {
                return InvalidUserError();
            }

            var result = await _messageService.DeleteMessageAsync(chatId, messageId, userId);

            if (!result)
            {
                return NotFound(new ErrorResponseDto { Message = "Message not found or you don't have permission to delete it" });
            }

            return NoContent();
        }
    }
}
