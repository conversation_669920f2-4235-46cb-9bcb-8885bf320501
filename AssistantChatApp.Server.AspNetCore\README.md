# Assistant Chat App - Backend

This is the backend implementation for the Assistant Chat App, built with ASP.NET Core 8.0 and Entity Framework Core.

## Technology Stack

- **Runtime**: .NET 8
- **Language**: C#
- **Web API Framework**: ASP.NET Core
- **Database**: PostgreSQL
- **ORM**: Entity Framework Core
- **Authentication**: JWT
- **Testing**: xUnit

## Project Structure

- **Controllers/**: API endpoints
- **Data/**: Database context and migrations
- **DTOs/**: Data Transfer Objects for API requests and responses
- **Helpers/**: Utility classes and middleware
- **Models/**: Domain models
- **Services/**: Business logic

## API Endpoints

The API follows RESTful principles and includes the following endpoints:

### Authentication

- `POST /auth/login`: Authenticate a user
- `GET /auth/me`: Get the current authenticated user

### Chats

- `GET /chats`: Get all chats for the authenticated user
- `GET /chats/{chatId}`: Get a specific chat
- `POST /chats`: Create a new chat
- `PATCH /chats/{chatId}`: Update a chat
- `DELETE /chats/{chatId}`: Delete a chat

### Messages

- `GET /chats/{chatId}/messages`: Get all messages in a chat
- `GET /chats/{chatId}/messages/{messageId}`: Get a specific message
- `POST /chats/{chatId}/messages`: Create a new message
- `PATCH /chats/{chatId}/messages/{messageId}`: Update a message
- `DELETE /chats/{chatId}/messages/{messageId}`: Delete a message

### Users

- `GET /users`: Get all users
- `GET /users/{userId}`: Get a specific user
- `GET /users/ai-assistants`: Get all AI assistants

## Getting Started

### Prerequisites

- .NET 8 SDK
- PostgreSQL

### Setup

1. Clone the repository
2. Update the connection string in `appsettings.json` to point to your PostgreSQL instance
3. Run the following commands:

```bash
# Apply database migrations
dotnet ef database update

# Run the application
dotnet run
```

### Testing

Run the tests using:

```bash
dotnet test
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. To access protected endpoints, include the JWT token in the Authorization header:

```
Authorization: Bearer {token}
```

You can obtain a token by calling the `/auth/login` endpoint with valid credentials.
