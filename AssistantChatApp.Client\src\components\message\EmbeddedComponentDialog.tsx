import React from "react";
import { DialogTitle, DialogContent, IconButton, Stack } from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import { RndDialogMemo } from "@/components/common/RndDialog";

export interface EmbeddedComponentDialogProps {
  componentId: string;
  open: boolean;
  onClose: () => void;
  title: React.ReactNode;
}

export const EmbeddedComponentDialog: React.FC<React.PropsWithChildren<EmbeddedComponentDialogProps>> = ({
  children,
  title,
  open,
  onClose,
}) => {
  return (
    <RndDialogMemo
      rndProps={{
        size: { width: 400, height: 300 },
        position: { x: 0, y: 0 },
      }}
      dialogProps={{
        open,
        onClose,
        scroll: "paper"
      }}
    >
      <DialogTitle>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          {title}
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>
      <DialogContent>
        {children}
      </DialogContent>
    </RndDialogMemo>
  )
}
