using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AssistantChatApp.Server.AspNetCore.Data;
using AssistantChatApp.Server.AspNetCore.DTOs;
using AssistantChatApp.Server.AspNetCore.Models;
using AssistantChatApp.Server.AspNetCore.Services.Helpers;

using Microsoft.EntityFrameworkCore;

namespace AssistantChatApp.Server.AspNetCore.Services
{
    public class ChatService
    {
        private readonly ApplicationDbContext _context;

        public ChatService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<PaginatedResponseDto<ChatDto>> GetChatsAsync(string userId, ChatFilterDto filter)
        {
            var query = _context.Chats
                .Include(c => c.Participants)
                .ThenInclude(cp => cp.User)
                .Include(c => c.Tags)
                .Include(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1))
                .ThenInclude(m => m.Author)
                .Include(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1))
                .ThenInclude(m => m.Mentions)
                .ThenInclude(mm => mm.User)
                .Where(c => c.Participants.Any(p => p.UserId == userId));

            // Apply filters
            if (!string.IsNullOrWhiteSpace(filter.Search))
            {
                var search = filter.Search.ToLower();
                query = query.Where(c => 
                    c.Title.ToLower().Contains(search) || 
                    (c.Description != null && c.Description.ToLower().Contains(search)));
            }

            if (filter.Tags != null && filter.Tags.Any())
            {
                query = query.Where(c => c.Tags.Any(t => filter.Tags.Contains(t.TagName)));
            }

            if (filter.Participants != null && filter.Participants.Any())
            {
                query = query.Where(c => 
                    c.Participants.Any(p => filter.Participants.Contains(p.UserId)));
            }

            if (!string.IsNullOrWhiteSpace(filter.StartDate) && DateTime.TryParse(filter.StartDate, out var startDate))
            {
                query = query.Where(c => c.CreatedAt >= startDate);
            }

            if (!string.IsNullOrWhiteSpace(filter.EndDate) && DateTime.TryParse(filter.EndDate, out var endDate))
            {
                query = query.Where(c => c.CreatedAt <= endDate);
            }

            var total = await query.CountAsync();
            var chats = await query
                .OrderByDescending(c => c.UpdatedAt)
                .Skip((filter.Page - 1) * filter.Limit)
                .Take(filter.Limit)
                .ToListAsync();

            return new PaginatedResponseDto<ChatDto>
            {
                Data = chats.Select(MapChatToDto).ToList(),
                Total = total,
                Page = filter.Page,
                Limit = filter.Limit
            };
        }

        public async Task<ChatDto?> GetChatByIdAsync(string chatId, string userId)
        {
            var chat = await _context.Chats
                .Include(c => c.Participants)
                .ThenInclude(cp => cp.User)
                .Include(c => c.Tags)
                .Include(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1))
                .ThenInclude(m => m.Author)
                .Include(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1))
                .ThenInclude(m => m.Mentions)
                .ThenInclude(mm => mm.User)
                .FirstOrDefaultAsync(c => c.Id == chatId && c.Participants.Any(p => p.UserId == userId));

            return chat != null ? MapChatToDto(chat) : null;
        }

        public async Task<ChatDto> CreateChatAsync(string userId, CreateChatDto createChatDto)
        {
            var chat = new Chat
            {
                Title = createChatDto.Title,
                Description = createChatDto.Description,
                OwnerId = userId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Add the owner as a participant
            chat.Participants.Add(new ChatParticipant
            {
                UserId = userId,
                JoinedAt = DateTime.UtcNow
            });

            // Add other participants
            foreach (var participantId in createChatDto.Participants.Where(p => p != userId))
            {
                chat.Participants.Add(new ChatParticipant
                {
                    UserId = participantId,
                    JoinedAt = DateTime.UtcNow
                });
            }

            // Add tags
            if (createChatDto.Tags != null)
            {
                foreach (var tag in createChatDto.Tags)
                {
                    chat.Tags.Add(new ChatTag { TagName = tag });
                }
            }

            _context.Chats.Add(chat);
            await _context.SaveChangesAsync();

            return await GetChatByIdAsync(chat.Id, userId) ?? throw new InvalidOperationException("Failed to retrieve created chat");
        }

        public async Task<ChatDto?> UpdateChatAsync(string chatId, string userId, UpdateChatDto updateChatDto)
        {
            var chat = await _context.Chats
                .Include(c => c.Participants)
                .Include(c => c.Tags)
                .FirstOrDefaultAsync(c => c.Id == chatId && (c.OwnerId == userId || c.Participants.Any(p => p.UserId == userId)));

            if (chat == null)
            {
                return null;
            }

            // Only the owner can update certain properties
            if (chat.OwnerId == userId)
            {
                if (updateChatDto.Title != null)
                {
                    chat.Title = updateChatDto.Title;
                }

                if (updateChatDto.Description != null)
                {
                    chat.Description = updateChatDto.Description;
                }

                // Update participants (only owner can do this)
                if (updateChatDto.Participants != null)
                {
                    // Remove participants that are not in the new list
                    var participantsToRemove = chat.Participants
                        .Where(p => p.UserId != chat.OwnerId && !updateChatDto.Participants.Contains(p.UserId))
                        .ToList();

                    foreach (var participant in participantsToRemove)
                    {
                        chat.Participants.Remove(participant);
                    }

                    // Add new participants
                    foreach (var participantId in updateChatDto.Participants.Where(p => p != chat.OwnerId && !chat.Participants.Any(cp => cp.UserId == p)))
                    {
                        chat.Participants.Add(new ChatParticipant
                        {
                            UserId = participantId,
                            JoinedAt = DateTime.UtcNow
                        });
                    }
                }

                // Update tags
                if (updateChatDto.Tags != null)
                {
                    // Remove all existing tags
                    chat.Tags.Clear();

                    // Add new tags
                    foreach (var tag in updateChatDto.Tags)
                    {
                        chat.Tags.Add(new ChatTag { TagName = tag });
                    }
                }
            }

            chat.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            return await GetChatByIdAsync(chat.Id, userId);
        }

        public async Task<bool> DeleteChatAsync(string chatId, string userId)
        {
            var chat = await _context.Chats
                .FirstOrDefaultAsync(c => c.Id == chatId && c.OwnerId == userId);

            if (chat == null)
            {
                return false;
            }

            _context.Chats.Remove(chat);
            await _context.SaveChangesAsync();

            return true;
        }

        private static ChatDto MapChatToDto(Chat chat)
        {
            return new ChatDto
            {
                Id = chat.Id,
                Title = chat.Title,
                Description = chat.Description,
                Participants = chat.Participants.Select(p => UserService.MapUserToDto(p.User)).ToList(),
                CreatedAt = chat.CreatedAt.ToString("o"),
                UpdatedAt = chat.UpdatedAt.ToString("o"),
                Tags = chat.Tags.Select(t => t.TagName).ToList(),
                LastMessage = chat.Messages.FirstOrDefault() != null 
                    ? MessageHelpers.MapMessageToDto(chat.Messages.First()) 
                    : null
            };
        }
    }
}
