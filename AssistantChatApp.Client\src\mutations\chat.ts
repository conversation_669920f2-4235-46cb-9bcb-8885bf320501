import { useMutation, useQueryClient } from "@tanstack/react-query";
import { chatApi } from "../api/chatApi";
import { aiChatApi } from "../api/aiChatCompletionApi";

import { CreateOrUpdateChatRequest, Chat, AIChatCompletionRequest, Message } from "../types";
import { chatQueryKeys } from "../queries/chat";
import { messageQueryKeys } from "../queries/message";

export interface UpdateChatMutationProps {
  chatId: string;
  chatData: CreateOrUpdateChatRequest;
}

export const useCreateChat = (onSuccess?: (chat: Chat) => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOrUpdateChatRequest) => chatApi.createChat(data),
    onSuccess: (chat) => {
      queryClient.invalidateQueries({ queryKey: chatQueryKeys.allChats });
      if (onSuccess) {
        onSuccess(chat);
      }
    },
  });
};

export const useUpdateChat = (onSuccess?: (chat: Chat) => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ chatId, chatData }: UpdateChatMutationProps) =>
      chatApi.updateChat(chatId, chatData),
    onSuccess: (chat) => {
      queryClient.invalidateQueries({ queryKey: chatQueryKeys.allChats });
      queryClient.invalidateQueries({ queryKey: chatQueryKeys.chat(chat.id) });
      if (onSuccess) {
        onSuccess(chat);
      }
    },
  });
};

export const useDeleteChat = (onSuccess?: (chatId: string) => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (chatId: string) => chatApi.deleteChat(chatId),
    onSuccess: (data, chatId) => {
      queryClient.invalidateQueries({ queryKey: chatQueryKeys.allChats });
      queryClient.invalidateQueries({ queryKey: chatQueryKeys.chat(chatId) });
      if (onSuccess) {
        onSuccess(chatId);
      }
    },
  });
};

export const useGetChatCompletion = (onSuccess?: (aiMessages: Message[]) => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: AIChatCompletionRequest) =>
      aiChatApi.getChatCompletion(request),
    onSuccess: (rsp, req) => {
      queryClient.invalidateQueries({ queryKey: messageQueryKeys.allChatMessages(req.chatId) });
      if (onSuccess) {
        onSuccess(rsp.completionMessages);
      }
    },
  });
};
