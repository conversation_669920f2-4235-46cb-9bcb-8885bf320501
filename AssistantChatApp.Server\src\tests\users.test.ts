import request from 'supertest';
import app from '../app';
import { db } from '../db';
import { users } from '../db/schema';
import { hashPassword } from '../auth/password';
import { v4 as uuidv4 } from 'uuid';

// Mock users for testing
const testUser = {
  id: uuidv4(),
  email: '<EMAIL>',
  password: 'password123',
  displayName: 'Test User',
  isAI: false,
};

const aiUser = {
  id: uuidv4(),
  email: '<EMAIL>',
  password: 'password123',
  displayName: 'AI Assistant',
  isAI: true,
};

describe('Users API', () => {
  let authToken: string;

  beforeAll(async () => {
    // Insert test users with hashed passwords
    const hashedPassword = await hashPassword('password123');
    
    await db.insert(users).values({
      ...testUser,
      password: hashedPassword,
    });
    
    await db.insert(users).values({
      ...aiUser,
      password: hashedPassword,
    });

    // Login to get auth token
    const res = await request(app)
      .post('/api/auth/login')
      .send({
        email: testUser.email,
        password: 'password123',
      });

    authToken = res.body.token;
  });

  afterAll(async () => {
    // Clean up test users
    await db.delete(users).where({ id: testUser.id });
    await db.delete(users).where({ id: aiUser.id });
  });

  describe('GET /api/users', () => {
    it('should return paginated users', async () => {
      const res = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('data');
      expect(res.body).toHaveProperty('total');
      expect(res.body).toHaveProperty('page');
      expect(res.body).toHaveProperty('limit');
      expect(Array.isArray(res.body.data)).toBe(true);
    });

    it('should filter users by search term', async () => {
      const res = await request(app)
        .get('/api/users?search=AI')
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toBe(200);
      expect(res.body.data.some((user: any) => user.displayName.includes('AI'))).toBe(true);
    });
  });

  describe('GET /api/users/:userId', () => {
    it('should return user by ID', async () => {
      const res = await request(app)
        .get(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('id', testUser.id);
      expect(res.body).toHaveProperty('email', testUser.email);
    });

    it('should return 404 for non-existent user', async () => {
      const res = await request(app)
        .get(`/api/users/${uuidv4()}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toBe(404);
    });
  });

  describe('GET /api/users/ai-assistants', () => {
    it('should return AI assistants', async () => {
      const res = await request(app)
        .get('/api/users/ai-assistants')
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toBe(200);
      expect(Array.isArray(res.body)).toBe(true);
      expect(res.body.every((user: any) => user.isAI)).toBe(true);
      expect(res.body.some((user: any) => user.id === aiUser.id)).toBe(true);
    });
  });
});
