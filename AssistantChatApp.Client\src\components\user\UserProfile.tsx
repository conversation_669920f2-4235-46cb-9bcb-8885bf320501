import React from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Chip,
  Divider,
  Stack,
  Paper,
  Container,
} from "@mui/material";
import {
  Calendar as CalendarIcon,
  Bot as BotIcon,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { User } from "../../types";
import { UserAvatar } from "./UserAvatar";

interface UserProfileProps {
  user: User;
}

export const UserProfileView: React.FC<UserProfileProps> = ({ user }) => {
  const { t } = useTranslation(["common", "users"]);

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // const getAvatarContent = () => {
  //   if (user.avatar) {
  //     return <Avatar src={user.avatar} sx={{ width: 120, height: 120 }} />;
  //   }

  //   return (
  //     <Avatar sx={{ width: 120, height: 120, bgcolor: "primary.main" }}>
  //       {user.isAI ? (
  //         <SmartToyIcon sx={{ fontSize: 60 }} />
  //       ) : (
  //         user.displayName.charAt(0).toUpperCase()
  //       )}
  //     </Avatar>
  //   );
  // };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={0} sx={{ borderRadius: 2 }}>
        <Card>
          <CardContent sx={{ p: 4 }}>
            {/* Header Section */}
            <Box sx={{ textAlign: "center", mb: 4 }}>
              <UserAvatar {...user} sx={{ width: 120, height: 120 }} />

              <Typography variant="h4" component="h1" sx={{ mt: 2, mb: 1 }}>
                {user.displayName}
              </Typography>

              {user.isAI && (
                <Chip
                  icon={<BotIcon />}
                  label={t("users:ai")}
                  color="primary"
                  variant="outlined"
                />
              )}
            </Box>

            <Divider sx={{ my: 3 }} />

            {/* Profile Details */}
            <Stack spacing={3}>
              <Box>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  {t("common:details")}
                </Typography>

                <Stack spacing={2}>
                  {/* User ID */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      {t("users:user_id")}
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        fontFamily: "monospace",
                        bgcolor: "background.paper",
                        p: 1,
                        borderRadius: 1,
                        wordBreak: "break-all",
                      }}
                    >
                      {user.id}
                    </Typography>
                  </Box>

                  {/* Display Name */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      {t("users:display_name")}
                    </Typography>
                    <Typography variant="body1">{user.displayName}</Typography>
                  </Box>

                  {/* Account Type */}
                  {/* <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      {t('users:account_type')}
                    </Typography>
                    <Typography variant="body1">
                      {user.isAI ? t('profile.ai_account') : t('profile.human_account')}
                    </Typography>
                  </Box> */}

                  {/* Created Date */}
                  <Box>
                    <Typography
                      variant="subtitle2"
                      color="text.secondary"
                      sx={{ display: "flex", alignItems: "center", gap: 1 }}
                    >
                      <CalendarIcon fontSize="small" />
                      {t("users:joinedAt")}
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(user.createdAt)}
                    </Typography>
                  </Box>
                </Stack>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Paper>
    </Container>
  );
};
