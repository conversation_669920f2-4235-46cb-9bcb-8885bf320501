/* eslint-disable react-refresh/only-export-components */
import React from "react";
import { useShallow } from "zustand/react/shallow";
import type { ExtraProps } from "react-markdown";
import { v4 as uuidv4 } from "uuid";
import type { Element as HastNode } from "hast";
import { useComponentRegistryStore } from "./componentRegistryStore";
import type {
  ComponentAction,
  ComponentRegistration,
  ComponentInstanceActions,
} from "./types";
import {
  tryParseJsonObject,
  tryExtractTextFromRootOrFirstChild,
} from "../utils";
import { Map } from "immutable";

type RegisteredComponentProps<
  TComponentName extends string = string,
  TActionName extends string = string,
  TData = unknown,
  TOutputData = unknown
> = {
  componentName: TComponentName;
  children?: React.ReactNode;
  node?: HastNode;
  onOutputAction: (
    finalAction: ComponentAction<TComponentName, TActionName, TOutputData>
  ) => void;
  registration: ComponentRegistration<
    TComponentName,
    TActionName,
    object,
    TData,
    TOutputData
  >;
  [key: string]: unknown;
};

const usePropsParsedFromChildren = (children: React.ReactNode) =>
  React.useMemo(() => {
    const text = tryExtractTextFromRootOrFirstChild(children);
    if (text) {
      return tryParseJsonObject(text);
    }
    return undefined;
  }, [children]);

function RegisteredComponent<
  TComponentName extends string = string,
  TActionName extends string = string,
  TData = unknown,
  TOutputData = unknown
>({
  componentName,
  children,
  registration,
  onOutputAction,
  ...props
}: RegisteredComponentProps<TComponentName, TActionName, TData, TOutputData>) {
  const instanceId = React.useMemo(() => uuidv4(), []);
  const Component = registration.component;

  // Build actions object by composing each registered action data transformation function with the provided `onOutputAction` callback. The built actions object then gets passed to the registered component instantiation.
  const actions = React.useMemo(() => {
    const actionDataMappers = registration.actionDataMappers;
    const actions = {} as ComponentInstanceActions<TActionName, TData>;
    if (!actionDataMappers) {
      return actions;
    }
    const dataMappersMap = Map(actionDataMappers);
    for (const [actionName, actionDataMapper] of dataMappersMap) {
      actions[actionName as TActionName] = (data) => {
        const actionOutputData = actionDataMapper({
          componentName,
          type: actionName,
          payload: data,
          componentId: instanceId,
        });
        onOutputAction({
          componentName,
          type: actionName,
          componentId: instanceId,
          payload: actionOutputData,
        });
      };
    }
    return actions;
  }, [
    componentName,
    instanceId,
    registration.actionDataMappers,
    onOutputAction,
  ]);

  const propsFromChildren = usePropsParsedFromChildren(children);

  // Merge props from directive attributes and parsed from children
  const finalProps = React.useMemo(
    () => ({
      ...props,
      ...propsFromChildren,
    }),
    [props, propsFromChildren]
  );

  return (
    <Component actions={actions} {...finalProps}>
      {children}
    </Component>
  );
}

export function useComponentsFromRegistry<TOutputData = unknown>(
  submit: (data: TOutputData) => void
) {
  const registrations = useComponentRegistryStore(
    useShallow((s) => s.components)
  );
  // const applyAction = useComponentRegistryStore((s) => s.applyAction);

  // It's crucial not to call `applyAction` here otherwise it will cause in a additional redundant handling logic execution
  const handleAction = React.useCallback(
    (action: ComponentAction) => {
      submit(action.payload as TOutputData);
    },
    [submit]
  );

  const components = React.useMemo(() => {
    const result: Record<string, React.ComponentType<ExtraProps>> = {};
    for (const [name, registration] of registrations) {
      result[name] = (props) => (
        <RegisteredComponent
          componentName={name}
          registration={registration}
          onOutputAction={handleAction}
          {...props}
        />
      );
    }
    return result;
  }, [handleAction, registrations]);

  return components;
}
