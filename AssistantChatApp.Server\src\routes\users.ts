import { Router } from 'express';
import { getUsers<PERSON>ontroller, getUserByIdController, getAIAssistantsController } from '../controllers/users';
import { validate } from '../middleware/validation';
import { getUsersSchema, getUserByIdSchema } from '../validation/users';
import { authenticate } from '../auth/middleware';

const router = Router();

// All routes require authentication
router.use(authenticate);

// GET /users - Get users with pagination and search
router.get('/', validate(getUsersSchema), getUsersController);

// GET /users/ai-assistants - Get AI assistants
router.get('/ai-assistants', getAIAssistantsController);

// GET /users/:userId - Get user by ID
router.get('/:userId', validate(getUserByIdSchema), getUserByIdController);

export default router;
