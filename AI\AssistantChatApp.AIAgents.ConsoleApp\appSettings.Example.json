{"AppSettings": {"EnableConsoleIO": true, "DefaultAgentId": "AgentUser1"}, "ApiProviders": {"TogetherAI": {"ApiEndpoint": "https://api.together.xyz/v1/", "ApiKey": "<API-key>"}, "OpenRouter": {"ApiEndpoint": "https://openrouter.ai/api/v1/", "ApiKey": "<API-key>"}}, "Agents": {"AgentUser1": {"Name": "Agent User #1", "ProviderId": "TogetherAI", "ModelId": "meta-llama/Llama-3.2-3B-Instruct-Turbo", "MainSystemPromptFilePath": "path/to/kb/main-system-prompt.txt", "KbSettings": {"Enabled": true, "RootDirPath": "path/to/kb/directory"}, "KernelMemoryPluginSettings": {"Enabled": true, "SearchSettings": {"Limit": 10, "MinRelevance": 0.5}}}}, "KernelMemoryAccessSettings": {"KernelMemoryServiceUrl": "http://kernel-memory-service"}, "ChatAppSettings": {"BaseUrl": "https://localhost:5001"}, "Otlp": {"EnableSemanticKernelTrace": true}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Debug", "Serilog.Sinks.OpenTelemetry"], "MinimumLevel": {"Default": "Information"}, "Enrich": ["FromLogContext"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/log-.log"}}, {"Name": "Debug"}, {"Name": "OpenTelemetry"}]}}