module AssistantChatApp.AIAgents.OpenRouter

open System
open System.Collections.Generic
open System.Net.Http
open System.Threading
open System.Threading.Tasks
open OpenRouterClient.Library.Models
open OpenRouterClient.Library.Services
open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.ChatCompletion
open Microsoft.Extensions.Options
open Microsoft.SemanticKernel.Connectors.OpenAI

let [<Literal>] ApiEndpoint = "https://openrouter.ai/api/v1/"

type OpenRouterPromptExecutionSettings() =
    inherit Microsoft.SemanticKernel.Connectors.OpenAI.OpenAIPromptExecutionSettings()


let createOpenRouterOpenAIChatCompletionService modelId apiKey =
    let endpoint = Uri ApiEndpoint
    OpenAIChatCompletionService(modelId, endpoint, apiKey = apiKey)


type OpenRouterCompletionService(options: IOptions<OpenRouterOptions>, httpClientFactory: IHttpClientFactory) =

    let http = httpClientFactory.CreateClient("OpenRouterHttpClient")

    let client = new OpenRouterService(options, http)

    member this.GetChatMessageContentsAsync(
            chatHistory: ChatHistory,
            (s as executionSettings: OpenRouterPromptExecutionSettings),
            kernel: Kernel,
            cancellationToken: CancellationToken
        ): Task<IReadOnlyList<ChatMessageContent>>
        =
        task {
            let r = OpenRouterClient.Library.Models.ChatCompletionCreateRequest()
            r.FrequencyPenalty <- s.FrequencyPenalty
            r.IncludeReasoning <- not <| isNull s.ReasoningEffort
            //r.LogitBias <- s.
            return []
        }

    interface IChatCompletionService with
        member this.GetChatMessageContentsAsync(
                chatHistory: ChatHistory,
                (s as executionSettings: PromptExecutionSettings),
                kernel: Kernel,
                cancellationToken: CancellationToken
            ): Task<IReadOnlyList<ChatMessageContent>>
            = 
            task {
                let request = OpenRouterClient.Library.Models.ChatCompletionCreateRequest()
                
                return []
            }

        member this.GetStreamingChatMessageContentsAsync (chatHistory: ChatHistory, executionSettings: PromptExecutionSettings, kernel: Kernel, cancellationToken: System.Threading.CancellationToken): System.Collections.Generic.IAsyncEnumerable<StreamingChatMessageContent> = 
            raise (System.NotImplementedException())

        member this.Attributes
            with get (): System.Collections.Generic.IReadOnlyDictionary<string,obj> =
                raise (System.NotImplementedException())


    interface IDisposable with
        member this.Dispose() =
            client.Dispose()
