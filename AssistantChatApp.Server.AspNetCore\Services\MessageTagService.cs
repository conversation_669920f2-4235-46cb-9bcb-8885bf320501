using AssistantChatApp.Server.AspNetCore.Data;
using AssistantChatApp.Server.AspNetCore.Models;
using Microsoft.EntityFrameworkCore;

namespace AssistantChatApp.Server.AspNetCore.Services;

public static class TagKeys
{
    public const string Tool = "tool";
}

public static class MessageTags {
    private static DTOs.TagDto Tag(string id, string name) => new() { Id = id, Name = name };

    public static readonly IReadOnlyDictionary<string, DTOs.TagDto> TagDict =
        new Dictionary<string, DTOs.TagDto> {
            { TagKeys.Tool, Tag("b4856b4e-b0db-4356-b1d8-afb27b158476", "tool") }
        };

    public static DTOs.TagDto[] GetAll() => TagDict.Values.ToArray();
    public static DTOs.TagDto GetByKey(string key) => TagDict[key];
    public static DTOs.TagDto[] GetByKeys(string[] keys) => keys.Select(k => TagDict[k]).ToArray();
}

public static class MessageTagSets
{
    /// <summary>
    /// Tags for internal use, i.e. not intended to be set and viewed by the end users.
    /// </summary>
    public static readonly DTOs.TagDto[] SystemTags =
        MessageTags.GetByKeys([TagKeys.Tool]);
}

public class MessageTagService(ApplicationDbContext dbContext)
{
    public async Task<Tag> GetOrCreateTagByName(string tagName)
    {
        var tag = await dbContext.Tags.FirstOrDefaultAsync(t => t.Name == tagName);
        if (tag is null)
        {
            tag = new Tag { Name = tagName };
            await dbContext.Tags.AddAsync(tag);
            await dbContext.SaveChangesAsync();
        }
        return tag;
    }

    public async Task EnsureTagsExist(DTOs.TagDto[] tags)
    {
        foreach (var tagDto in tags)
        {
            var exists = await dbContext.Tags.AnyAsync(t => t.Id == tagDto.Id);
            if (!exists)
            {
                var tag = new Tag { Id = tagDto.Id, Name = tagDto.Name };
                await dbContext.Tags.AddAsync(tag);
            }
        }
        await dbContext.SaveChangesAsync();
    }
    public async Task EnsureSystemTagsExist() =>
        await EnsureTagsExist(MessageTagSets.SystemTags);
}
