import {
    Link as TanStackLink,
    LinkProps as TanStackLinkProps,
    ActiveLinkOptions
} from "@tanstack/react-router";
import { Link as MUILink, LinkProps as MUILinkProps } from "@mui/material";
import React from "react";

// Combine the props from both Link components
type CustomLinkProps = ActiveLinkOptions & {
    muiProps?: MUILinkProps;
    children?: React.ReactNode;
}

export const Link: React.FC<CustomLinkProps> = ({
    to,
    children,
    muiProps,
    ...restProps
}) => {
    return (
        <TanStackLink to={to} {...restProps}>
            {({ isActive }) => (
                <MUILink
                    component="span"
                    underline={isActive ? "always" : "hover"}
                    sx={(t) => ({
                        color: isActive ? t.palette.action.active : "inherit", // Change color when active
                    })}
                    {...muiProps}
                >
                    {children}
                </MUILink>
            )}
        </TanStackLink>
    );
};
