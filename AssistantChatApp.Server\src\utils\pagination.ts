import { PaginatedResponse } from '../types';

/**
 * Create a paginated response
 * @param data Array of items
 * @param total Total number of items
 * @param page Current page number
 * @param limit Items per page
 * @returns Paginated response object
 */
export function createPaginatedResponse<T>(
  data: T[],
  total: number,
  page: number,
  limit: number
): PaginatedResponse<T> {
  return {
    data,
    total,
    page,
    limit,
  };
}

/**
 * Parse pagination parameters from query string
 * @param query Express request query object
 * @returns Pagination parameters
 */
export function parsePaginationParams(query: any): { page: number; limit: number } {
  const page = parseInt(query.page as string) || 1;
  const limit = parseInt(query.limit as string) || 20;
  
  return {
    page: Math.max(1, page), // Ensure page is at least 1
    limit: Math.min(Math.max(1, limit), 100), // Ensure limit is between 1 and 100
  };
}

/**
 * Calculate offset for SQL queries based on page and limit
 * @param page Page number
 * @param limit Items per page
 * @returns Offset value
 */
export function calculateOffset(page: number, limit: number): number {
  return (page - 1) * limit;
}
