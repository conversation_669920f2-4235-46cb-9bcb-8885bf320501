import { User, Chat, Message, PaginatedResponse, LoginResponse, ChatFilters, CreateOrUpdateChatRequest, ApiClient, SendMessageResponse, MessageAdditionalContent } from '../types';

const emptyAdditionalMessageContent: MessageAdditionalContent = {
  functionCalls: [],
  functionResults: []
}

// In-memory data stores
const users: User[] = [
  {
    id: 'user1',
    displayName: '<PERSON>',
    avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg',
    createdAt: new Date().toISOString(),
    isAI: false
  },
  {
    id: 'user2',
    displayName: '<PERSON>',
    avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg',
    createdAt: new Date().toISOString(),
    isAI: false
  },
  {
    id: 'ai1',
    displayName: 'AI Assistant',
    avatar: 'https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg',
    createdAt: new Date().toISOString(),
    isAI: true
  }
];

let chats: Chat[] = [
  {
    id: 'chat1',
    title: 'General Discussion',
    description: 'A place for general conversation',
    participants: [users[0], users[1]],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    tags: ['general', 'team'],
  },
  {
    id: 'chat2',
    title: 'AI Assistant Chat',
    description: 'Get help from our AI assistant',
    participants: [users[0], users[2]],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    tags: ['ai', 'help'],
  }
];

let messages: Message[] = [
  {
    id: 'msg1',
    chatId: 'chat1',
    author: users[0],
    content: 'Hello everyone! How are you doing today?',
    otherContent: emptyAdditionalMessageContent,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'msg2',
    chatId: 'chat1',
    author: users[1],
    content: 'Hi! I\'m doing great, thanks for asking!',
    otherContent: emptyAdditionalMessageContent,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'msg3',
    chatId: 'chat2',
    author: users[0],
    content: 'Hey AI Assistant, can you help me with something?',
    otherContent: emptyAdditionalMessageContent,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'msg4',
    chatId: 'chat2',
    author: users[2],
    content: 'Of course! I\'m here to help. What do you need assistance with?',
    otherContent: emptyAdditionalMessageContent,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Update last messages for chats
chats[0].lastMessage = messages[1];
chats[1].lastMessage = messages[3];

// Helper functions
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
const generateId = () => Math.random().toString(36).substr(2, 9);

// Mock API implementation
export const mockApi: ApiClient = {
  login: async (email: string, password: string): Promise<LoginResponse> => {
    await delay(500); // Simulate network delay
    const user = users[0]; // Always return first user for demo
    return {
      token: 'mock_jwt_token',
      user
    };
  },

  getMe: async (): Promise<User> => {
    await delay(300);
    return users[0];
  },

  getChats: async (filters: ChatFilters = {}): Promise<PaginatedResponse<Chat>> => {
    await delay(500);
    let filteredChats = [...chats];

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredChats = filteredChats.filter(chat =>
        chat.title.toLowerCase().includes(searchLower) ||
        chat.description?.toLowerCase().includes(searchLower)
      );
    }

    if (filters.tags?.length) {
      filteredChats = filteredChats.filter(chat =>
        filters.tags!.some(tag => chat.tags?.includes(tag))
      );
    }

    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const start = (page - 1) * limit;
    const end = start + limit;

    return {
      data: filteredChats.slice(start, end),
      total: filteredChats.length,
      page,
      limit
    };
  },

  getChat: async (chatId: string): Promise<Chat> => {
    await delay(300);
    const chat = chats.find(c => c.id === chatId);
    if (!chat) throw new Error('Chat not found');
    return chat;
  },

  createChat: async (data: CreateOrUpdateChatRequest): Promise<Chat> => {
    await delay(500);
    const participants = data.participants.map(id => users.find(u => u.id === id)!) || [users[0]];
    const newChat: Chat = {
      id: generateId(),
      title: data.title || 'New Chat',
      description: data.description,
      participants: participants,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tags: data.tags || []
    };
    chats.push(newChat);
    return newChat;
  },

  updateChat: async (chatId: string, data: CreateOrUpdateChatRequest): Promise<Chat> => {
    await delay(500);
    const index = chats.findIndex(c => c.id === chatId);
    if (index === -1) throw new Error('Chat not found');

    chats[index] = {
      ...chats[index],
      title: data.title,
      description: data.description,
      tags: data.tags,
      participants: users.filter(u => data.participants.includes(u.id)),
      updatedAt: new Date().toISOString()
    };
    return chats[index];
  },

  deleteChat: async (chatId: string): Promise<void> => {
    await delay(500);
    chats = chats.filter(c => c.id !== chatId);
    messages = messages.filter(m => m.chatId !== chatId);
  },

  getMessages: async (chatId: string, page = 1, limit = 50): Promise<PaginatedResponse<Message>> => {
    await delay(300);
    const chatMessages = messages.filter(m => m.chatId === chatId);
    const start = (page - 1) * limit;
    const end = start + limit;

    return {
      data: chatMessages.slice(start, end),
      total: chatMessages.length,
      page,
      limit
    };
  },

  getMessagesByIds: async (
    chatId: string,
    messageIds: string[]
  ) => {
    await delay(300);
    const chatMessages = messages.filter(m => m.chatId === chatId && messageIds.includes(m.id));
    return { data: chatMessages };
  },

  sendMessage: async (chatId: string, content: string, mentions: string[] = []): Promise<SendMessageResponse> => {
    await delay(300);
    const newMessage: Message = {
      id: generateId(),
      chatId,
      author: users[0], // Current user
      content,
      otherContent: emptyAdditionalMessageContent,
      mentions,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    messages.push(newMessage);

    // Update chat's last message
    const chatIndex = chats.findIndex(c => c.id === chatId);
    if (chatIndex !== -1) {
      chats[chatIndex].lastMessage = newMessage;
      chats[chatIndex].updatedAt = new Date().toISOString();
    }

    return { originalMessage: newMessage, responseMessages: [] };
  },

  updateMessage: async (chatId: string, messageId: string, content: string, mentions: string[] = []): Promise<Message> => {
    await delay(300);
    const index = messages.findIndex(m => m.id === messageId && m.chatId === chatId);
    if (index === -1) throw new Error('Message not found');

    messages[index] = {
      ...messages[index],
      content,
      mentions,
      updatedAt: new Date().toISOString()
    };
    return messages[index];
  },

  deleteMessage: async (chatId: string, messageId: string): Promise<void> => {
    await delay(300);
    messages = messages.filter(m => !(m.id === messageId && m.chatId === chatId));
  },

  getUsers: async (page = 1, limit = 20, search = ''): Promise<PaginatedResponse<User>> => {
    await delay(300);
    let filteredUsers = [...users];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = filteredUsers.filter(user =>
        user.displayName.toLowerCase().includes(searchLower)
      );
    }

    const start = (page - 1) * limit;
    const end = start + limit;

    return {
      data: filteredUsers.slice(start, end),
      total: filteredUsers.length,
      page,
      limit
    };
  },

  getUser: async (userId: string): Promise<User> => {
    await delay(300);
    const user = users.find(u => u.id === userId);
    if (!user) throw new Error('User not found');
    return user;
  },

  getAIAssistants: async (): Promise<User[]> => {
    await delay(300);
    return users.filter(u => u.isAI);
  }
};
