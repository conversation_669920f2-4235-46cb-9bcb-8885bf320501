/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react";
import type { ExtraProps } from "react-markdown";
import { v4 as uuidv4 } from "uuid";
// import type { Components } from "react-markdown"
import type { Element as HastNode } from "hast";
import { useShallow } from "zustand/react/shallow";
import {
  tryParseJsonObject,
  tryExtractTextFromRootOrFirstChild,
} from "../utils";
import type { ComponentAction, ComponentContext, ComponentName } from "./types";
import {
  useComponentRegistryStore,
  type ComponentRegistry,
} from "./componentRegistryStore";

// interface InteractiveMarkdownProps {
//   componentRegistry: ComponentRegistry
//   children: string
//   messageId: string
//   onComponentAction?: (action: ComponentAction<unknown>) => void
// }

interface RegisteredComponentProps<
  TComponentName extends string = ComponentName,
  TData = unknown
> {
  // componentRegistry: ComponentRegistrySelectors & ComponentRegistryCommands;
  componentName: string;
  messageId: string;
  handleAction: (
    action: Omit<
      ComponentAction<TComponentName, TData>,
      "messageId" | "componentId"
    >,
    componentId: string
  ) => void;
  handleUpdate: (data: TData, componentId: string) => void;
  children?: React.ReactNode;
  node?: HastNode;
  [key: string]: unknown;
}

const usePropsParsedFromChildren = (children: React.ReactNode) =>
  React.useMemo(() => {
    const text = tryExtractTextFromRootOrFirstChild(children);
    if (text) {
      return tryParseJsonObject(text);
    }
    return undefined;
  }, [children]);

// eslint-disable-next-line react-refresh/only-export-components
const RegisteredComponent: React.FC<RegisteredComponentProps> = (props) => {
  const {
    // componentRegistry,
    messageId,
    componentName,
    handleAction,
    handleUpdate,
    children,
    node,
    ...otherProps
  } = props;
  const componentId = React.useMemo(() => uuidv4(), []);

  const componentRegistry = useComponentRegistryStore<
    Pick<
      ComponentRegistry,
      "initializeComponent" | "getComponentState" | "getComponent"
    >
  >(
    useShallow((s) => ({
      initializeComponent: s.initializeComponent,
      getComponentState: s.getComponentState,
      getComponent: s.getComponent,
    }))
  );

  // Initialize component if needed
  React.useEffect(() => {
    componentRegistry.initializeComponent(
      messageId,
      componentId,
      componentName
    );
  }, [componentId, componentName, messageId, componentRegistry]);

  const registration = componentRegistry.getComponent(componentName);

  const context: ComponentContext = {
    messageId,
    componentId,
    onAction: (action) => handleAction(action, componentId),
    onUpdate: (data) => handleUpdate(data, componentId),
    data: componentRegistry.getComponentState(messageId, componentId),
  };

  const propsFromChildren = usePropsParsedFromChildren(children);

  // Merge props from directive attributes and parsed from children
  const finalProps = React.useMemo(
    () => ({
      ...otherProps,
      ...propsFromChildren,
    }),
    [otherProps, propsFromChildren]
  );

  if (!registration) return null;

  const Component = registration.component;
  return (
    <Component {...finalProps} context={context}>
      {children}
    </Component>
  );
};

export function useComponentsFromRegistry(
  messageId: string,
  onComponentAction?: (action: ComponentAction) => void
) {
  const {
    cleanup,
    setActionHandler,
    handleAction,
    setComponentState,
  } = useComponentRegistryStore(
    useShallow((s) => ({
      cleanup: s.cleanup,
      setActionHandler: s.setActionHandler,
      handleAction: s.handleAction,
      setComponentState: s.setComponentState
    }))
  );

  const componentKeys = useComponentRegistryStore(useShallow((s) => s.state.components.keys()));
  // Set up action handler
  React.useEffect(() => {
    //console.debug("Setting up action handler", messageId, componentRegistry)
    if (onComponentAction) {
      setActionHandler(messageId, onComponentAction);
    }
    return () => {
      cleanup(messageId);
    };
  }, [messageId, onComponentAction, setActionHandler, cleanup]);

  const handleComponentAction = React.useCallback(
    (
      action: Omit<ComponentAction, "messageId" | "componentId">,
      componentId: string
    ) => {
      //console.debug("On component action", messageId, componentId, action, componentRegistry)

      const fullAction: ComponentAction = {
        ...action,
        messageId,
        componentId,
      };
      handleAction(fullAction);
    },
    [messageId, handleAction]
  );

  const handleUpdate = React.useCallback(
    (data: unknown, componentId: string) => {
      setComponentState(messageId, componentId, data);
    },
    [messageId, setComponentState]
  );

  const componentsMap = React.useMemo(() => {
    const result: { [key: string]: React.ComponentType<object & ExtraProps> } =
      {};

    // Create wrapper components for each registered component
    const registeredComponents = Array.from(componentKeys);

    registeredComponents.forEach((componentName) => {
      console.debug("Registering component", componentName);
      result[componentName] = (props) => (
        <RegisteredComponent
          // componentRegistry={componentRegistry}
          componentName={componentName}
          messageId={messageId}
          handleAction={handleComponentAction}
          handleUpdate={handleUpdate}
          {...props}
        />
      );
    });
    console.debug("Registered components found: ", result);
    return result;
  }, [handleUpdate, messageId, handleComponentAction, componentKeys]);

  return componentsMap;
}
