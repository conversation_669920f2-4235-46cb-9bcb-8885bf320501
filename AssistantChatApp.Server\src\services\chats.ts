import { PgTable } from 'drizzle-orm/pg-core';
import { db } from '../db';
import { chats, chatParticipants, users, messages, tags, chatTags } from '../db/schema';
import { Chat, ChatFilters, PaginatedResponse, SafeUser } from '../types';
import { NotFoundError, ForbiddenError } from '../utils/errors';
import { createPaginatedResponse, calculateOffset } from '../utils/pagination';
import  type { SQL, SQLWrapper } from "drizzle-orm";
import { eq, and, like, inArray, gte, lte, desc, sql } from 'drizzle-orm';
import { alias } from 'drizzle-orm/pg-core';

const buildChatQueryFilters = (filters: ChatFilters) => {
    const queryFilters: SQLWrapper[] = [];

    // Apply filters
    if (filters.search) {
        queryFilters.push(
            or(
                like(chats.title, `%${filters.search}%`),
                like(chats.description, `%${filters.search}%`)
            )
        );
    }

    if (filters.tags && filters.tags.length > 0) {
        // queryFilters.push(sql`array_agg(${tags.name}) && array[${filters.tags.join(',')}]`);
    }

    if (filters.participants && filters.participants.length > 0) {
        queryFilters.push(inArray(chatParticipants.userId, filters.participants));
    }

    if (filters.startDate) {
        queryFilters.push(gte(chats.createdAt, new Date(filters.startDate)));
    }

    if (filters.endDate) {
        queryFilters.push(lte(chats.createdAt, new Date(filters.endDate)));
    }

    return and(...queryFilters);
}

/**
 * Get chats with pagination and filters
 * @param userId Current user ID
 * @param filters Chat filters
 * @returns Paginated list of chats
 */
export async function getChats(
  userId: string,
  filters: ChatFilters
): Promise<PaginatedResponse<Chat>> {
  const { page = 1, limit = 20 } = filters;
  const offset = calculateOffset(page, limit);

  // Create aliases for joined tables
  const lastMessageAlias = alias(messages, 'lastMessage');
  const authorAlias = alias(users, 'author');

  // const chatFilterExpr = buildChatQueryFilters(filters);

  // Build query
  let query = db
    .select({
      chat: chats,
      participants: sql<SafeUser[]>`json_agg(distinct jsonb_build_object(
        'id', ${users.id},
        'email', ${users.email},
        'displayName', ${users.displayName},
        'avatar', ${users.avatar},
        'createdAt', ${users.createdAt},
        'isAI', ${users.isAI}
      ))`,
      tags: sql<string[]>`array_agg(distinct ${tags.name})`,
      lastMessage: sql<any>`json_build_object(
        'id', ${lastMessageAlias.id},
        'chatId', ${lastMessageAlias.chatId},
        'content', ${lastMessageAlias.content},
        'createdAt', ${lastMessageAlias.createdAt},
        'updatedAt', ${lastMessageAlias.updatedAt},
        'author', json_build_object(
          'id', ${authorAlias.id},
          'email', ${authorAlias.email},
          'displayName', ${authorAlias.displayName},
          'avatar', ${authorAlias.avatar},
          'createdAt', ${authorAlias.createdAt},
          'isAI', ${authorAlias.isAI}
        )
      )`
    })
    .from(chats)
    .innerJoin(chatParticipants, eq(chats.id, chatParticipants.chatId))
    .innerJoin(users, eq(chatParticipants.userId, users.id))
    .leftJoin(chatTags, eq(chats.id, chatTags.chatId))
    .leftJoin(tags, eq(chatTags.tagId, tags.id))
    .leftJoin(
      lastMessageAlias,
      and(
        eq(chats.id, lastMessageAlias.chatId),
        sql`${lastMessageAlias.id} = (
          SELECT id FROM messages
          WHERE chat_id = ${chats.id}
          ORDER BY created_at DESC
          LIMIT 1
        )`
      )
    )
    .leftJoin(authorAlias, eq(lastMessageAlias.authorId, authorAlias.id))
    .where(eq(chatParticipants.userId, userId))
    // .where(chatFilterExpr)
    .groupBy(chats.id, lastMessageAlias.id, authorAlias.id);

  // console.debug("`getChats` SQL: ", query.toSQL().sql);

  // Get total count
  const countResult = await db
    .select({ count: sql<number>`count(distinct ${chats.id})` })
    .from(chats)
    .innerJoin(chatParticipants, eq(chats.id, chatParticipants.chatId))
    .where(eq(chatParticipants.userId, userId))
    .execute();

  const total = countResult[0]?.count || 0;

  // Get paginated results
  const results = await query
    .orderBy(desc(chats.updatedAt))
    .limit(limit)
    .offset(offset)
    .execute();

  // Format results
  const formattedChats: Chat[] = results.map((row) => ({
    id: row.chat.id,
    title: row.chat.title,
    description: row.chat.description ?? undefined,
    participants: row.participants,
    createdAt: row.chat.createdAt,
    updatedAt: row.chat.updatedAt,
    tags: row.tags.filter(Boolean), // Remove null values
    lastMessage: row.lastMessage.id ? row.lastMessage : undefined,
  }));

  return createPaginatedResponse(formattedChats, total, page, limit);
}

/**
 * Get a chat by ID
 * @param chatId Chat ID
 * @param userId Current user ID
 * @returns Chat data
 */
export async function getChatById(chatId: string, userId: string): Promise<Chat> {
  // Check if user is a participant
  const participantCheck = await db
    .select()
    .from(chatParticipants)
    .where(and(
      eq(chatParticipants.chatId, chatId),
      eq(chatParticipants.userId, userId)
    ))
    .limit(1);

  if (!participantCheck.length) {
    throw new ForbiddenError('You are not a participant in this chat');
  }

  // Get chat with participants and tags
  const result = await db
    .select({
      chat: chats,
      participants: sql<SafeUser[]>`json_agg(distinct jsonb_build_object(
        'id', ${users.id},
        'email', ${users.email},
        'displayName', ${users.displayName},
        'avatar', ${users.avatar},
        'createdAt', ${users.createdAt},
        'isAI', ${users.isAI}
      ))`,
      tags: sql<string[]>`array_agg(distinct ${tags.name})`
    })
    .from(chats)
    .innerJoin(chatParticipants, eq(chats.id, chatParticipants.chatId))
    .innerJoin(users, eq(chatParticipants.userId, users.id))
    .leftJoin(chatTags, eq(chats.id, chatTags.chatId))
    .leftJoin(tags, eq(chatTags.tagId, tags.id))
    .where(eq(chats.id, chatId))
    .groupBy(chats.id)
    .limit(1);

  if (!result.length) {
    throw new NotFoundError('Chat not found');
  }

  const chatData = result[0];

  // Get last message
  const lastMessageResult = await db
    .select({
      message: messages,
      author: users
    })
    .from(messages)
    .innerJoin(users, eq(messages.authorId, users.id))
    .where(eq(messages.chatId, chatId))
    .orderBy(desc(messages.createdAt))
    .limit(1);

  // Format chat data
  return {
    id: chatData.chat.id,
    title: chatData.chat.title,
    description: chatData.chat.description ?? undefined,
    participants: chatData.participants,
    createdAt: chatData.chat.createdAt,
    updatedAt: chatData.chat.updatedAt,
    tags: chatData.tags.filter(Boolean), // Remove null values
    lastMessage: lastMessageResult.length
      ? {
          id: lastMessageResult[0].message.id,
          chatId: lastMessageResult[0].message.chatId,
          author: {
            id: lastMessageResult[0].author.id,
            email: lastMessageResult[0].author.email,
            displayName: lastMessageResult[0].author.displayName,
            avatar: lastMessageResult[0].author.avatar ?? undefined,
            createdAt: lastMessageResult[0].author.createdAt,
            isAI: lastMessageResult[0].author.isAI,
          },
          content: lastMessageResult[0].message.content,
          createdAt: lastMessageResult[0].message.createdAt,
          updatedAt: lastMessageResult[0].message.updatedAt,
        }
      : undefined,
  };
}

/**
 * Create a new chat
 * @param data Chat data
 * @param userId Current user ID
 * @returns Created chat
 */
export async function createChat(
  data: {
    title: string;
    description?: string;
    participants: string[];
    tags?: string[];
  },
  userId: string
): Promise<Chat> {
  // Start a transaction
  return await db.transaction(async (tx) => {
    // Create chat
    const [newChat] = await tx
      .insert(chats)
      .values({
        title: data.title,
        description: data.description,
      })
      .returning();

    // Add participants (including current user)
    const uniqueParticipantIds = [...new Set([userId, ...data.participants])];

    for (const participantId of uniqueParticipantIds) {
      await tx
        .insert(chatParticipants)
        .values({
          chatId: newChat.id,
          userId: participantId,
        });
    }

    // Add tags if provided
    if (data.tags && data.tags.length > 0) {
      for (const tagName of data.tags) {
        // Find or create tag
        let tagId;
        const existingTag = await tx
          .select()
          .from(tags)
          .where(eq(tags.name, tagName))
          .limit(1);

        if (existingTag.length) {
          tagId = existingTag[0].id;
        } else {
          const [newTag] = await tx
            .insert(tags)
            .values({ name: tagName })
            .returning();
          tagId = newTag.id;
        }

        // Add chat-tag relationship
        await tx
          .insert(chatTags)
          .values({
            chatId: newChat.id,
            tagId,
          });
      }
    }

    // Get participants
    const participantsResult = await tx
      .select()
      .from(users)
      .where(inArray(users.id, uniqueParticipantIds));

    const participantsList = participantsResult.map((user) => ({
      id: user.id,
      email: user.email,
      displayName: user.displayName,
      avatar: user.avatar ?? undefined,
      createdAt: user.createdAt,
      isAI: user.isAI,
    }));

    // Return formatted chat
    return {
      id: newChat.id,
      title: newChat.title,
      description: newChat.description ?? undefined,
      participants: participantsList,
      createdAt: newChat.createdAt,
      updatedAt: newChat.updatedAt,
      tags: data.tags,
    };
  });
}

/**
 * Update a chat
 * @param chatId Chat ID
 * @param data Chat data to update
 * @param userId Current user ID
 * @returns Updated chat
 */
export async function updateChat(
  chatId: string,
  data: {
    title?: string;
    description?: string;
    participants?: string[];
    tags?: string[];
  },
  userId: string
): Promise<Chat> {
  // Check if user is a participant
  const participantCheck = await db
    .select()
    .from(chatParticipants)
    .where(and(
      eq(chatParticipants.chatId, chatId),
      eq(chatParticipants.userId, userId)
    ))
    .limit(1);

  if (!participantCheck.length) {
    throw new ForbiddenError('You are not a participant in this chat');
  }

  // Start a transaction
  return await db.transaction(async (tx) => {
    // Update chat
    const updateData: Partial<typeof chats.$inferInsert> = {
      updatedAt: new Date(),
    };

    if (data.title !== undefined) updateData.title = data.title;
    if (data.description !== undefined) updateData.description = data.description;

    const [updatedChat] = await tx
      .update(chats)
      .set(updateData)
      .where(eq(chats.id, chatId))
      .returning();

    // Update participants if provided
    if (data.participants) {
      // Get current participants
      const currentParticipants = await tx
        .select()
        .from(chatParticipants)
        .where(eq(chatParticipants.chatId, chatId));

      const currentParticipantIds = currentParticipants.map((p) => p.userId);

      // Ensure current user remains a participant
      const newParticipantIds = [...new Set([userId, ...data.participants])];

      // Remove participants not in the new list
      const participantsToRemove = currentParticipantIds.filter(
        (id) => !newParticipantIds.includes(id)
      );

      if (participantsToRemove.length > 0) {
        await tx
          .delete(chatParticipants)
          .where(and(
            eq(chatParticipants.chatId, chatId),
            inArray(chatParticipants.userId, participantsToRemove)
          ));
      }

      // Add new participants
      const participantsToAdd = newParticipantIds.filter(
        (id) => !currentParticipantIds.includes(id)
      );

      for (const participantId of participantsToAdd) {
        await tx
          .insert(chatParticipants)
          .values({
            chatId,
            userId: participantId,
          });
      }
    }

    // Update tags if provided
    if (data.tags) {
      // Remove current tags
      await tx
        .delete(chatTags)
        .where(eq(chatTags.chatId, chatId));

      // Add new tags
      for (const tagName of data.tags) {
        // Find or create tag
        let tagId;
        const existingTag = await tx
          .select()
          .from(tags)
          .where(eq(tags.name, tagName))
          .limit(1);

        if (existingTag.length) {
          tagId = existingTag[0].id;
        } else {
          const [newTag] = await tx
            .insert(tags)
            .values({ name: tagName })
            .returning();
          tagId = newTag.id;
        }

        // Add chat-tag relationship
        await tx
          .insert(chatTags)
          .values({
            chatId,
            tagId,
          });
      }
    }

    // Get updated chat with participants and tags
    return await getChatById(chatId, userId);
  });
}

/**
 * Delete a chat
 * @param chatId Chat ID
 * @param userId Current user ID
 */
export async function deleteChat(chatId: string, userId: string): Promise<void> {
  // Check if user is a participant
  const participantCheck = await db
    .select()
    .from(chatParticipants)
    .where(and(
      eq(chatParticipants.chatId, chatId),
      eq(chatParticipants.userId, userId)
    ))
    .limit(1);

  if (!participantCheck.length) {
    throw new ForbiddenError('You are not a participant in this chat');
  }

  // Delete chat (cascade will delete related records)
  await db
    .delete(chats)
    .where(eq(chats.id, chatId));
}

// Helper function for OR conditions
function or(...conditions: unknown[]) {
  return sql`(${conditions.join(' OR ')})`;
}
