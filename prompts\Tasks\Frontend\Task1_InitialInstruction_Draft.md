Develop a web frontend for the AI assistant chat app.

## Tech stack preferences:
- Language: TypeScript
- Package manager: pnpm
- Framework: React.js
- UI kit: MUI, aka Material UI (`@mui/material`)
- Asynchronous state management (e.g., fetched data): TanStack Query (`@tanstack/react-query`)
- Client global state management: <PERSON><PERSON> (`jotai`).
- Client routing: TanStack Router (`@tanstack/react-router`)
- Forms state management: TanStack Form (`@tanstack/react-form`)
- Rich text editor: MDXEditor (`@mdxeditor/editor`)
- **Resizable Layout**: react resizable panels (`react-resizable-panels`)

## Key notes on functionality:
- Group conversations. I.e., multiple users take part in the same conversation with <PERSON> assistant and with each other. Matter of clarification: when the AI assistant is invoked; as an option, by an explicit mention (via `@` prefix, common in many chat apps).
- Loose coupling with the backend. I.e. the backend should be abstracted away and be easily swappable. Ideally, there should be a single place (module or a set of modules) where the backend is configured and used. This way, it should possible to easily mock the backend for testing purposes.
- Client should manage and store only the necessary operational data. Basically, all the data should be fetched from the backend, and the client only caches some of it temporarily (e.g., via TanStack Query) just to provide a smoother user experience.
- There should be a log in and log out functionality with the corresponding UI. No registration/sign up functionality is required for now.
- Messages should support rich text formatting. For that, Markdown fits well. So, completed messages should be rendered as HTML. And messages in editing stage should support Markdown raw source editing, as well as WYSIWYG editing (e.g., via MDXEditor): it should be possible to switch between the two modes (in case of MDXEditor, this can be configured via `diffSourcePlugin`).
- User mentions in messages should be highlighted and interactive (at least in the completed, rendered state of the messages). I.e. on either click or hover, the user summary card popup should be shown.

## Main data entities:
- Chat. Represents a chronological sequence of messages.
  Chat's metadata attributes:
  - unique ID
  - title
  - description
  - participants
  - creation timestamp
  - last update timestamp
  - tags: a list of tags that help better categorize chats, thus facilitating search and filtering.
- Message. Represents a single message in a conversation.
  Message's metadata attributes:
  - Unique ID
  - author
  - creation timestamp
  - modification timestamp
  Besides the metadata, message has content. Currently it's just Markdown text. But in the future, it may include additional data, such as file attachments.
- User. Represents a user. It could be either a human user or an AI assistant.
  User's metadata attributes:
  - Unique ID
  - display name
  - avatar
  - creation timestamp
  - a differentiation flag to distinguish between human and AI users

