import React, { useEffect, useRef } from 'react';
import { Box, CircularProgress, Divider, Typography } from '@mui/material';
import { useParams } from '@tanstack/react-router';
import { useQuery } from '@tanstack/react-query';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';

import { chatApi } from '../api/chatApi';
import MessageList from '../components/message/MessageList';
import { MessageEditorInRealmProvider } from "../components/message/editor/MessageEditor";
import { ChatSideHeader } from '../components/chat/ChatHeader';
import ChatParticipants from '../components/chat/ChatParticipants';
import { UserSelectionForm } from "../components/message/UserSelectionForm";
import { useGetChatMessages } from "../queries/message";
import { useGetChatCompletion } from "../mutations/chat";
import { useTranslation } from "react-i18next";
import { User } from '../types';
import { AIRespondingIndicator } from "../components/message/AIRespondingIndicator";
import { MessageComponents } from "../components/message/interactiveComponents";

const ChatPage: React.FC = () => {
  const { t } = useTranslation("chats");
  const { chatId } = useParams({ from: '/chats/$chatId' });
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch chat details
  const { data: chat, isLoading: chatLoading, error: chatError } = useQuery({
    queryKey: ['chat', chatId],
    queryFn: () => chatApi.getChat(chatId),
  });

  // Fetch messages
  const { data: messagesData, isLoading: messagesLoading, error: messagesError } =
    useGetChatMessages(chatId);

  const chatCompletionMutation = useGetChatCompletion();

  const submitAIChatCompletion = async (selectedUser: User) => {
    await chatCompletionMutation.mutateAsync({
      chatId,
      aiUserId: selectedUser.id
    });
  };

  const aiUsers = React.useMemo(() => {
    return chat?.participants.filter(p => p.isAI) ?? [];
  }, [chat?.participants]);

  // Scroll to bottom whenever new messages arrive
  useEffect(() => {
    if (messagesData && !messagesLoading) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messagesData, messagesLoading]);

  MessageComponents.useRegisterMessageComponents();

  // Handle loading and error states
  if (chatLoading || messagesLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (chatError || messagesError) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography color="error">
          {t("chatLoadingError")}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ height: 'calc(100vh - 64px)', display: 'flex', flexDirection: 'column' }}>
      {chat && (
        <PanelGroup direction="horizontal" style={{ flex: 1 }}>
          {/* Main chat area */}
          <Panel defaultSize={75} minSize={50}>
            <Box sx={{
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              borderRight: '1px solid',
              borderColor: 'divider'
            }}>
              {/* Messages container */}
              <Box sx={{
                flex: 1,
                overflowY: 'auto',
                p: 2,
                display: 'flex',
                flexDirection: 'column',
              }}>
                <MessageList
                  messages={messagesData?.data || []}
                  chatId={chatId}
                />
                <div ref={messagesEndRef} />
              </Box>

              {/* Show typing indicator when AI is responding */}
              <AIRespondingIndicator
                chatParticipants={chat.participants}
                isPending={chatCompletionMutation.isPending}
                requestData={chatCompletionMutation.variables}
              />

              {/* Message composer */}
              <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
                {/* <MessageComposer chatId={chatId} /> */}
                <MessageEditorInRealmProvider chatId={chatId} />
              </Box>
            </Box>
          </Panel>

          {/* Resize handle */}
          <PanelResizeHandle style={{ width: 8 }}>
            <Box
              sx={{
                width: '4px',
                height: '100%',
                backgroundColor: 'divider',
                mx: 'auto',
                '&:hover': {
                  backgroundColor: 'primary.main',
                  cursor: 'col-resize',
                }
              }}
            />
          </PanelResizeHandle>

          {/* Participants panel */}
          <Panel defaultSize={25} minSize={15}>
            <ChatSideHeader chat={chat} />
            <Divider orientation="horizontal" />
            <UserSelectionForm
              onSubmit={submitAIChatCompletion}
              users={aiUsers}
              submitLabel={t("invoke_ai")}
              showSubmitButton={true}
            />
            <Divider orientation="horizontal" />
            <ChatParticipants participants={chat.participants} />
          </Panel>
        </PanelGroup>
    )}
    </Box>
  );
};

export default ChatPage;
