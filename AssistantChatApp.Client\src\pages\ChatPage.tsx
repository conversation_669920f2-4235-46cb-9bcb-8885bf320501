import React from "react";
import { Box, CircularProgress, Typography } from "@mui/material";
import { useParams } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import { Panel, PanelGroup } from "react-resizable-panels";
import { useAtomValue } from "jotai";
import { chatApi } from "../api/chatApi";
import { useGetChatMessages } from "../queries/message";
import { useTranslation } from "react-i18next";
// import { AIChatView } from "../components/chat/AIChatView";
import { ChatMainView } from "../components/chat/ChatMainView";
import { ChatSidePanelView } from "../components/chat/ChatSidePanel";
import { VerticalResizeHandle } from "../components/common/PanelResizeHandles";
import { chatCompletionMutationAtom } from "@/components/chat/atoms";

const ChatPage: React.FC = () => {
  const { t } = useTranslation("chats");
  const { chatId } = useParams({ from: "/chats/$chatId" });

  // Fetch chat details
  const {
    data: chat,
    isLoading: chatLoading,
    error: chatError,
  } = useQuery({
    queryKey: ["chat", chatId],
    queryFn: () => chatApi.getChat(chatId),
  });

  // Fetch messages
  const {
    data: messagesData,
    isLoading: messagesLoading,
    error: messagesError,
  } = useGetChatMessages(chatId);

  const chatCompletionMutation = useAtomValue(chatCompletionMutationAtom);

  // Handle loading and error states
  if (chatLoading || messagesLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (chatError || messagesError) {
    return (
      <Box sx={{ p: 3, textAlign: "center" }}>
        <Typography color="error">{t("chatLoadingError")}</Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        height: "calc(100vh - 64px)",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {chat && (
        <PanelGroup direction="horizontal" style={{ flex: 1 }}>
          {/* Main chat area */}
          <Panel defaultSize={80} minSize={50}>
            <ChatMainView
              chat={chat}
              messagesData={messagesData}
              areMessagesLoading={messagesLoading}
              chatCompletionMutation={chatCompletionMutation}
            />
          </Panel>

          {/* Resize handle */}
          <VerticalResizeHandle />

          {/* Participants panel */}
          <Panel
            collapsible={true}
            collapsedSize={15}
            defaultSize={20}
            minSize={15}
          >
            <ChatSidePanelView
              chat={chat}
              chatCompletionMutation={chatCompletionMutation}
            />
          </Panel>
        </PanelGroup>
      )}
    </Box>
  );
};

/*
const ChatPage1: React.FC = () => {
  const { t } = useTranslation("chats");
  const { chatId } = useParams({ from: "/chats/$chatId" });
  // Fetch chat details
  const {
    data: chat,
    isLoading: chatLoading,
    error: chatError,
  } = useQuery({
    queryKey: ["chat", chatId],
    queryFn: () => chatApi.getChat(chatId),
  });

  const aiUsers = React.useMemo(() => {
    return chat?.participants.filter((p) => p.isAI) ?? [];
  }, [chat?.participants]);

  // Handle loading and error states
  if (chatLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (chatError) {
    return (
      <Box sx={{ p: 3, textAlign: "center" }}>
        <Typography color="error">{t("chatLoadingError")}</Typography>
      </Box>
    );
  }

  return <AIChatView chatId={chatId} aiUsers={aiUsers} />;
};
*/

export default ChatPage;
