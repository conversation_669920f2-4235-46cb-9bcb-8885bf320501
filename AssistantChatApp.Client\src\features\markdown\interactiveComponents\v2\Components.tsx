import React from "react";
// import {  } from "./componentRegistry";
import type { ComponentRegistration, InteractiveComponentProps } from "./types";
import {
  type SurveyComponentProps as OriginSurveyComponentProps,
  SurveyComponent as OriginSurveyComponent,
} from "../../../../components/common/Survey";

type SurveyComponentProps = Omit<OriginSurveyComponentProps, "onSubmit">;

type SurveyComponentData = {
  selectedAnswers: string[];
};

type SurveyComponentActionName = "submit";

type ComponentTypeDefs = {
  survey: {
    actionName: SurveyComponentActionName;
    data: SurveyComponentData;
    props: SurveyComponentProps;
  };
};

export type StringOutputComponentRegistrations = {
  [K in keyof ComponentTypeDefs]: ComponentRegistration<
    K,
    ComponentTypeDefs[K]["actionName"],
    ComponentTypeDefs[K]["props"],
    ComponentTypeDefs[K]["data"],
    string | undefined
  >;
};

// type MessageComponentName = keyof MessageComponentRegistrations;

export const SurveyComponent: React.FC<
  InteractiveComponentProps<
    SurveyComponentActionName,
    SurveyComponentProps,
    SurveyComponentData
  >
> = ({ answers, multiple, question, showKeys, actions }) => {
  return (
    <OriginSurveyComponent
      answers={answers}
      multiple={multiple}
      question={question}
      showKeys={showKeys}
      onSubmit={(answers) => {
        actions.submit({ selectedAnswers: answers });
      }}
    />
  );
};
