{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Debug", "Serilog.Sinks.OpenTelemetry"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore.Hosting": "Warning", "Microsoft.AspNetCore.Mvc": "Warning", "Microsoft.AspNetCore.Routing": "Warning", "Microsoft.Extensions.Http.DefaultHttpClientFactory": "Verbose"}}, "Enrich": ["FromLogContext"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {RequestId} {SourceContext} {Message}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/log-.log", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {RequestId} {SourceContext} {Message}{NewLine}{Exception}"}}, {"Name": "Debug", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {RequestId} {SourceContext} {Message}{NewLine}{Exception}"}}, {"Name": "OpenTelemetry"}], "HttpClientLogging": {"LogMode": "LogFailures", "RequestHeaderLogMode": "LogNone", "RequestBodyLogMode": "LogFailures", "ResponseHeaderLogMode": "LogNone", "ResponseBodyLogMode": "LogFailures", "RequestBodyLogTextLengthLimit": 2000, "ResponseBodyLogTextLengthLimit": 2000, "MaskFormat": "*****", "MaskedProperties": ["*password*", "*token*", "*api_key*", "*secret*"]}}, "AllowedHosts": "*"}