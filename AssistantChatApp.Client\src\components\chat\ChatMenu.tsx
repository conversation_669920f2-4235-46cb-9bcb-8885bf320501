import React, { useState } from "react";
import {
  Menu,
  MenuItem,
  ListItemIcon,
  Divider,
} from "@mui/material";
import {
  Edit as EditIcon,
  Trash2 as TrashIcon,
} from "lucide-react";
import { Chat } from "../../types";
import { useChatFormState } from "./hooks";
import { UpdateChatFormDialog } from "./UpdateChatFormDialog";
import { DeleteConfirmationDialog } from "../common/DeleteConfirmationDialog";
import { useDeleteChat } from "../../mutations/chat";
import { useNavigate } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";

interface ChatMenuProps {
  chat: Chat;
  menuAnchorEl: null | HTMLElement;
  onClose: () => void;
}

export const ChatMenu: React.FC<ChatMenuProps> = ({ chat, menuAnchorEl, onClose }) => {
  const { t } = useTranslation("chats");
  const navigate = useNavigate();

  const menuItemClickHandler = (action: () => void) => () => {
    action();
    onClose();
  };

  const onChatDeleteSuccess = () => {
    navigate({ to: "/chats" });
  };
  const deleteChatMutation = useDeleteChat(onChatDeleteSuccess);

  const updateFormState = useChatFormState(chat);
  const [isUpdateDialogOpened, setUpdateDialogOpened] = useState(false);

  const openUpdateDialog = () => setUpdateDialogOpened(true);
  const closeUpdateDialog = () => {
    setUpdateDialogOpened(false);
    onClose();
  };
  const [isDeleteDialogOpened, setDeleteDialogOpened] = useState(false);
  const openDeleteDialog = () => setDeleteDialogOpened(true);
  const closeDeleteDialog = () => {
    setDeleteDialogOpened(false);
    onClose();
  };

  const handleChatDelete = () => {
    deleteChatMutation.mutate(chat.id);
  };

  return (
    <>
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={onClose}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
      >
        <MenuItem onClick={menuItemClickHandler(openUpdateDialog)}>
          <ListItemIcon>
            <EditIcon size={18} />
          </ListItemIcon>
          {t("edit_chat")}
        </MenuItem>
        <Divider />
        <MenuItem
          onClick={menuItemClickHandler(openDeleteDialog)}
          sx={{ color: "error.main" }}
        >
          <ListItemIcon>
            <TrashIcon size={18} color="#d32f2f" />
          </ListItemIcon>
          {t("delete_chat")}
        </MenuItem>
      </Menu>
      <UpdateChatFormDialog
        chat={chat}
        isDialogOpened={isUpdateDialogOpened}
        onDialogClose={closeUpdateDialog}
        formState={updateFormState}
      />

      <DeleteConfirmationDialog
        isOpened={isDeleteDialogOpened}
        onClose={closeDeleteDialog}
        onConfirm={handleChatDelete}
        message={t("delete_chat_confirmation")}
        title={t("delete_chat_title")}
      />
    </>
  );
};
