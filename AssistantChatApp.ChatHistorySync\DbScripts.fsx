#r "nuget: FSharp.Data.LiteralProviders, 1.0.3"
#r "nuget: SQLProvider, 1.5.6"
#r "nuget: SQLProvider.PostgreSql, 1.5.6"

#load "Library.fs"

open System
open FSharp.Data.Sql
open FSharp.Data.Sql.PostgreSql
open AssistantChatApp.ChatHistorySync.DbAccess

let ctx = ChatHistoryDB.GetDataContext()

let allMessages =
    query {
        for m in ctx.Public.N8nChatHistories do
        select m.Message
    }
    |> Seq.toArray
