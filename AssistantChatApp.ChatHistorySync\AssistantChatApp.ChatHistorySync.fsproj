﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="Library.fs" />
    <None Include="DbScripts.fsx" />
  </ItemGroup>

  <ItemGroup>
    <None Include="dbConnectionStringSample.txt" />
  </ItemGroup>

  <ItemGroup>
    <None Include=".editorconfig" />
    <None Include=".gitignore" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FSharp.Data.LiteralProviders" Version="1.0.3" />
    <PackageReference Include="SQLProvider" Version="1.5.6" />
    <PackageReference Include="SQLProvider.PostgreSql" Version="1.5.6" />
  </ItemGroup>

</Project>
