# Task: Generic DataGrid with data store integration

## Tech stack involved

### Frontend

- Framework: React
- Language: TypeScript
- UI Library: MUI
- Data Grid Libraries: Material React Table, TanStack Table
- State management: TanStack Query, Zustand, Jo<PERSON>

### Backend

- Platform: .NET (F# or C#)
- Web framework: ASP.NET Core
- Considered DB options:
  - For portability and rapid prototyping: SQLite, LiteDB
  - For a longer term solution (on later stages): PostgreSQL, ArangoDB

## Task Description

Current goal is to come up with a minimal working prototype/proof-of-concept quickly. So, it doesn't have to support all the features fully but a reasonable subset of them (to fit the main idea of the task).

For now, the task is focused on a specific frontend component: a DataGrid that can load and show data from a data store in a flexible way. I.e. it's a reusable component that doesn't have to be statically tied to a specific dataset, rather the details about the data to load and show are provided at runtime.

However, the backend part has a broader scope, as it can be used as a generic data store with limited querying capabilities.

The task requires frontend and backend coordination. Thus, at least the interaction protocol need to be specified.

### Backend interface

On the top-level, the backend should provide an API that accepts the following information:
- A query describing the data to retrieve. At minimum, a resource/collection identifier. Additionally, it can include filtering, sorting, projection, etc.

A response should contain the following information:
- a data schema that describes the data returned;
- a set of data rows that match the schema.

### Frontend Part

The DataGrid component should be able to load and show the data from a data store based on a set of parameters about the data expected. The concrete data store implementation (is it a DB, a file, etc.) must not matter: its the backend's responsibility to handle that.

The frontend's duty is to supply to the backend the following information:
- An connection information about the data store to access.
- A query to be executed against the data store to retrieve the data (e.g., resource/collection identifier, filtering, sorting, projection, etc.)

As a response, the frontend should receive and process the following data:
1. A data schema that describes the data returned, so that DataGrid could derive the column definitions.
2. A set of data rows that match the schema and could be rendered in the DataGrid.

## Use case: AI-assisted chat with interactivity

1. User asks a question that implies consulting some data registry. E.g.: "I'd like to buy a windshield for my BMW X5. What options could you provide?"
2. AI assistant receives that query and in the reasoning process decides to form a query to a data registry. Suppose, the registry supports some kind of formal query language that allows filtering, sorting, projecting, etc. The AI assistant, knowing the query syntax, responds with a "data store query" tool call, containing the query expression that matches the user's question (according to the AI assistant's understanding of the user's intent).
3. The "data store querying tool" doesn't return the actual data every time. Here are the possible scenarios:
   1. If the query is invalid, the corresponding error messages is returned.
   2. If the query is valid, instead of simply returning the data, the tool requests the quantity of entries obtained by the query expression (for example, by adjusting the query to return the quantity). And if it's too large (greater than a certain threshold), a corresponding message is returned. Otherwise, the data is returned (or a message that the query is good).
4. The query tool result is sent to the AI assistant. It analyzes it in the following way:
   1. If the query is invalid, it adjusts the query and repeats the query tool call with the adjusted query. In that case, we're returning to the step 3.
   2. If the query is not okay because of the quantity overshoot, the AI tries to adjust the query (e.g., by adding more filtering criteria) and repeats the query tool call with it. In that case, we're returning to the step 3. However, if the AI decides that it can't/doesn't know how to adjust the query, it forms a follow-up question to be sent to the user.
5. If the query is valid and the quantity is okay, the AI assistant forms a response to the user. To avoid inlining the data directly into the response, it should embed a specifically structured DataGrid definition (in a format, described in the agent's system prompt). The definition should contain the query and the connection information. This response message finally will be processed by the frontend logic where this DataGrid definition will be rendered as an interactive DataGrid component.

Outcome: user sees and can interact with a reasonable amount of data that matches their intent.
