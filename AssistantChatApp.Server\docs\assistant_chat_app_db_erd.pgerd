{"version": "81300", "data": {"id": "908c1282-f58f-4385-a5cf-69be429fbdef", "offsetX": 242.90787408050534, "offsetY": 33.831408549358315, "zoom": 75.00000076556418, "gridSize": 15, "layers": [{"id": "138021ab-ae5a-4f7e-881e-752cb0b2b9bf", "type": "diagram-links", "isSvg": true, "transformed": true, "models": {"7469621f-a4bf-426c-839b-b8fab964e62f": {"id": "7469621f-a4bf-426c-839b-b8fab964e62f", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "sourcePort": "9898e1ec-90d4-487e-8f4f-8802237334f6", "target": "af97c764-e265-462b-b5e6-c44a1886e58d", "targetPort": "080ca0e4-124c-498d-8747-f5a293dbed11", "points": [{"id": "aaaf2082-4ffd-4a68-8fd3-549031d20f39", "type": "point", "x": 504.08254651699303, "y": 453.59802412373836}, {"id": "57e95457-328e-494f-be57-60bb86ba7a1c", "type": "point", "x": 504.08254651699303, "y": 678.5980625171496}, {"id": "f6003f69-c3bd-4fe7-abb0-0c19fb9fcc0e", "type": "point", "x": 525.9059506296954, "y": 678.5980625171496}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "af97c764-e265-462b-b5e6-c44a1886e58d", "local_column_attnum": 1, "referenced_table_uid": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "referenced_column_attnum": 1}}, "9a5b7c7c-085f-45a9-bd9b-1cc5da79c809": {"id": "9a5b7c7c-085f-45a9-bd9b-1cc5da79c809", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "686a7ee1-f2a9-4310-afac-3a478e26c396", "sourcePort": "39aa00de-6e17-4c7c-9008-7b88262f2ce4", "target": "af97c764-e265-462b-b5e6-c44a1886e58d", "targetPort": "94dfd1d4-abe2-48f9-b39c-fec3d0bfd716", "points": [{"id": "60bd41bc-c316-4805-bc2f-2f03786ad528", "type": "point", "x": 810.9117257152843, "y": 543.6066495070567}, {"id": "9d8b1ecf-b0a8-403b-9979-508ac493e252", "type": "point", "x": 774.7894933173119, "y": 543.6066495070567}, {"id": "a86b6119-cf4c-4e1c-b4f4-3c645ecb741f", "type": "point", "x": 774.7894933173119, "y": 687.0520012329391}, {"id": "d5d66b71-22c9-42f5-bbd4-f1a5b6467793", "type": "point", "x": 759.071069304817, "y": 687.0520012329391}, {"id": "f4eab4c8-78a7-49d5-9180-b3ec3342b138", "type": "point", "x": 759.071069304817, "y": 704.3004548329169}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "af97c764-e265-462b-b5e6-c44a1886e58d", "local_column_attnum": 2, "referenced_table_uid": "686a7ee1-f2a9-4310-afac-3a478e26c396", "referenced_column_attnum": 1}}, "7e790fce-f5d2-46eb-a2e3-368d9af780e1": {"id": "7e790fce-f5d2-46eb-a2e3-368d9af780e1", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "sourcePort": "3fe8f485-1f8e-49bb-b850-c86ddcf395fa", "target": "241476da-b095-4c9f-897b-b44da727d21c", "targetPort": "39e1f5a2-135c-4f7f-b878-fea2baafe1a4", "points": [{"id": "6726bcd1-6127-43fb-ad54-ef40d125851c", "type": "point", "x": 270.91742784187153, "y": 453.59802412373836}, {"id": "5b8817cf-2a11-45da-b420-f975e389f9ce", "type": "point", "x": 249.0797008126486, "y": 453.59802412373836}, {"id": "5095fb0d-bdf2-49c7-bf0e-3dab24ec65f2", "type": "point", "x": 249.0797008126486, "y": 258.60091511157157}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "241476da-b095-4c9f-897b-b44da727d21c", "local_column_attnum": 1, "referenced_table_uid": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "referenced_column_attnum": 1}}, "85caa9c0-23f8-47c5-a04c-4a8992aa217c": {"id": "85caa9c0-23f8-47c5-a04c-4a8992aa217c", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "8b96b61f-ab35-4185-a1c0-fb3841297a7a", "sourcePort": "9dd0e304-3fa1-4742-8ae8-d37a77736f9d", "target": "241476da-b095-4c9f-897b-b44da727d21c", "targetPort": "b4886ce7-cc99-4b73-8764-6f2c9a109214", "points": [{"id": "f06e55db-05fb-43b4-a6a3-e1cd420c8469", "type": "point", "x": -20.91740743395404, "y": 453.59802412373836}, {"id": "f27b138e-e508-42c1-a770-eaa84463bdd9", "type": "point", "x": -13.210498639160656, "y": 453.59802412373836}, {"id": "26c898f6-299b-4ec3-8f3d-21aebf87545c", "type": "point", "x": -13.210498639160656, "y": 284.30330742733884}, {"id": "c8e2fa71-7648-46fd-bb44-e0701ef39158", "type": "point", "x": 15.914582137527091, "y": 284.30330742733884}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "241476da-b095-4c9f-897b-b44da727d21c", "local_column_attnum": 2, "referenced_table_uid": "8b96b61f-ab35-4185-a1c0-fb3841297a7a", "referenced_column_attnum": 1}}, "cb25fbb6-58f1-4171-a5c5-f4d4475c9dbe": {"id": "cb25fbb6-58f1-4171-a5c5-f4d4475c9dbe", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "sourcePort": "efbf12b1-0240-4219-ae92-bb77b5d6893b", "target": "26e0329f-9d66-4fbf-9185-207159fb3865", "targetPort": "a40f8449-cd66-424b-9eb0-498c58a97c22", "points": [{"id": "8221706a-50bf-4988-9a3c-696233bc7b76", "type": "point", "x": 759.071069304817, "y": 183.60091587713575}, {"id": "f875ebc6-b779-4155-bced-8ed0a97b4515", "type": "point", "x": 759.071069304817, "y": 169.71867318029737}, {"id": "b1b13e1e-8d94-4ecb-8813-bdbd04be7aa0", "type": "point", "x": 1070.7894902958853, "y": 169.71867318029737}, {"id": "5b6e4c3d-ff39-4d7a-ba67-1a6e0dff1490", "type": "point", "x": 1070.7894902958853, "y": 483.60091281487905}, {"id": "73a96e9a-d37d-4664-81d8-34f4bfb44059", "type": "point", "x": 1080.9087932717832, "y": 483.60091281487905}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "26e0329f-9d66-4fbf-9185-207159fb3865", "local_column_attnum": 1, "referenced_table_uid": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "referenced_column_attnum": 1}}, "fea207bb-b663-4323-b111-67f942a0de61": {"id": "fea207bb-b663-4323-b111-67f942a0de61", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "686a7ee1-f2a9-4310-afac-3a478e26c396", "sourcePort": "5fb3e1aa-45e8-4274-a90a-788827d6e9d1", "target": "26e0329f-9d66-4fbf-9185-207159fb3865", "targetPort": "176bfc89-6d04-474a-89ac-ca970afca95d", "points": [{"id": "2593a27b-acbe-4456-92aa-4c01ba1ba43c", "type": "point", "x": 1044.0768443904058, "y": 543.6066495070567}, {"id": "698ff291-55e3-4e0c-a734-40f54ffb4971", "type": "point", "x": 1066.7894903367153, "y": 543.6066495070567}, {"id": "fea7687e-180d-40e2-b2ec-9f17383e5a21", "type": "point", "x": 1066.7894903367153, "y": 509.30330513064627}, {"id": "a0d608ce-1904-48a3-8025-4d88156fcd79", "type": "point", "x": 1080.9087932717832, "y": 509.30330513064627}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "26e0329f-9d66-4fbf-9185-207159fb3865", "local_column_attnum": 2, "referenced_table_uid": "686a7ee1-f2a9-4310-afac-3a478e26c396", "referenced_column_attnum": 1}}, "ab2ccd95-29e9-48a7-b37f-81fa2c6a7523": {"id": "ab2ccd95-29e9-48a7-b37f-81fa2c6a7523", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "686a7ee1-f2a9-4310-afac-3a478e26c396", "sourcePort": "39aa00de-6e17-4c7c-9008-7b88262f2ce4", "target": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "targetPort": "c65b0824-924c-4e18-8cfd-a9f85872af88", "points": [{"id": "95b82f1a-890a-45b4-ba7d-16323e6ef8d8", "type": "point", "x": 810.9117257152843, "y": 543.6066495070567}, {"id": "dd6fdd33-c23f-4e9a-95fa-c1f012445154", "type": "point", "x": 808.1228263103944, "y": 543.6066495070567}, {"id": "8ee5f18e-5ccc-4f13-bd47-c095eb26b6f3", "type": "point", "x": 808.1228263103944, "y": 235.00574119877402}, {"id": "45a84b2d-1295-46bf-8077-db46c696c9b4", "type": "point", "x": 759.071069304817, "y": 235.00574119877402}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "local_column_attnum": 3, "referenced_table_uid": "686a7ee1-f2a9-4310-afac-3a478e26c396", "referenced_column_attnum": 1}}, "80cdd955-a6e8-4810-809d-e2a785d06e84": {"id": "80cdd955-a6e8-4810-809d-e2a785d06e84", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "sourcePort": "9898e1ec-90d4-487e-8f4f-8802237334f6", "target": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "targetPort": "6c19726e-4886-48d4-85f5-0be15387a43d", "points": [{"id": "217e2888-9de1-47a2-ab24-8281f07c7fac", "type": "point", "x": 504.08254651699303, "y": 453.59802412373836}, {"id": "32d64eed-9eb5-444a-bc59-df04c64b7f9a", "type": "point", "x": 525.9059506296954, "y": 453.59802412373836}, {"id": "7de633b5-7b4b-4b31-9743-7d78085386bb", "type": "point", "x": 525.9059506296954, "y": 209.************}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "local_column_attnum": 2, "referenced_table_uid": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "referenced_column_attnum": 1}}}}, {"id": "9e9217b1-1442-4ab1-9743-98919925f233", "type": "diagram-nodes", "isSvg": false, "transformed": true, "models": {"af97c764-e265-462b-b5e6-c44a1886e58d": {"id": "af97c764-e265-462b-b5e6-c44a1886e58d", "type": "table", "selected": false, "x": 555, "y": 585, "ports": [{"id": "080ca0e4-124c-498d-8747-f5a293dbed11", "type": "one<PERSON><PERSON>", "x": 555.9059506296954, "y": 678.5980625171496, "name": "coll-port-1-left", "alignment": "left", "parentNode": "af97c764-e265-462b-b5e6-c44a1886e58d", "links": ["7469621f-a4bf-426c-839b-b8fab964e62f"]}, {"id": "82a3da3e-52e3-448a-8bb6-62ee0e7c9a60", "type": "one<PERSON><PERSON>", "x": 729.071069304817, "y": 678.5980625171496, "name": "coll-port-1-right", "alignment": "right", "parentNode": "af97c764-e265-462b-b5e6-c44a1886e58d", "links": []}, {"id": "4cf0a743-a5b3-4cdd-b492-c4205b1097c8", "type": "one<PERSON><PERSON>", "x": 555.9059506296954, "y": 704.3004548329169, "name": "coll-port-2-left", "alignment": "left", "parentNode": "af97c764-e265-462b-b5e6-c44a1886e58d", "links": []}, {"id": "94dfd1d4-abe2-48f9-b39c-fec3d0bfd716", "type": "one<PERSON><PERSON>", "x": 729.071069304817, "y": 704.3004548329169, "name": "coll-port-2-right", "alignment": "right", "parentNode": "af97c764-e265-462b-b5e6-c44a1886e58d", "links": ["9a5b7c7c-085f-45a9-bd9b-1cc5da79c809"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["080ca0e4-124c-498d-8747-f5a293dbed11", "82a3da3e-52e3-448a-8bb6-62ee0e7c9a60", "4cf0a743-a5b3-4cdd-b492-c4205b1097c8", "94dfd1d4-abe2-48f9-b39c-fec3d0bfd716"], "otherInfo": {"data": {"columns": [{"name": "chat_id", "atttypid": 2950, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "chat_participants", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["uuid"]}, {"name": "user_id", "atttypid": 2950, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "chat_participants", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["uuid", "uuid"]}, {"name": "joined_at", "atttypid": 1114, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "now()", "typname": "timestamp without time zone", "displaytypname": "timestamp without time zone", "cltype": "timestamp without time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1114, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "chat_participants", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "time without time zone", "timestamp with time zone", "timestamp without time zone", "timestamp without time zone"]}], "name": "chat_participants", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18227, "name": "chat_participants_chat_id_user_id_pk", "col_count": 2, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "chat_id"}, {"column": "user_id"}], "include": []}], "foreign_key": [{"name": "chat_participants_chat_id_chats_id_fk", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [1], "confkey": [1], "confrelid": 18229, "fknsp": "public", "fktab": "chat_participants", "refnspoid": 2200, "refnsp": "public", "reftab": "chats", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "chat_id", "references": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "referenced": "id", "references_table_name": "public.chats"}], "remote_schema": "public", "remote_table": "chats", "coveringindex": null, "autoindex": false, "hasindex": false}, {"name": "chat_participants_user_id_users_id_fk", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [2], "confkey": [1], "confrelid": 18239, "fknsp": "public", "fktab": "chat_participants", "refnspoid": 2200, "refnsp": "public", "reftab": "users", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "user_id", "references": "686a7ee1-f2a9-4310-afac-3a478e26c396", "referenced": "id", "references_table_name": "public.users"}], "remote_schema": "public", "remote_table": "users", "coveringindex": null, "autoindex": false, "hasindex": false}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "241476da-b095-4c9f-897b-b44da727d21c": {"id": "241476da-b095-4c9f-897b-b44da727d21c", "type": "table", "selected": false, "x": 45, "y": 165, "ports": [{"id": "091c5285-6f0b-4bad-99d1-fb5e3abd45ab", "type": "one<PERSON><PERSON>", "x": 45.91458213752709, "y": 258.60091511157157, "name": "coll-port-1-left", "alignment": "left", "parentNode": "241476da-b095-4c9f-897b-b44da727d21c", "links": []}, {"id": "39e1f5a2-135c-4f7f-b878-fea2baafe1a4", "type": "one<PERSON><PERSON>", "x": 219.0797008126486, "y": 258.60091511157157, "name": "coll-port-1-right", "alignment": "right", "parentNode": "241476da-b095-4c9f-897b-b44da727d21c", "links": ["7e790fce-f5d2-46eb-a2e3-368d9af780e1"]}, {"id": "b4886ce7-cc99-4b73-8764-6f2c9a109214", "type": "one<PERSON><PERSON>", "x": 45.91458213752709, "y": 284.30330742733884, "name": "coll-port-2-left", "alignment": "left", "parentNode": "241476da-b095-4c9f-897b-b44da727d21c", "links": ["85caa9c0-23f8-47c5-a04c-4a8992aa217c"]}, {"id": "f8567632-b638-4b12-841d-9bf86ef12278", "type": "one<PERSON><PERSON>", "x": 219.0797008126486, "y": 284.30330742733884, "name": "coll-port-2-right", "alignment": "right", "parentNode": "241476da-b095-4c9f-897b-b44da727d21c", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["091c5285-6f0b-4bad-99d1-fb5e3abd45ab", "39e1f5a2-135c-4f7f-b878-fea2baafe1a4", "b4886ce7-cc99-4b73-8764-6f2c9a109214", "f8567632-b638-4b12-841d-9bf86ef12278"], "otherInfo": {"data": {"columns": [{"name": "chat_id", "atttypid": 2950, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "chat_tags", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["uuid"]}, {"name": "tag_id", "atttypid": 2950, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "chat_tags", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["uuid", "uuid"]}], "name": "chat_tags", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18265, "name": "chat_tags_chat_id_tag_id_pk", "col_count": 2, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "chat_id"}, {"column": "tag_id"}], "include": []}], "foreign_key": [{"name": "chat_tags_chat_id_chats_id_fk", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [1], "confkey": [1], "confrelid": 18229, "fknsp": "public", "fktab": "chat_tags", "refnspoid": 2200, "refnsp": "public", "reftab": "chats", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "chat_id", "references": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "referenced": "id", "references_table_name": "public.chats"}], "remote_schema": "public", "remote_table": "chats", "coveringindex": null, "autoindex": false, "hasindex": false}, {"name": "chat_tags_tag_id_tags_id_fk", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [2], "confkey": [1], "confrelid": 18267, "fknsp": "public", "fktab": "chat_tags", "refnspoid": 2200, "refnsp": "public", "reftab": "tags", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "tag_id", "references": "8b96b61f-ab35-4185-a1c0-fb3841297a7a", "referenced": "id", "references_table_name": "public.tags"}], "remote_schema": "public", "remote_table": "tags", "coveringindex": null, "autoindex": false, "hasindex": false}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7": {"id": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "type": "table", "selected": false, "x": 300, "y": 360, "ports": [{"id": "3fe8f485-1f8e-49bb-b850-c86ddcf395fa", "type": "one<PERSON><PERSON>", "x": 300.91742784187153, "y": 453.59802412373836, "name": "coll-port-1-left", "alignment": "left", "parentNode": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "links": ["7e790fce-f5d2-46eb-a2e3-368d9af780e1"]}, {"id": "9898e1ec-90d4-487e-8f4f-8802237334f6", "type": "one<PERSON><PERSON>", "x": 474.08254651699303, "y": 453.59802412373836, "name": "coll-port-1-right", "alignment": "right", "parentNode": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "links": ["7469621f-a4bf-426c-839b-b8fab964e62f", "80cdd955-a6e8-4810-809d-e2a785d06e84"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["3fe8f485-1f8e-49bb-b850-c86ddcf395fa", "9898e1ec-90d4-487e-8f4f-8802237334f6"], "otherInfo": {"data": {"columns": [{"name": "id", "atttypid": 2950, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "gen_random_uuid()", "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "chats", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["uuid"]}, {"name": "title", "atttypid": 1043, "attlen": "100", "attnum": 2, "attndims": 0, "atttypmod": 104, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(100)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "chats", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "description", "atttypid": 1043, "attlen": "500", "attnum": 3, "attndims": 0, "atttypmod": 504, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(500)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "chats", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "created_at", "atttypid": 1114, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "now()", "typname": "timestamp without time zone", "displaytypname": "timestamp without time zone", "cltype": "timestamp without time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1114, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "chats", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "time without time zone", "timestamp with time zone", "timestamp without time zone", "timestamp without time zone"]}, {"name": "updated_at", "atttypid": 1114, "attlen": null, "attnum": 5, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "now()", "typname": "timestamp without time zone", "displaytypname": "timestamp without time zone", "cltype": "timestamp without time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1114, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "chats", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "time without time zone", "timestamp with time zone", "timestamp without time zone", "timestamp without time zone", "timestamp without time zone"]}], "name": "chats", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18237, "name": "chats_pkey", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "id"}], "include": []}], "unique_constraint": [], "foreign_key": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "26e0329f-9d66-4fbf-9185-207159fb3865": {"id": "26e0329f-9d66-4fbf-9185-207159fb3865", "type": "table", "selected": false, "x": 1110, "y": 390, "ports": [{"id": "a40f8449-cd66-424b-9eb0-498c58a97c22", "type": "one<PERSON><PERSON>", "x": 1110.9087932717832, "y": 483.60091281487905, "name": "coll-port-1-left", "alignment": "left", "parentNode": "26e0329f-9d66-4fbf-9185-207159fb3865", "links": ["cb25fbb6-58f1-4171-a5c5-f4d4475c9dbe"]}, {"id": "208f029c-592d-4181-9c6f-dc37e11425bc", "type": "one<PERSON><PERSON>", "x": 1284.0739933271122, "y": 483.60091281487905, "name": "coll-port-1-right", "alignment": "right", "parentNode": "26e0329f-9d66-4fbf-9185-207159fb3865", "links": []}, {"id": "176bfc89-6d04-474a-89ac-ca970afca95d", "type": "one<PERSON><PERSON>", "x": 1110.9087932717832, "y": 509.30330513064627, "name": "coll-port-2-left", "alignment": "left", "parentNode": "26e0329f-9d66-4fbf-9185-207159fb3865", "links": ["fea207bb-b663-4323-b111-67f942a0de61"]}, {"id": "61d1ba1b-3d42-406a-828a-4eb10d4cac89", "type": "one<PERSON><PERSON>", "x": 1284.0739933271122, "y": 509.30330513064627, "name": "coll-port-2-right", "alignment": "right", "parentNode": "26e0329f-9d66-4fbf-9185-207159fb3865", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["a40f8449-cd66-424b-9eb0-498c58a97c22", "208f029c-592d-4181-9c6f-dc37e11425bc", "176bfc89-6d04-474a-89ac-ca970afca95d", "61d1ba1b-3d42-406a-828a-4eb10d4cac89"], "otherInfo": {"data": {"columns": [{"name": "message_id", "atttypid": 2950, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "mentions", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["uuid"]}, {"name": "user_id", "atttypid": 2950, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1 2", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "mentions", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["uuid", "uuid"]}], "name": "mentions", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18278, "name": "mentions_message_id_user_id_pk", "col_count": 2, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "message_id"}, {"column": "user_id"}], "include": []}], "foreign_key": [{"name": "mentions_message_id_messages_id_fk", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [1], "confkey": [1], "confrelid": 18252, "fknsp": "public", "fktab": "mentions", "refnspoid": 2200, "refnsp": "public", "reftab": "messages", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "message_id", "references": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "referenced": "id", "references_table_name": "public.messages"}], "remote_schema": "public", "remote_table": "messages", "coveringindex": null, "autoindex": false, "hasindex": false}, {"name": "mentions_user_id_users_id_fk", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [2], "confkey": [1], "confrelid": 18239, "fknsp": "public", "fktab": "mentions", "refnspoid": 2200, "refnsp": "public", "reftab": "users", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "user_id", "references": "686a7ee1-f2a9-4310-afac-3a478e26c396", "referenced": "id", "references_table_name": "public.users"}], "remote_schema": "public", "remote_table": "users", "coveringindex": null, "autoindex": false, "hasindex": false}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "457faecd-0f8f-4e3d-9c20-e260bff17caa": {"id": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "type": "table", "selected": false, "x": 555, "y": 90, "ports": [{"id": "6b2442a4-2339-4adc-9a3b-ac662c67efe2", "type": "one<PERSON><PERSON>", "x": 555.9059506296954, "y": 183.60091587713575, "name": "coll-port-1-left", "alignment": "left", "parentNode": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "links": []}, {"id": "efbf12b1-0240-4219-ae92-bb77b5d6893b", "type": "one<PERSON><PERSON>", "x": 729.071069304817, "y": 183.60091587713575, "name": "coll-port-1-right", "alignment": "right", "parentNode": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "links": ["cb25fbb6-58f1-4171-a5c5-f4d4475c9dbe"]}, {"id": "5773d118-653e-478b-9154-4bd68b4e4c27", "type": "one<PERSON><PERSON>", "x": 555.9059506296954, "y": 235.00574119877402, "name": "coll-port-3-left", "alignment": "left", "parentNode": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "links": []}, {"id": "c65b0824-924c-4e18-8cfd-a9f85872af88", "type": "one<PERSON><PERSON>", "x": 729.071069304817, "y": 235.00574119877402, "name": "coll-port-3-right", "alignment": "right", "parentNode": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "links": ["ab2ccd95-29e9-48a7-b37f-81fa2c6a7523"]}, {"id": "6c19726e-4886-48d4-85f5-0be15387a43d", "type": "one<PERSON><PERSON>", "x": 555.9059506296954, "y": 209.************, "name": "coll-port-2-left", "alignment": "left", "parentNode": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "links": ["80cdd955-a6e8-4810-809d-e2a785d06e84"]}, {"id": "de5bf9cb-3a61-4a12-b445-75beb87b2758", "type": "one<PERSON><PERSON>", "x": 729.071069304817, "y": 209.************, "name": "coll-port-2-right", "alignment": "right", "parentNode": "457faecd-0f8f-4e3d-9c20-e260bff17caa", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["6b2442a4-2339-4adc-9a3b-ac662c67efe2", "efbf12b1-0240-4219-ae92-bb77b5d6893b", "5773d118-653e-478b-9154-4bd68b4e4c27", "c65b0824-924c-4e18-8cfd-a9f85872af88", "6c19726e-4886-48d4-85f5-0be15387a43d", "de5bf9cb-3a61-4a12-b445-75beb87b2758"], "otherInfo": {"data": {"columns": [{"name": "id", "atttypid": 2950, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "gen_random_uuid()", "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["uuid"]}, {"name": "chat_id", "atttypid": 2950, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["uuid", "uuid"]}, {"name": "author_id", "atttypid": 2950, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": null, "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["uuid", "uuid", "uuid"]}, {"name": "content", "atttypid": 25, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}, {"name": "created_at", "atttypid": 1114, "attlen": null, "attnum": 5, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "now()", "typname": "timestamp without time zone", "displaytypname": "timestamp without time zone", "cltype": "timestamp without time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1114, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "time without time zone", "timestamp with time zone", "timestamp without time zone", "timestamp without time zone"]}, {"name": "updated_at", "atttypid": 1114, "attlen": null, "attnum": 6, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "now()", "typname": "timestamp without time zone", "displaytypname": "timestamp without time zone", "cltype": "timestamp without time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1114, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "messages", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "time without time zone", "timestamp with time zone", "timestamp without time zone", "timestamp without time zone", "timestamp without time zone"]}], "name": "messages", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18260, "name": "messages_pkey", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "id"}], "include": []}], "foreign_key": [{"name": "messages_author_id_users_id_fk", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [3], "confkey": [1], "confrelid": 18239, "fknsp": "public", "fktab": "messages", "refnspoid": 2200, "refnsp": "public", "reftab": "users", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "author_id", "references": "686a7ee1-f2a9-4310-afac-3a478e26c396", "referenced": "id", "references_table_name": "public.users"}], "remote_schema": "public", "remote_table": "users", "coveringindex": null, "autoindex": false, "hasindex": false}, {"name": "messages_chat_id_chats_id_fk", "condeferrable": false, "condeferred": false, "confupdtype": "a", "confdeltype": "c", "confmatchtype": false, "conkey": [2], "confkey": [1], "confrelid": 18229, "fknsp": "public", "fktab": "messages", "refnspoid": 2200, "refnsp": "public", "reftab": "chats", "comment": null, "convalidated": true, "conislocal": true, "columns": [{"local_column": "chat_id", "references": "cb3e7be9-09d4-445a-a7b1-01399c7dfcf7", "referenced": "id", "references_table_name": "public.chats"}], "remote_schema": "public", "remote_table": "chats", "coveringindex": null, "autoindex": false, "hasindex": false}], "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "8b96b61f-ab35-4185-a1c0-fb3841297a7a": {"id": "8b96b61f-ab35-4185-a1c0-fb3841297a7a", "type": "table", "selected": false, "x": -225, "y": 360, "ports": [{"id": "0273caa4-7b72-400f-b39f-14160fc8fc5b", "type": "one<PERSON><PERSON>", "x": -224.08254645412742, "y": 453.59802412373836, "name": "coll-port-1-left", "alignment": "left", "parentNode": "8b96b61f-ab35-4185-a1c0-fb3841297a7a", "links": []}, {"id": "9dd0e304-3fa1-4742-8ae8-d37a77736f9d", "type": "one<PERSON><PERSON>", "x": -50.91740743395404, "y": 453.59802412373836, "name": "coll-port-1-right", "alignment": "right", "parentNode": "8b96b61f-ab35-4185-a1c0-fb3841297a7a", "links": ["85caa9c0-23f8-47c5-a04c-4a8992aa217c"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["0273caa4-7b72-400f-b39f-14160fc8fc5b", "9dd0e304-3fa1-4742-8ae8-d37a77736f9d"], "otherInfo": {"data": {"columns": [{"name": "id", "atttypid": 2950, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "gen_random_uuid()", "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "tags", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["uuid"]}, {"name": "name", "atttypid": 1043, "attlen": "50", "attnum": 2, "attndims": 0, "atttypmod": 54, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(50)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "tags", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}], "name": "tags", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18271, "name": "tags_pkey", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "id"}], "include": []}], "unique_constraint": [{"oid": 18273, "name": "tags_name_unique", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "name"}], "include": []}], "foreign_key": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "686a7ee1-f2a9-4310-afac-3a478e26c396": {"id": "686a7ee1-f2a9-4310-afac-3a478e26c396", "type": "table", "selected": false, "x": 840, "y": 450, "ports": [{"id": "39aa00de-6e17-4c7c-9008-7b88262f2ce4", "type": "one<PERSON><PERSON>", "x": 840.9117257152843, "y": 543.6066495070567, "name": "coll-port-1-left", "alignment": "left", "parentNode": "686a7ee1-f2a9-4310-afac-3a478e26c396", "links": ["9a5b7c7c-085f-45a9-bd9b-1cc5da79c809", "ab2ccd95-29e9-48a7-b37f-81fa2c6a7523"]}, {"id": "5fb3e1aa-45e8-4274-a90a-788827d6e9d1", "type": "one<PERSON><PERSON>", "x": 1014.0768443904059, "y": 543.6066495070567, "name": "coll-port-1-right", "alignment": "right", "parentNode": "686a7ee1-f2a9-4310-afac-3a478e26c396", "links": ["fea207bb-b663-4323-b111-67f942a0de61"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["39aa00de-6e17-4c7c-9008-7b88262f2ce4", "5fb3e1aa-45e8-4274-a90a-788827d6e9d1"], "otherInfo": {"data": {"columns": [{"name": "id", "atttypid": 2950, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "gen_random_uuid()", "typname": "uuid", "displaytypname": "uuid", "cltype": "uuid", "inheritedfrom": null, "inheritedid": null, "elemoid": 2950, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "users", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["uuid"]}, {"name": "email", "atttypid": 1043, "attlen": "255", "attnum": 2, "attndims": 0, "atttypmod": 259, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(255)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "users", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "password", "atttypid": 1043, "attlen": "255", "attnum": 3, "attndims": 0, "atttypmod": 259, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(255)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "users", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "display_name", "atttypid": 1043, "attlen": "100", "attnum": 4, "attndims": 0, "atttypmod": 104, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(100)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "users", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "avatar", "atttypid": 1043, "attlen": "255", "attnum": 5, "attndims": 0, "atttypmod": 259, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(255)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "users", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "created_at", "atttypid": 1114, "attlen": null, "attnum": 6, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "now()", "typname": "timestamp without time zone", "displaytypname": "timestamp without time zone", "cltype": "timestamp without time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1114, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "users", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "time without time zone", "timestamp with time zone", "timestamp without time zone", "timestamp without time zone"]}, {"name": "updated_at", "atttypid": 1114, "attlen": null, "attnum": 7, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "now()", "typname": "timestamp without time zone", "displaytypname": "timestamp without time zone", "cltype": "timestamp without time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1114, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "users", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "time without time zone", "timestamp with time zone", "timestamp without time zone", "timestamp without time zone", "timestamp without time zone"]}, {"name": "is_ai", "atttypid": 16, "attlen": null, "attnum": 8, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": null, "attstorage": "p", "attidentity": "", "defval": "false", "typname": "boolean", "displaytypname": "boolean", "cltype": "boolean", "inheritedfrom": null, "inheritedid": null, "elemoid": 16, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "users", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["boolean", "character", "character varying", "text"]}], "name": "users", "schema": "public", "description": null, "rlspolicy": false, "forcerlspolicy": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "relpersistence": false, "primary_key": [{"oid": 18248, "name": "users_pkey", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "id"}], "include": []}], "unique_constraint": [{"oid": 18250, "name": "users_email_unique", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "email"}], "include": []}], "foreign_key": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}}}]}}