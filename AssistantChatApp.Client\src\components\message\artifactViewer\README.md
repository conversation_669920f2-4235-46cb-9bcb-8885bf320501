# Artifact Viewer Module

A self-contained, draggable and resizable modal window system for displaying interactive components (artifacts) embedded in chat messages.

## Features

- **Draggable and Resizable**: Move and resize modals using react-rnd
- **Expand/Collapse**: Toggle between collapsed (header only) and expanded states
- **Fullscreen Mode**: Switch between normal and fullscreen modes
- **Size Switcher**: Discrete size switcher with predefined sizes
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Multiple Modals**: Support for multiple modal instances with focus management
- **Keyboard Shortcuts**: Escape to close, Alt+Tab to cycle through modals

## Basic Usage

### Simple Trigger Button

```tsx
import { ArtifactTriggerButton } from './components/message/artifactViewer';

function MyComponent() {
  return (
    <ArtifactTriggerButton
      artifactId="my-artifact-1"
      title="Survey Form"
      description="Interactive survey with multiple questions"
    >
      <SurveyComponent questions={questions} />
    </ArtifactTriggerButton>
  );
}
```

### Card-style Trigger

```tsx
import { ArtifactTriggerCard } from './components/message/artifactViewer';

function MyComponent() {
  return (
    <ArtifactTriggerCard
      artifactId="data-table-1"
      title="Sales Data"
      description="Interactive data table with filtering and sorting"
      variant="outlined"
    >
      <DataGrid data={salesData} />
    </ArtifactTriggerCard>
  );
}
```

### Manual Control with Hooks

```tsx
import { useArtifactViewer, ArtifactViewer } from './components/message/artifactViewer';

function MyComponent() {
  const { isOpen, open, close, state } = useArtifactViewer(
    'manual-artifact',
    'Custom Form',
    'A manually controlled artifact'
  );

  return (
    <>
      <Button onClick={open}>Open Artifact</Button>
      
      {isOpen && (
        <ArtifactViewer
          artifactId="manual-artifact"
          title="Custom Form"
          description="A manually controlled artifact"
          onClose={close}
        >
          <MyCustomForm />
        </ArtifactViewer>
      )}
    </>
  );
}
```

## Advanced Usage

### Multiple Modal Management

```tsx
import { useArtifactViewerManager } from './components/message/artifactViewer';

function MyComponent() {
  const { activeViewers, closeAll, focusNext } = useArtifactViewerManager();

  return (
    <div>
      <p>Active modals: {activeViewers.length}</p>
      <Button onClick={closeAll}>Close All</Button>
      <Button onClick={focusNext}>Focus Next</Button>
    </div>
  );
}
```

### Custom Size Switcher

```tsx
import { SizeSwitcher } from './components/message/artifactViewer';

const customSizes = [
  { width: 300, height: 200, label: 'Tiny' },
  { width: 500, height: 400, label: 'Small' },
  { width: 800, height: 600, label: 'Medium' },
  { width: 1200, height: 800, label: 'Large' },
];

function MyHeader() {
  const [currentSize, setCurrentSize] = useState({ width: 500, height: 400 });

  return (
    <SizeSwitcher
      currentSize={currentSize}
      availableSizes={customSizes}
      onSizeChange={setCurrentSize}
    />
  );
}
```

### Content Wrappers

```tsx
import {
  ArtifactViewerPaddedContent,
  ArtifactViewerScrollableContent,
  ArtifactViewerSectionedContent,
} from './components/message/artifactViewer';

function MyArtifactContent() {
  return (
    <ArtifactViewerScrollableContent maxHeight="400px">
      <ArtifactViewerSectionedContent
        sections={[
          {
            title: 'Overview',
            content: <div>Overview content here</div>,
            defaultExpanded: true,
          },
          {
            title: 'Details',
            content: <div>Detailed content here</div>,
            collapsible: true,
            defaultExpanded: false,
          },
        ]}
      />
    </ArtifactViewerScrollableContent>
  );
}
```

## State Management

The module uses Jotai for state management with atom families to handle multiple modal instances:

```tsx
import { useAtom } from 'jotai';
import { artifactViewerAtomFamily } from './components/message/artifactViewer';

function MyComponent() {
  const [viewerState] = useAtom(artifactViewerAtomFamily('my-artifact'));
  
  if (viewerState) {
    console.log('Modal is open:', viewerState.isOpen);
    console.log('Modal position:', viewerState.position);
    console.log('Modal size:', viewerState.size);
  }
}
```

## Responsive Behavior

The module automatically adapts to different screen sizes:

- **Desktop**: Full drag/resize functionality with size switcher
- **Tablet**: Limited dragging, responsive sizing
- **Mobile**: Fixed positioning, compact header, touch-friendly controls

## Keyboard Shortcuts

- **Escape**: Close the focused modal
- **Alt+Tab**: Cycle through open modals
- **Alt+Shift+Tab**: Cycle through open modals in reverse

## Customization

### Custom Themes

The module respects MUI theme settings and can be customized through theme overrides:

```tsx
const theme = createTheme({
  components: {
    MuiPaper: {
      styleOverrides: {
        root: {
          // Custom styles for modal papers
        },
      },
    },
  },
});
```

### Custom Icons

```tsx
import { CustomIcon } from './icons';

<ArtifactTriggerButton
  artifactId="custom-artifact"
  title="Custom Artifact"
  icon={<CustomIcon />}
>
  <MyContent />
</ArtifactTriggerButton>
```

## TypeScript Support

The module is fully typed with TypeScript. All components, hooks, and utilities include comprehensive type definitions.

## Performance Considerations

- Modal instances are only created when needed
- Unused modals are automatically cleaned up
- Responsive calculations are debounced to prevent excessive re-renders
- Content is only rendered when modals are open

## Browser Support

- Modern browsers with ES2018+ support
- React 18+
- Material-UI v5+
- Jotai v2+
