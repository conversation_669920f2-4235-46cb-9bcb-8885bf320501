import { Router } from 'express';
import {
  getChats<PERSON>ontroller,
  getChatByIdController,
  createChatController,
  updateChatController,
  deleteChatController,
} from '../controllers/chats';
import { validate } from '../middleware/validation';
import {
  createChatSchema,
  updateChatSchema,
  getChatSchema,
  deleteChatSchema,
} from '../validation/chats';
import { authenticate } from '../auth/middleware';
import messagesRouter from './messages';

const router = Router();

// All routes require authentication
router.use(authenticate);

// GET /chats - Get chats with pagination and filters
router.get('/', getChatsController);

// POST /chats - Create a new chat
router.post('/', validate(createChatSchema), createChatController);

// GET /chats/:chatId - Get chat by ID
router.get('/:chatId', validate(getChatSchema), getChatByIdController);

// PATCH /chats/:chatId - Update chat
router.patch('/:chatId', validate(updateChatSchema), updateChatController);

// DELETE /chats/:chatId - Delete chat
router.delete('/:chatId', validate(deleteChatSchema), deleteChatController);

// Nested routes for messages
router.use('/:chatId/messages', messagesRouter);

export default router;
