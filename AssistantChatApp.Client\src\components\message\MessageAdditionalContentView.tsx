import React, { useState } from "react";
import {
  <PERSON>,
  Button,
  Collapse,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Chip,
  Paper,
  Divider,
  Stack,
} from "@mui/material";
import {
  ExpandMore as ExpandMoreIcon,
  Functions as FunctionsIcon,
  PlayArrow as CallIcon,
  CheckCircle as ResultIcon,
} from "@mui/icons-material";
import { MessageAdditionalContent } from "../../types";
import { useTranslation } from "react-i18next";

interface MessageAdditionalContentProps {
  content: MessageAdditionalContent;
  isVisible?: boolean;
}

/** MUI `Accordion`-based component. Designed by Claude 4 Sonnet */
export const MessageAdditionalContentAccordionView: React.FC<
  MessageAdditionalContentProps
> = ({ content, isVisible = true }) => {
  const { t } = useTranslation("messages");
  const [expanded, setExpanded] = useState(false);

  if (
    !isVisible ||
    (!content.functionCalls?.length && !content.functionResults?.length)
  ) {
    return null;
  }

  const totalItems =
    (content.functionCalls?.length || 0) +
    (content.functionResults?.length || 0);

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  const formatJson = (jsonString: string) => {
    try {
      return JSON.stringify(JSON.parse(jsonString), null, 2);
    } catch {
      return jsonString;
    }
  };

  return (
    <Accordion
      expanded={expanded}
      onChange={handleToggle}
      sx={{
        border: "1px solid",
        borderColor: "divider",
        boxShadow: "none",
        "&:before": {
          display: "none",
        },
        "&.Mui-expanded": {
          margin: 0,
        },
      }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        sx={{
          minHeight: 40,
          "&.Mui-expanded": {
            minHeight: 40,
          },
          "& .MuiAccordionSummary-content": {
            margin: "8px 0",
            "&.Mui-expanded": {
              margin: "8px 0",
            },
          },
        }}
      >
        <Stack direction="row" alignItems="center" spacing={1}>
          <FunctionsIcon sx={{ fontSize: 18, color: "text.secondary" }} />
          <Typography variant="caption" color="text.secondary" fontWeight={500}>
            {t("other_content")}
          </Typography>
          <Chip
            label={totalItems}
            size="small"
            variant="outlined"
            sx={{
              height: 20,
              fontSize: "0.7rem",
              color: "text.secondary",
              borderColor: "text.secondary",
            }}
          />
        </Stack>
      </AccordionSummary>

      <AccordionDetails sx={{ pt: 0, pb: 2 }}>
        <Stack spacing={2}>
          {/* Function Calls Section */}
          {content.functionCalls && content.functionCalls.length > 0 && (
            <Box>
              <Stack
                direction="row"
                alignItems="center"
                spacing={1}
                sx={{ mb: 1.5 }}
              >
                <CallIcon sx={{ fontSize: 16, color: "primary.main" }} />
                <Typography
                  variant="subtitle2"
                  color="primary.main"
                  fontWeight={600}
                >
                  {t("function_calls")} ({content.functionCalls.length})
                </Typography>
              </Stack>
              <Stack spacing={1.5}>
                {content.functionCalls.map((call, index) => (
                  <Paper
                    key={`${call.callId}-${index}`}
                    variant="outlined"
                    sx={{ p: 2, backgroundColor: "background.paper" }}
                  >
                    <Stack spacing={1}>
                      <Stack
                        direction="row"
                        alignItems="center"
                        spacing={1}
                        flexWrap="wrap"
                      >
                        <Typography variant="body2" fontWeight={600}>
                          {call.functionName}
                        </Typography>
                        <Chip
                          label={call.pluginName}
                          size="small"
                          variant="filled"
                          color="primary"
                          sx={{ height: 20, fontSize: "0.7rem" }}
                        />
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{ ml: "auto" }}
                        >
                          {t("id")}: {call.callId}
                        </Typography>
                      </Stack>

                      {call.arguments && (
                        <Box>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ mb: 0.5, display: "block" }}
                          >
                            {t("arguments")}:
                          </Typography>
                          <Paper
                            variant="outlined"
                            sx={{
                              p: 1,
                              border: "1px solid",
                              borderColor: "grey.200",
                            }}
                          >
                            <Typography
                              variant="caption"
                              component="pre"
                              sx={{
                                fontFamily: "monospace",
                                fontSize: "0.75rem",
                                whiteSpace: "pre-wrap",
                                wordBreak: "break-word",
                                color: "text.primary",
                              }}
                            >
                              {formatJson(call.arguments)}
                            </Typography>
                          </Paper>
                        </Box>
                      )}
                    </Stack>
                  </Paper>
                ))}
              </Stack>
            </Box>
          )}

          {/* Divider between sections */}
          {content.functionCalls?.length && content.functionResults?.length && (
            <Divider sx={{ my: 1 }} />
          )}

          {/* Function Results Section */}
          {content.functionResults && content.functionResults.length > 0 && (
            <Box>
              <Stack
                direction="row"
                alignItems="center"
                spacing={1}
                sx={{ mb: 1.5 }}
              >
                <ResultIcon sx={{ fontSize: 16, color: "success.main" }} />
                <Typography
                  variant="subtitle2"
                  color="success.main"
                  fontWeight={600}
                >
                  {t("function_results")} ({content.functionResults.length})
                </Typography>
              </Stack>
              <Stack spacing={1.5}>
                {content.functionResults.map((result, index) => (
                  <Paper
                    key={`${result.callId}-${index}`}
                    variant="outlined"
                    sx={{ p: 2, backgroundColor: "background.paper" }}
                  >
                    <Stack spacing={1}>
                      <Typography variant="caption" color="text.secondary">
                        {t("call_id")}: {result.callId}
                      </Typography>

                      <Box>
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{ mb: 0.5, display: "block" }}
                        >
                          {t("result")}:
                        </Typography>
                        <Paper
                          variant="outlined"
                          sx={{
                            p: 1,
                            backgroundColor: "success.50",
                            border: "1px solid",
                            borderColor: "success.200",
                          }}
                        >
                          <Typography
                            variant="caption"
                            component="pre"
                            sx={{
                              fontFamily: "monospace",
                              fontSize: "0.75rem",
                              whiteSpace: "pre-wrap",
                              wordBreak: "break-word",
                              color: "text.primary",
                            }}
                          >
                            {formatJson(JSON.parse(result.result))}
                          </Typography>
                        </Paper>
                      </Box>
                    </Stack>
                  </Paper>
                ))}
              </Stack>
            </Box>
          )}
        </Stack>
      </AccordionDetails>
    </Accordion>
  );
};

/** MUI `Collapse`-based component. Designed by Llama 4 Maverick */
export const MessageAdditionalContentCollapseView: React.FC<
  MessageAdditionalContent
> = ({ functionCalls, functionResults }) => {
  const [open, setOpen] = useState(false);

  const handleToggle = () => {
    setOpen(!open);
  };

  const functionCallsCount = functionCalls?.length ?? 0;
  const functionResultsCount = functionResults?.length ?? 0;

  return (
    <Box sx={{ mt: 1 }}>
      <Button size="small" onClick={handleToggle}>
        {open
          ? "Hide Additional Content"
          : `Show Additional Content (${functionCallsCount} function calls, ${functionResultsCount} results)`}
      </Button>
      <Collapse in={open} timeout="auto" unmountOnExit>
        <Box sx={{ mt: 1, p: 1, bgcolor: "grey.100", borderRadius: 1 }}>
          {functionCalls && functionCalls.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2">Function Calls:</Typography>
              {functionCalls.map((call, index) => (
                <Box
                  key={index}
                  sx={{
                    mt: 0.5,
                    p: 1,
                    bgcolor: "white",
                    border: "1px solid",
                    borderColor: "grey.300",
                    borderRadius: 1,
                  }}
                >
                  <Typography variant="body2">
                    <strong>Function:</strong> {call.functionName} (
                    {call.pluginName})
                  </Typography>
                  <Typography variant="body2">
                    <strong>Arguments:</strong>
                  </Typography>
                  <pre
                    style={{
                      margin: 0,
                      whiteSpace: "pre-wrap",
                      wordBreak: "break-word",
                    }}
                  >
                    {call.arguments}
                  </pre>
                </Box>
              ))}
            </Box>
          )}

          {functionResults && functionResults.length > 0 && (
            <Box>
              <Typography variant="subtitle2">Function Results:</Typography>
              {functionResults.map((result, index) => (
                <Box
                  key={index}
                  sx={{
                    mt: 0.5,
                    p: 1,
                    bgcolor: "white",
                    border: "1px solid",
                    borderColor: "grey.300",
                    borderRadius: 1,
                  }}
                >
                  <Typography variant="body2">
                    <strong>Call ID:</strong> {result.callId}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Result:</strong>
                  </Typography>
                  <pre
                    style={{
                      margin: 0,
                      whiteSpace: "pre-wrap",
                      wordBreak: "break-word",
                    }}
                  >
                    {result.result}
                  </pre>
                </Box>
              ))}
            </Box>
          )}
        </Box>
      </Collapse>
    </Box>
  );
};
