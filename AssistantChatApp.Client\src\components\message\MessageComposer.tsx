import React, { useState, useRef } from 'react';
import {
  Box,
  Paper,
  Popper,
  Grow,
  ClickAwayListener,
  MenuList,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Stack,
} from '@mui/material';
import { MDXEditor, type MDXEditorMethods } from '@mdxeditor/editor';
import {
  headingsPlugin,
  linkPlugin,
  listsPlugin,
  markdownShortcutPlugin,
  quotePlugin,
  thematicBreakPlugin,
  toolbarPlugin,
  codeBlockPlugin,
  diffSourcePlugin,
  UndoRedo,
  BoldItalicUnderlineToggles,
  BlockTypeSelect,
  CreateLink,
  ListsToggle,
  CodeToggle,
  DiffSourceToggleWrapper,
} from '@mdxeditor/editor';
import '@mdxeditor/editor/style.css'

import { User } from '../../types';
import { useTranslation } from "react-i18next";
import { useSendMessage } from '../../mutations/message';
import { useGetChat } from '../../queries/chat';
import { SendMessageFab } from './editor/Utils';

interface MessageComposerProps {
  chatId: string;
}

interface MessageMDXEditorProps {
  editorRef: React.RefObject<MDXEditorMethods>;
  setMessage: React.Dispatch<React.SetStateAction<string>>;
  onMentionKeyDown: (e: KeyboardEvent) => void;
  chatParticipants: User[];
}

interface UserMentionSuggestionPopper {
  anchorEl: Element | null;
  onClose: () => void;
  users: User[];
  onUserSelect: (user: User) => void;
}

const MessageMDXEditor: React.FC<MessageMDXEditorProps> =
({
  editorRef, setMessage, onMentionKeyDown
}) => {
  const { t } = useTranslation("messages");

  return (
    <MDXEditor
      ref={editorRef}
      markdown=""
      placeholder={t("message_editing_placeholder")}
      onChange={(content) => {
        setMessage(content);
        onMentionKeyDown(new KeyboardEvent('keydown'));
      }}
      contentEditableClassName="prose"
      plugins={[
        headingsPlugin(),
        listsPlugin(),
        linkPlugin(),
        quotePlugin(),
        codeBlockPlugin(),
        thematicBreakPlugin(),
        markdownShortcutPlugin(),
        diffSourcePlugin({ viewMode: "rich-text", readOnlyDiff: true }),
        toolbarPlugin({
          toolbarContents: () => (
            <DiffSourceToggleWrapper options={[ "rich-text", "source" ]}>
              <UndoRedo />
              <BoldItalicUnderlineToggles />
              <BlockTypeSelect />
              <CreateLink />
              <ListsToggle />
              <CodeToggle />
            </DiffSourceToggleWrapper>
          )
        })
      ]}
    />
  )
}

const UserMentionSuggestionPopper: React.FC<UserMentionSuggestionPopper> = ({
  anchorEl,
  onClose,
  users,
  onUserSelect,
}) => {
  const isOpened = Boolean(anchorEl && users.length > 0);
  const { t } = useTranslation("messages");
  return (
    <Popper
      open={isOpened}
      anchorEl={anchorEl}
      placement="top-start"
      transition
      disablePortal
      sx={{ zIndex: 2 }}
    >
      {({ TransitionProps }) => (
        <Grow
          {...TransitionProps}
          style={{ transformOrigin: 'bottom left' }}
        >
          <Paper elevation={3} sx={{ width: 250, mt: 1 }}>
            <ClickAwayListener onClickAway={onClose}>
              <MenuList>
                {users.map((participant) => (
                  <MenuItem
                    key={participant.id}
                    onClick={() => onUserSelect(participant)}
                    dense
                  >
                    <ListItemIcon>
                      <Avatar
                        src={participant.avatar}
                        alt={participant.displayName}
                        sx={{ width: 24, height: 24 }}
                      />
                    </ListItemIcon>
                    <ListItemText primary={participant.displayName} />
                    {participant.isAI && (
                      <Box
                        component="span"
                        sx={{
                          fontSize: '0.65rem',
                          p: '2px 6px',
                          borderRadius: 1,
                          bgcolor: 'primary.main',
                          color: 'white',
                        }}
                      >
                        {t("ai_badge")}
                      </Box>
                    )}
                  </MenuItem>
                ))}
              </MenuList>
            </ClickAwayListener>
          </Paper>
        </Grow>
      )}
    </Popper>
  );
}

export const MessageComposer: React.FC<MessageComposerProps> = ({ chatId }) => {
  const [message, setMessage] = useState('');
  const [mentions, setMentions] = useState<string[]>([]);
  const [mentionPopperAnchorEl, setMentionPopperAnchorEl] = useState<null | Element>(null);
  const [mentionQuery, setMentionQuery] = useState('');
  const editorRef = useRef<MDXEditorMethods>(null);

  const { data: chat } = useGetChat(chatId);

  const onSendMessageSuccess = () => {
    // Clear message and mentions
    setMessage('');
    setMentions([]);
    if (editorRef.current) {
      editorRef.current.setMarkdown('');
    }
  }

  // Send message mutation
  const sendMessageMutation = useSendMessage(onSendMessageSuccess);

  const handleSendMessage = React.useCallback(() => {
    if (message.trim()) {
      sendMessageMutation.mutate({ chatId, textContent: message, mentions });
    }
  }, [sendMessageMutation, message, mentions, chatId]);

  // Handle mentions
  const handleMentionKeyDown = () => {
    // Check if the input includes @
    const content = editorRef.current?.getMarkdown() || '';
    const atIndex = content.lastIndexOf('@');

    if (atIndex !== -1) {
      const query = content.slice(atIndex + 1).split(/\s/)[0];
      setMentionQuery(query);

      if (!mentionPopperAnchorEl) {
        // Position the popper near the cursor
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();
          const element = document.elementFromPoint(rect.left, rect.top);
          if (element) {
            setMentionPopperAnchorEl(element);
          }
        }
      }
    } else {
      handleCloseMentionPopper();
    }
  };

  const handleCloseMentionPopper = () => {
    setMentionPopperAnchorEl(null);
    setMentionQuery('');
  };

  const handleSelectMention = (user: User) => {
    // Replace the @query with @userId
    const content = editorRef.current?.getMarkdown() || '';
    const atIndex = content.lastIndexOf('@');

    if (atIndex !== -1) {
      const beforeAt = content.substring(0, atIndex);
      const afterQuery = content.substring(atIndex + 1 + mentionQuery.length);

      const newContent = `${beforeAt}@${user.id} ${afterQuery}`;
      editorRef.current?.setMarkdown(newContent);

      // Add to mentions array if not already added
      if (!mentions.includes(user.id)) {
        setMentions([...mentions, user.id]);
      }
    }

    handleCloseMentionPopper();
  };

  // Filter participants based on mention query
  const filteredParticipants = React.useMemo(() => {
    if (!chat?.participants || !mentionQuery) return [];

    return chat.participants.filter(participant =>
      participant.displayName.toLowerCase().includes(mentionQuery.toLowerCase())
    ).slice(0, 5); // Limit to 5 suggestions
  }, [chat?.participants, mentionQuery]);

  return (
    <Stack direction="row" spacing={1} alignItems="center">
      <Paper
        variant="outlined"
        sx={{
          p: 1,
          minHeight: 100,
          maxHeight: 300,
          overflowY: 'auto',
          flexGrow: 1,
        }}
      >
        <MessageMDXEditor
          editorRef={editorRef}
          setMessage={setMessage}
          onMentionKeyDown={handleMentionKeyDown}
          chatParticipants={chat?.participants ?? []}
        />
      </Paper>

      <SendMessageFab
        onClick={handleSendMessage}
        isPending={sendMessageMutation.isPending}
        isDisabled={!message.trim() || sendMessageMutation.isPending}
      />

      {/* Mention suggestions popper */}
      <UserMentionSuggestionPopper
        anchorEl={mentionPopperAnchorEl}
        onClose={handleCloseMentionPopper}
        users={filteredParticipants}
        onUserSelect={handleSelectMention}
      />
    </Stack>
  );
};
