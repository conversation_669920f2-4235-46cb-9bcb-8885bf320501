/* tslint:disable */
/* eslint-disable */
/**
 * KernelMemory.WebService, Version=*******, Culture=neutral, PublicKeyToken=null
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * 
 * @export
 * @interface Citation
 */
export interface Citation {
    /**
     * 
     * @type {string}
     * @memberof Citation
     */
    'link'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Citation
     */
    'index'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Citation
     */
    'documentId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Citation
     */
    'fileId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Citation
     */
    'sourceContentType'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Citation
     */
    'sourceName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Citation
     */
    'sourceUrl'?: string | null;
    /**
     * 
     * @type {Array<Partition>}
     * @memberof Citation
     */
    'partitions'?: Array<Partition> | null;
}
/**
 * 
 * @export
 * @interface DataPipelineStatus
 */
export interface DataPipelineStatus {
    /**
     * 
     * @type {boolean}
     * @memberof DataPipelineStatus
     */
    'completed'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof DataPipelineStatus
     */
    'empty'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof DataPipelineStatus
     */
    'index'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DataPipelineStatus
     */
    'document_id'?: string | null;
    /**
     * 
     * @type {{ [key: string]: Array<string>; }}
     * @memberof DataPipelineStatus
     */
    'tags'?: { [key: string]: Array<string>; } | null;
    /**
     * 
     * @type {string}
     * @memberof DataPipelineStatus
     */
    'creation'?: string;
    /**
     * 
     * @type {string}
     * @memberof DataPipelineStatus
     */
    'last_update'?: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof DataPipelineStatus
     */
    'steps'?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof DataPipelineStatus
     */
    'remaining_steps'?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof DataPipelineStatus
     */
    'completed_steps'?: Array<string> | null;
}
/**
 * 
 * @export
 * @interface DeleteAccepted
 */
export interface DeleteAccepted {
    /**
     * 
     * @type {string}
     * @memberof DeleteAccepted
     */
    'index'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DeleteAccepted
     */
    'documentId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DeleteAccepted
     */
    'message'?: string | null;
}
/**
 * 
 * @export
 * @interface IndexCollection
 */
export interface IndexCollection {
    /**
     * 
     * @type {Array<IndexDetails>}
     * @memberof IndexCollection
     */
    'results'?: Array<IndexDetails> | null;
}
/**
 * 
 * @export
 * @interface IndexDetails
 */
export interface IndexDetails {
    /**
     * 
     * @type {string}
     * @memberof IndexDetails
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface MemoryAnswer
 */
export interface MemoryAnswer {
    /**
     * 
     * @type {StreamStates}
     * @memberof MemoryAnswer
     */
    'streamState'?: StreamStates;
    /**
     * 
     * @type {string}
     * @memberof MemoryAnswer
     */
    'question'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof MemoryAnswer
     */
    'noResult'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof MemoryAnswer
     */
    'noResultReason'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MemoryAnswer
     */
    'text'?: string | null;
    /**
     * 
     * @type {Array<TokenUsage>}
     * @memberof MemoryAnswer
     */
    'tokenUsage'?: Array<TokenUsage> | null;
    /**
     * 
     * @type {Array<Citation>}
     * @memberof MemoryAnswer
     */
    'relevantSources'?: Array<Citation> | null;
}


/**
 * 
 * @export
 * @interface MemoryQuery
 */
export interface MemoryQuery {
    /**
     * 
     * @type {string}
     * @memberof MemoryQuery
     */
    'index'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MemoryQuery
     */
    'question'?: string | null;
    /**
     * 
     * @type {Array<{ [key: string]: Array<string>; }>}
     * @memberof MemoryQuery
     */
    'filters'?: Array<{ [key: string]: Array<string>; }> | null;
    /**
     * 
     * @type {number}
     * @memberof MemoryQuery
     */
    'minRelevance'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof MemoryQuery
     */
    'stream'?: boolean;
    /**
     * 
     * @type {{ [key: string]: any | null; }}
     * @memberof MemoryQuery
     */
    'args'?: { [key: string]: any | null; } | null;
}
/**
 * 
 * @export
 * @interface Partition
 */
export interface Partition {
    /**
     * 
     * @type {string}
     * @memberof Partition
     */
    'text'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof Partition
     */
    'relevance'?: number;
    /**
     * 
     * @type {number}
     * @memberof Partition
     */
    'partitionNumber'?: number;
    /**
     * 
     * @type {number}
     * @memberof Partition
     */
    'sectionNumber'?: number;
    /**
     * 
     * @type {string}
     * @memberof Partition
     */
    'lastUpdate'?: string;
    /**
     * 
     * @type {{ [key: string]: Array<string>; }}
     * @memberof Partition
     */
    'tags'?: { [key: string]: Array<string>; } | null;
}
/**
 * 
 * @export
 * @interface ProblemDetails
 */
export interface ProblemDetails {
    [key: string]: any;

    /**
     * 
     * @type {string}
     * @memberof ProblemDetails
     */
    'type'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ProblemDetails
     */
    'title'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof ProblemDetails
     */
    'status'?: number | null;
    /**
     * 
     * @type {string}
     * @memberof ProblemDetails
     */
    'detail'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ProblemDetails
     */
    'instance'?: string | null;
}
/**
 * 
 * @export
 * @interface SearchQuery
 */
export interface SearchQuery {
    /**
     * 
     * @type {string}
     * @memberof SearchQuery
     */
    'index'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchQuery
     */
    'query'?: string | null;
    /**
     * 
     * @type {Array<{ [key: string]: Array<string>; }>}
     * @memberof SearchQuery
     */
    'filters'?: Array<{ [key: string]: Array<string>; }> | null;
    /**
     * 
     * @type {number}
     * @memberof SearchQuery
     */
    'minRelevance'?: number;
    /**
     * 
     * @type {number}
     * @memberof SearchQuery
     */
    'limit'?: number;
    /**
     * 
     * @type {{ [key: string]: any | null; }}
     * @memberof SearchQuery
     */
    'args'?: { [key: string]: any | null; } | null;
}
/**
 * 
 * @export
 * @interface SearchResult
 */
export interface SearchResult {
    /**
     * 
     * @type {string}
     * @memberof SearchResult
     */
    'query'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof SearchResult
     */
    'noResult'?: boolean;
    /**
     * 
     * @type {Array<Citation>}
     * @memberof SearchResult
     */
    'results'?: Array<Citation> | null;
}
/**
 * 
 * @export
 * @enum {string}
 */

export const StreamStates = {
    Error: 'error',
    Reset: 'reset',
    Append: 'append',
    Last: 'last'
} as const;

export type StreamStates = typeof StreamStates[keyof typeof StreamStates];


/**
 * 
 * @export
 * @interface TokenUsage
 */
export interface TokenUsage {
    /**
     * 
     * @type {string}
     * @memberof TokenUsage
     */
    'timestamp'?: string;
    /**
     * 
     * @type {string}
     * @memberof TokenUsage
     */
    'serviceType'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TokenUsage
     */
    'modelType'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TokenUsage
     */
    'modelName'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof TokenUsage
     */
    'tokenizerTokensIn'?: number | null;
    /**
     * 
     * @type {number}
     * @memberof TokenUsage
     */
    'tokenizerTokensOut'?: number | null;
    /**
     * 
     * @type {number}
     * @memberof TokenUsage
     */
    'serviceTokensIn'?: number | null;
    /**
     * 
     * @type {number}
     * @memberof TokenUsage
     */
    'serviceTokensOut'?: number | null;
    /**
     * 
     * @type {number}
     * @memberof TokenUsage
     */
    'serviceReasoningTokens'?: number | null;
}
/**
 * 
 * @export
 * @interface UploadAccepted
 */
export interface UploadAccepted {
    /**
     * 
     * @type {string}
     * @memberof UploadAccepted
     */
    'index'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UploadAccepted
     */
    'documentId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UploadAccepted
     */
    'message'?: string | null;
}

/**
 * KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi - axios parameter creator
 * @export
 */
export const KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Answer a user question using the internal knowledge base.
         * @summary Use the memories extracted from the files uploaded to generate an answer. The query can include filters to use only a subset of the memories available.
         * @param {MemoryQuery} memoryQuery 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        answerQuestion: async (memoryQuery: MemoryQuery, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'memoryQuery' is not null or undefined
            assertParamExists('answerQuestion', 'memoryQuery', memoryQuery)
            const localVarPath = `/ask`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(memoryQuery, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Check the status of a file upload in progress.
         * @summary Check the status of a file upload in progress. When uploading a document, which can consist of multiple files, each file goes through multiple steps. The status include details about which steps are completed.
         * @param {string} documentId 
         * @param {string} [index] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        checkDocumentStatus: async (documentId: string, index?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'documentId' is not null or undefined
            assertParamExists('checkDocumentStatus', 'documentId', documentId)
            const localVarPath = `/upload-status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (index !== undefined) {
                localVarQueryParameter['index'] = index;
            }

            if (documentId !== undefined) {
                localVarQueryParameter['documentId'] = documentId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete a document from the knowledge base.
         * @summary Delete a document from the knowledge base. When deleting a document, which can consist of multiple files, all the memories previously extracted are deleted too.
         * @param {string} documentId 
         * @param {string} [index] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDocumentById: async (documentId: string, index?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'documentId' is not null or undefined
            assertParamExists('deleteDocumentById', 'documentId', documentId)
            const localVarPath = `/documents`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (index !== undefined) {
                localVarQueryParameter['index'] = index;
            }

            if (documentId !== undefined) {
                localVarQueryParameter['documentId'] = documentId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete a container of documents (aka \'index\') from the knowledge base.
         * @summary Delete a container of documents (aka \'index\') from the knowledge base. Indexes are collections of memories extracted from the documents uploaded.
         * @param {string} [index] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteIndexByName: async (index?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/indexes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (index !== undefined) {
                localVarQueryParameter['index'] = index;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get the list of containers (aka \'indexes\') from the knowledge base.
         * @summary Get the list of containers (aka \'indexes\') from the knowledge base. Each index has a unique name. Indexes are collections of memories extracted from the documents uploaded.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listIndexes: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/indexes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Search the knowledge base for relevant snippets of text.
         * @summary Search the knowledge base for relevant snippets of text. The search can include filters to use only a subset of the knowledge base.
         * @param {SearchQuery} searchQuery 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchDocumentSnippets: async (searchQuery: SearchQuery, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'searchQuery' is not null or undefined
            assertParamExists('searchDocumentSnippets', 'searchQuery', searchQuery)
            const localVarPath = `/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchQuery, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Upload a document consisting of one or more files to extract memories from. The extraction process happens asynchronously. If a document with the same ID already exists, it will be overwritten and the memories previously extracted will be updated.
         * @summary Upload a new document to the knowledge base
         * @param {string} [index] Name of the index where to store memories generated by the files.
         * @param {string} [documentId] Unique ID used for import pipeline and document ID.
         * @param {Array<string>} [tags] Tags to apply to the memories extracted from the files.
         * @param {Array<string>} [steps] How to process the files, e.g. how to extract/chunk etc.
         * @param {Array<File>} [files] Files to process and extract memories from.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadDocument: async (index?: string, documentId?: string, tags?: Array<string>, steps?: Array<string>, files?: Array<File>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/upload`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();


            if (index !== undefined) { 
                localVarFormParams.append('index', index as any);
            }
    
            if (documentId !== undefined) { 
                localVarFormParams.append('documentId', documentId as any);
            }
                if (tags) {
                tags.forEach((element) => {
                    localVarFormParams.append('tags', element as any);
                })
            }

                if (steps) {
                steps.forEach((element) => {
                    localVarFormParams.append('steps', element as any);
                })
            }

                if (files) {
                files.forEach((element) => {
                    localVarFormParams.append('files', element as any);
                })
            }

    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi - functional programming interface
 * @export
 */
export const KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiAxiosParamCreator(configuration)
    return {
        /**
         * Answer a user question using the internal knowledge base.
         * @summary Use the memories extracted from the files uploaded to generate an answer. The query can include filters to use only a subset of the memories available.
         * @param {MemoryQuery} memoryQuery 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async answerQuestion(memoryQuery: MemoryQuery, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<MemoryAnswer>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.answerQuestion(memoryQuery, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi.answerQuestion']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Check the status of a file upload in progress.
         * @summary Check the status of a file upload in progress. When uploading a document, which can consist of multiple files, each file goes through multiple steps. The status include details about which steps are completed.
         * @param {string} documentId 
         * @param {string} [index] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async checkDocumentStatus(documentId: string, index?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DataPipelineStatus>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.checkDocumentStatus(documentId, index, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi.checkDocumentStatus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete a document from the knowledge base.
         * @summary Delete a document from the knowledge base. When deleting a document, which can consist of multiple files, all the memories previously extracted are deleted too.
         * @param {string} documentId 
         * @param {string} [index] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteDocumentById(documentId: string, index?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DeleteAccepted>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteDocumentById(documentId, index, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi.deleteDocumentById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete a container of documents (aka \'index\') from the knowledge base.
         * @summary Delete a container of documents (aka \'index\') from the knowledge base. Indexes are collections of memories extracted from the documents uploaded.
         * @param {string} [index] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteIndexByName(index?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DeleteAccepted>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteIndexByName(index, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi.deleteIndexByName']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get the list of containers (aka \'indexes\') from the knowledge base.
         * @summary Get the list of containers (aka \'indexes\') from the knowledge base. Each index has a unique name. Indexes are collections of memories extracted from the documents uploaded.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listIndexes(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<IndexCollection>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listIndexes(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi.listIndexes']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Search the knowledge base for relevant snippets of text.
         * @summary Search the knowledge base for relevant snippets of text. The search can include filters to use only a subset of the knowledge base.
         * @param {SearchQuery} searchQuery 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async searchDocumentSnippets(searchQuery: SearchQuery, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SearchResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.searchDocumentSnippets(searchQuery, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi.searchDocumentSnippets']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Upload a document consisting of one or more files to extract memories from. The extraction process happens asynchronously. If a document with the same ID already exists, it will be overwritten and the memories previously extracted will be updated.
         * @summary Upload a new document to the knowledge base
         * @param {string} [index] Name of the index where to store memories generated by the files.
         * @param {string} [documentId] Unique ID used for import pipeline and document ID.
         * @param {Array<string>} [tags] Tags to apply to the memories extracted from the files.
         * @param {Array<string>} [steps] How to process the files, e.g. how to extract/chunk etc.
         * @param {Array<File>} [files] Files to process and extract memories from.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async uploadDocument(index?: string, documentId?: string, tags?: Array<string>, steps?: Array<string>, files?: Array<File>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.uploadDocument(index, documentId, tags, steps, files, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi.uploadDocument']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi - factory interface
 * @export
 */
export const KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiFp(configuration)
    return {
        /**
         * Answer a user question using the internal knowledge base.
         * @summary Use the memories extracted from the files uploaded to generate an answer. The query can include filters to use only a subset of the memories available.
         * @param {MemoryQuery} memoryQuery 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        answerQuestion(memoryQuery: MemoryQuery, options?: RawAxiosRequestConfig): AxiosPromise<MemoryAnswer> {
            return localVarFp.answerQuestion(memoryQuery, options).then((request) => request(axios, basePath));
        },
        /**
         * Check the status of a file upload in progress.
         * @summary Check the status of a file upload in progress. When uploading a document, which can consist of multiple files, each file goes through multiple steps. The status include details about which steps are completed.
         * @param {string} documentId 
         * @param {string} [index] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        checkDocumentStatus(documentId: string, index?: string, options?: RawAxiosRequestConfig): AxiosPromise<DataPipelineStatus> {
            return localVarFp.checkDocumentStatus(documentId, index, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete a document from the knowledge base.
         * @summary Delete a document from the knowledge base. When deleting a document, which can consist of multiple files, all the memories previously extracted are deleted too.
         * @param {string} documentId 
         * @param {string} [index] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDocumentById(documentId: string, index?: string, options?: RawAxiosRequestConfig): AxiosPromise<DeleteAccepted> {
            return localVarFp.deleteDocumentById(documentId, index, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete a container of documents (aka \'index\') from the knowledge base.
         * @summary Delete a container of documents (aka \'index\') from the knowledge base. Indexes are collections of memories extracted from the documents uploaded.
         * @param {string} [index] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteIndexByName(index?: string, options?: RawAxiosRequestConfig): AxiosPromise<DeleteAccepted> {
            return localVarFp.deleteIndexByName(index, options).then((request) => request(axios, basePath));
        },
        /**
         * Get the list of containers (aka \'indexes\') from the knowledge base.
         * @summary Get the list of containers (aka \'indexes\') from the knowledge base. Each index has a unique name. Indexes are collections of memories extracted from the documents uploaded.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listIndexes(options?: RawAxiosRequestConfig): AxiosPromise<IndexCollection> {
            return localVarFp.listIndexes(options).then((request) => request(axios, basePath));
        },
        /**
         * Search the knowledge base for relevant snippets of text.
         * @summary Search the knowledge base for relevant snippets of text. The search can include filters to use only a subset of the knowledge base.
         * @param {SearchQuery} searchQuery 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchDocumentSnippets(searchQuery: SearchQuery, options?: RawAxiosRequestConfig): AxiosPromise<SearchResult> {
            return localVarFp.searchDocumentSnippets(searchQuery, options).then((request) => request(axios, basePath));
        },
        /**
         * Upload a document consisting of one or more files to extract memories from. The extraction process happens asynchronously. If a document with the same ID already exists, it will be overwritten and the memories previously extracted will be updated.
         * @summary Upload a new document to the knowledge base
         * @param {string} [index] Name of the index where to store memories generated by the files.
         * @param {string} [documentId] Unique ID used for import pipeline and document ID.
         * @param {Array<string>} [tags] Tags to apply to the memories extracted from the files.
         * @param {Array<string>} [steps] How to process the files, e.g. how to extract/chunk etc.
         * @param {Array<File>} [files] Files to process and extract memories from.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadDocument(index?: string, documentId?: string, tags?: Array<string>, steps?: Array<string>, files?: Array<File>, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.uploadDocument(index, documentId, tags, steps, files, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi - object-oriented interface
 * @export
 * @class KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi
 * @extends {BaseAPI}
 */
export class KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi extends BaseAPI {
    /**
     * Answer a user question using the internal knowledge base.
     * @summary Use the memories extracted from the files uploaded to generate an answer. The query can include filters to use only a subset of the memories available.
     * @param {MemoryQuery} memoryQuery 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi
     */
    public answerQuestion(memoryQuery: MemoryQuery, options?: RawAxiosRequestConfig) {
        return KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiFp(this.configuration).answerQuestion(memoryQuery, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Check the status of a file upload in progress.
     * @summary Check the status of a file upload in progress. When uploading a document, which can consist of multiple files, each file goes through multiple steps. The status include details about which steps are completed.
     * @param {string} documentId 
     * @param {string} [index] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi
     */
    public checkDocumentStatus(documentId: string, index?: string, options?: RawAxiosRequestConfig) {
        return KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiFp(this.configuration).checkDocumentStatus(documentId, index, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete a document from the knowledge base.
     * @summary Delete a document from the knowledge base. When deleting a document, which can consist of multiple files, all the memories previously extracted are deleted too.
     * @param {string} documentId 
     * @param {string} [index] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi
     */
    public deleteDocumentById(documentId: string, index?: string, options?: RawAxiosRequestConfig) {
        return KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiFp(this.configuration).deleteDocumentById(documentId, index, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete a container of documents (aka \'index\') from the knowledge base.
     * @summary Delete a container of documents (aka \'index\') from the knowledge base. Indexes are collections of memories extracted from the documents uploaded.
     * @param {string} [index] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi
     */
    public deleteIndexByName(index?: string, options?: RawAxiosRequestConfig) {
        return KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiFp(this.configuration).deleteIndexByName(index, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get the list of containers (aka \'indexes\') from the knowledge base.
     * @summary Get the list of containers (aka \'indexes\') from the knowledge base. Each index has a unique name. Indexes are collections of memories extracted from the documents uploaded.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi
     */
    public listIndexes(options?: RawAxiosRequestConfig) {
        return KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiFp(this.configuration).listIndexes(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Search the knowledge base for relevant snippets of text.
     * @summary Search the knowledge base for relevant snippets of text. The search can include filters to use only a subset of the knowledge base.
     * @param {SearchQuery} searchQuery 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi
     */
    public searchDocumentSnippets(searchQuery: SearchQuery, options?: RawAxiosRequestConfig) {
        return KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiFp(this.configuration).searchDocumentSnippets(searchQuery, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Upload a document consisting of one or more files to extract memories from. The extraction process happens asynchronously. If a document with the same ID already exists, it will be overwritten and the memories previously extracted will be updated.
     * @summary Upload a new document to the knowledge base
     * @param {string} [index] Name of the index where to store memories generated by the files.
     * @param {string} [documentId] Unique ID used for import pipeline and document ID.
     * @param {Array<string>} [tags] Tags to apply to the memories extracted from the files.
     * @param {Array<string>} [steps] How to process the files, e.g. how to extract/chunk etc.
     * @param {Array<File>} [files] Files to process and extract memories from.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApi
     */
    public uploadDocument(index?: string, documentId?: string, tags?: Array<string>, steps?: Array<string>, files?: Array<File>, options?: RawAxiosRequestConfig) {
        return KernelMemoryWebServiceVersion1000CultureneutralPublicKeyTokennullApiFp(this.configuration).uploadDocument(index, documentId, tags, steps, files, options).then((request) => request(this.axios, this.basePath));
    }
}



