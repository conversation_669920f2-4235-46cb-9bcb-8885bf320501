import { apiClient } from './apiClient';
import { PaginatedResponse, User } from '../types';

export const userApi = {
  // Get users with pagination
  getUsers: async (page = 1, limit = 20, search = ''): Promise<PaginatedResponse<User>> => {
    return apiClient.getUsers(page, limit, search);
  },

  // Get a single user by ID
  getUser: async (userId: string): Promise<User> => {
    return apiClient.getUser(userId);
  },

  // Get AI assistants
  getAIAssistants: async (): Promise<User[]> => {
    return apiClient.getAIAssistants();
  },
};
