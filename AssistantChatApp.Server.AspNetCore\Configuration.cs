using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AssistantChatApp.Server.AspNetCore
{
    public static class ConfigKeys
    {
        public const string JwtSettings = "JwtSettings";
    }

    public class RootSettings
    {
        public string? AllowedOrigins { get; set; }
    }

    public class AiAgentSettings
    {
        public string? BaseUrl { get; set; }
    }

    public class JwtSettings
    {
        public string? Secret { get; set; }
        public string? Issuer { get; set; }
        public string? Audience { get; set; }
    }

    public class ConnectionStrings
    {
        public string? DefaultConnection { get; set; }
    }
}
