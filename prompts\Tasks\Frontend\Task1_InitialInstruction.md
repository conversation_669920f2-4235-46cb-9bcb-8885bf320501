# Full Instruction

Develop a web frontend for an AI assistant chat application. The goal of this project is to create a client-only app that is highly modular, easy to adjust (especially regarding backend communication), and offers a smooth user experience.

---

## 1. Tech Stack & Tools

- **Language:** TypeScript
- **Package Manager:** pnpm
- **Framework:** React.js
- **User Interface:** Material UI (MUI – `@mui/material`)
- **Asynchronous Data & Caching:** TanStack Query (i.e. `@tanstack/react-query`)
- **Global Client State:** Jotai (`jotai`)
- **Client Routing:** TanStack Router (`@tanstack/react-router`)
- **Forms Management:** TanStack Form (`@tanstack/react-form`)
- **Rich Text Editor:** MDXEditor (`@mdxeditor/editor`)
- **Resizable Layout**: react resizable panels (`react-resizable-panels`)

---

## 2. Communication with the Backend

- **Protocol:** Use a simple HTTP API (e.g., REST).
- **Abstraction:** The backend communication must be decoupled from the UI. There should be a dedicated module (or set of modules) responsible for handling all HTTP interactions. This module should be easy to replace or mock for testing purposes.
- **Response Feedback:** Although streaming responses are not required, the UI must provide feedback (e.g., a loading spinner or progress indicator) when awaiting long-running responses, especially for AI assistant responses.
- **Security:**
  - Use a simple JWT-based solution for authentication.
  - The login process should obtain a JWT token from a designated login endpoint.
  - Outline a straightforward storage mechanism (e.g., secure HttpOnly cookies or in-memory) for the JWT token.
  - Ensure that the token is automatically attached to subsequent HTTP requests.
  - No need for token refresh mechanisms at this stage, but be mindful of secure token handling.

---

## 3. Functionality Requirements

### A. Chat & Conversation Mechanics

- **Group Conversations:**
  - Multiple users can engage in the same conversation simultaneously.
  - The AI assistant should be invoked either automatically (when it's one-on-one conversation between the user and the AI assistant), or through an explicit mention (using the `@` prefix, similar to common chat applications).
  - Ergonomic Chat List View: Displays a list of available chats, allowing users to select and enter conversations. Besides that, allows searching and filtering chat by its metadata attributes (title, description, participants, tags, timestamps).

### B. Message Composition & Display

- **Rich Text Support:**
  - Completed messages should be rendered as HTML with support for Markdown formatting.
  - While editing messages, users should have the option to switch between raw Markdown source editing and WYSIWYG editing (in MDXEditor, it's configurable via `diffSourcePlugin`).
- **User Mentions:**
  - When a message includes user mentions, these should be highlighted.
  - On hover or click (at least in the rendered state), a user summary card popup should display additional details about the mentioned user.

### C. Authentication

- **Login/Logout:**
  - Implement a simple login and logout UI flow.
  - No registration/sign up functionality is required at this stage.

### D. Data Management

- **Client Data Handling:**
  - The client should manage and store only the necessary operational data.
  - All primary data is to be fetched from the backend. Use TanStack Query's default configuration for basic caching to ensure a smoother user experience.
  - Keep state management (via Jotai) and data fetching (via TanStack Query) clearly separated from the UI, for maintainability and testing convenience.

### E. UI Design & Layout

- Follow MUI best practices for styling, i.e. use theme as main style configuration mechanism. Although, some fine-tuning via inline styles are allowed.
- **Responsive Design:** Ensure the UI is responsive and works well on both desktop and mobile devices. I.e., leverage MUI's layout components, like `Grid`, `Stack`, `Container`, `Box`, plus native CSS mechanisms like Flexbox.
- **Resizable Layout:** Utilize the `react-resizable-panels` library to allow users to adjust the layout of the chat window and other UI parts that can benefit from dynamic resizing.

---

## 4. Main Data Entities

### A. Chat
- **Purpose:** Represents a chronological sequence of messages.
- **Attributes:**
  - Unique ID
  - Title
  - Description
  - Participants
  - Creation Timestamp
  - Last Update Timestamp
  - Tags (a list to help categorize chats for better search and filtering)

### B. Message
- **Purpose:** Represents a single message within a conversation.
- **Attributes:**
  - Unique ID
  - Author (link to User)
  - Creation Timestamp
  - Modification Timestamp
  - Content (currently Markdown text; future considerations for attachments)

### C. User
- **Purpose:** Represents a participant in the chat (can be either a human user or an AI assistant).
- **Attributes:**
  - Unique ID
  - Display Name
  - Avatar
  - Creation Timestamp
  - Flag to differentiate between human and AI users

---

## 5. Additional Considerations

- **Component Architecture:**
  - Design components to clearly separate data-fetching logic from UI rendering (e.g., via custom hooks).
  - Consider a high-level structure with components such as chat list, chat window, message composer, and user summary card.

- **Error Handling:**
  - Define how the app should display error messages in case of API request failures.
  - Decide on a fallback UI or notifications for network issues or authentication errors.

- **Testing Strategy:**
  - Initial testing will be manual, given the relatively simple and UI-focused nature of the app.
  - Future iterations should consider adding automated unit and integration tests for critical functionality.

------

# Instruction Summary

Create a modern web frontend for an AI chat application with the following specifications:

Create a React + TypeScript application that implements a chat interface for communicating with AI assistants. The application should follow these detailed requirements:

Technical Requirements:
- Use TypeScript with strict mode enabled
- Initialize project using pnpm and Vite
- Implement core dependencies:
  - React 18+ for UI
  - Material UI v5 for component library
  - TanStack Query v5 for data fetching/caching
  - Jotai for global state management
  - TanStack Router for routing
  - TanStack Form for form handling
  - MDXEditor for rich text editing
  - react-resizable-panels for adjustable layouts

Architecture Requirements:
1. Create a modular architecture with clear separation between:
   - UI components
   - Data fetching layer
   - State management
   - Backend communication
   - Authentication handling

2. Implement key features:
   - JWT-based authentication system
   - Rich text message composition with Markdown support
   - Real-time chat updates
   - Resizable panels for chat list and conversation view
   - User mention system with popup cards
   - Search and filter functionality for chat list

3. Define core data models:
   - Chat: {id: string, title: string, description: string, participants: User[], createdAt: Date, updatedAt: Date, tags: string[]}
   - Message: {id: string, authorId: string, content: string, createdAt: Date, updatedAt: Date}
   - User: {id: string, displayName: string, avatarUrl: string, createdAt: Date, isAI: boolean}

4. Build UI components:
   - ChatList: Displays available conversations with search/filter
   - ChatWindow: Shows current conversation with message history
   - MessageComposer: Rich text editor with mention support
   - UserCard: Popup component for user information
   - AuthenticationFlow: Login/logout functionality

5. Implement error handling and loading states:
   - Show loading indicators during API calls
   - Display error messages for failed requests
   - Implement fallback UI for network issues

The final deliverable should be a production-ready, maintainable codebase that follows React best practices and provides a smooth user experience for AI chat interactions.
