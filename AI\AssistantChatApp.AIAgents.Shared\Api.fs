namespace AssistantChatApp.AIAgents.Shared.Api

open AssistantChatApp.AIAgents.Shared

type ChatUser = {
    UserId: string
    UserName: string
}

type AgentUser = ChatUser
type InquirerUser = ChatUser

type AgentApi = {
    GetChatCompletion: AgentUser -> InquirerUser -> ChatMessage [] -> Async<ChatMessage []>
}

type ChatAppApi = {
    SendMessages: ChatMessage [] -> Async<bool []>
}

module Routing =

    let apiRouteBuilder typeName methodName =
        sprintf "/api/%s/%s" typeName methodName
