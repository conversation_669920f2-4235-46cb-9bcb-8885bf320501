import { createRootRoute, createRoute } from '@tanstack/react-router';
import Layout from '../components/layout/Layout';
import LoginPage from '../pages/LoginPage';
import ChatListPage from '../pages/ChatListPage';
import ChatPage from '../pages/ChatPage';
import NotFoundPage from '../pages/NotFoundPage';
import ProtectedRoute from '../components/common/ProtectedRoute';
import { UserProfilePage } from '../pages/UserProfilePage';

// Root route with layout
export const rootRoute = createRootRoute({
  component: Layout,
});

// Login route - public
export const loginRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/login',
  component: LoginPage,
});

// Index route redirects to chat list
export const indexRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: () => <ProtectedRoute><ChatListPage /></ProtectedRoute>,
});

// Chat list route - protected
export const chatListRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/chats',
  component: () => <ProtectedRoute><ChatListPage /></ProtectedRoute>,
});

// Chat detail route - protected
export const chatRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/chats/$chatId',
  component: () => <ProtectedRoute><ChatPage /></ProtectedRoute>,
});

export const userRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/users/me',
  component: () => <ProtectedRoute><UserProfilePage /></ProtectedRoute>,
});

// Not found route
export const notFoundRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '*',
  component: NotFoundPage,
});

// Create route tree
export const routeTree = rootRoute.addChildren([
  indexRoute,
  loginRoute,
  chatListRoute,
  chatRoute,
  userRoute,
  notFoundRoute,
]);
