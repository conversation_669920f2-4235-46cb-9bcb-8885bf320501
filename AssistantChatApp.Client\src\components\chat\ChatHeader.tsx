import React, { useState } from "react";
import {
  <PERSON>,
  Typography,
  IconButton,
  Avatar,
  Chip,
  Tooltip,
  AvatarGroup,
  Stack,
} from "@mui/material";
import {
  MoreVertical as MoreIcon,
  Users as UsersIcon,
  ArrowLeft as ArrowLeftIcon,
} from "lucide-react";
import { Link } from "@tanstack/react-router";
import { Chat } from "../../types";
import { ChatMenu } from "./ChatMenu";
import { useTranslation } from "react-i18next";

interface ChatHeaderProps {
  chat: Chat;
}

const ChatParticipantsAvatarGroup: React.FC<
  Pick<Chat, "participants">
> = ({ participants }: { participants: Chat["participants"] }) => {
  return (
    <AvatarGroup max={3} sx={{ mr: 2 }}>
      {participants.slice(0, 3).map((participant) => (
        <Avatar
          key={participant.id}
          alt={participant.displayName}
          src={participant.avatar}
          sx={{ width: 36, height: 36 }}
        />
      ))}
    </AvatarGroup>
  );
};

const ChatParticipantsCount: React.FC<Pick<Chat, "participants">> = ({
  participants,
}: {
  participants: Chat["participants"];
}) => {
  const { t } = useTranslation("chats");
  return (
    <Tooltip
      title={t("participant_count", {
        count: participants.length,
      })}
    >
      <Box sx={{ display: "flex", alignItems: "center", ml: 1 }}>
        <UsersIcon size={16} />
        <Typography variant="body2" sx={{ ml: 0.5 }}>
          {participants.length}
        </Typography>
      </Box>
    </Tooltip>
  );
};

const ChatDescription: React.FC<{ description: string }> = ({
  description,
}) => {
  return (
    <Typography
      variant="body2"
      color="text.secondary"
      noWrap
      sx={{
        maxWidth: { xs: "200px", sm: "400px", md: "500px" },
        overflow: "hidden",
        textOverflow: "ellipsis",
      }}
    >
      {description}
    </Typography>
  );
};

const ChatTags: React.FC<{ tags: string[] }> = ({ tags }) => {
  return (
    <Box sx={{ display: "flex", mx: 2, gap: 0.5 }}>
      {tags.slice(0, 2).map((tag) => (
        <Chip key={tag} label={tag} size="small" />
      ))}
      {tags.length > 2 && (
        <Chip label={`+${tags.length - 2}`} size="small" variant="outlined" />
      )}
    </Box>
  );
};

const BackToChatsButton: React.FC = () => {
  const { t } = useTranslation("chats");
  return (
    <IconButton
      component={Link}
      to="/chats"
      sx={{ mr: 1.5 }}
      title={t("back_to_chats")}
    >
      <ArrowLeftIcon size={20} />
    </IconButton>
  );
};

export const ChatHeader: React.FC<ChatHeaderProps> = ({ chat }) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setMenuAnchorEl(null);
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          px: 2,
          py: 1.5,
          borderBottom: "1px solid",
          borderColor: "divider",
          bgcolor: "background.paper",
        }}
      >
        <BackToChatsButton />
        <ChatParticipantsAvatarGroup participants={chat.participants} />

        <Box sx={{ flexGrow: 1, minWidth: 0 }}>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Typography variant="subtitle1" fontWeight="medium" noWrap>
              {chat.title}
            </Typography>
            <ChatParticipantsCount participants={chat.participants} />
          </Box>

          {chat.description && (
            <ChatDescription description={chat.description} />
          )}
        </Box>

        {chat.tags && chat.tags.length > 0 && <ChatTags tags={chat.tags} />}

        <IconButton onClick={handleOpenMenu}>
          <MoreIcon size={20} />
        </IconButton>

        <ChatMenu
          chat={chat}
          menuAnchorEl={menuAnchorEl}
          onClose={handleCloseMenu}
        />
      </Box>
    </>
  );
};

export const ChatSideHeader: React.FC<ChatHeaderProps> = ({ chat }) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setMenuAnchorEl(null);
  };
  return (
    <Stack
      sx={{
        alignItems: "center",
        px: 2,
        py: 1.5,
        borderBottom: "1px solid",
        borderColor: "divider",
        bgcolor: "background.paper",
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        sx={{ width: "100%" }}
      >
        <BackToChatsButton />

        <Typography variant="subtitle1" fontWeight="medium" noWrap>
          {chat.title}
        </Typography>

        <ChatParticipantsCount participants={chat.participants} />

        <IconButton onClick={handleOpenMenu}>
          <MoreIcon size={20} />
        </IconButton>

        <ChatMenu
          chat={chat}
          menuAnchorEl={menuAnchorEl}
          onClose={handleCloseMenu}
        />
      </Stack>

      {chat.description && <ChatDescription description={chat.description} />}

      {chat.tags && chat.tags.length > 0 && <ChatTags tags={chat.tags} />}
    </Stack>
  );
};
