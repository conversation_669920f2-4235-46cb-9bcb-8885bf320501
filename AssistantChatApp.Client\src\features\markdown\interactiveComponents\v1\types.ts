export type MessageId = string;
export type ComponentId = string;
export type ComponentName = string;

export interface ComponentAction<
  TActionName extends string = string,
  TData = unknown
> {
  type: TActionName;
  payload?: TData;
  componentId: ComponentId;
  messageId: MessageId;
}

export interface ComponentContext<
  TComponentName extends string = ComponentName,
  TData = unknown
> {
  messageId: MessageId;
  componentId: ComponentId;
  onAction: (
    action: Omit<
      ComponentAction<TComponentName, TData>,
      "messageId" | "componentId"
    >
  ) => void;
  onUpdate: (data: TData) => void;
  data?: TData;
}

export type InteractiveComponentProps<
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _TComponentActionName extends string = string,
  TData = unknown,
  TProps = unknown
> = TProps & {
  children?: React.ReactNode;
  context: ComponentContext<ComponentName, TData>;
  // [key: string]: unknown; // Allow arbitrary props from directive attributes
};

export type ComponentActionHandlers<
  TComponentName extends string = ComponentName,
  TActionName extends string = string,
  TData = unknown
> = {
  [actionType in TActionName]: (
    action: ComponentAction<TComponentName, TData>,
    currentData: TData
  ) => unknown;
};

export interface ComponentRegistration<
  TComponentName extends string = ComponentName,
  TComponentActionName extends string = string,
  TData = unknown,
  TProps = unknown
> {
  component: React.ComponentType<
    InteractiveComponentProps<TComponentActionName, TData, TProps>
  >;
  initialData?: TData;
  actionHandlers?: {
    [actionType in TComponentActionName]: (
      action: ComponentAction<TComponentName, TData>,
      currentData: TData
    ) => unknown;
  };
}

export type ComponentRegistrationEntry<
  TComponentName extends string = ComponentName,
  TComponentActionName extends string = string,
  TData = unknown,
  TProps = unknown
> = [
  TComponentName,
  ComponentRegistration<TComponentName, TComponentActionName, TData, TProps>
];

export type ComponentRegistrationMap<
  TComponentName extends string = ComponentName,
  TComponentActionName extends string = string,
  TData = unknown,
  TProps = unknown
> = {
  [componentName in TComponentName]: ComponentRegistration<
    TComponentName,
    TComponentActionName,
    TData,
    TProps
  >;
};
