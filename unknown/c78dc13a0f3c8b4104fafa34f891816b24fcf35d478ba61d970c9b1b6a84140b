Look at the `NewChatButton` component (defined in `src/components/chat/NewChatButton.tsx`). It represents a button that opens a dialog with a form for creating a new chat. And it's too much, so your task will be to refactor it.

## Step 1

Extract the form state management logic into a dedicated hook. However, make the hook more generic, so that it could be used as a base for more specific hooks:
1. To create a new chat (`useCreateChatForm`). (This is the logic implemented in the `NewChatButton` component that need to be adapted to be based on the generic hook you are going to define.)
2. To edit/update an existing chat (`useUpdateChatForm`).

The hook should incapsulate only the necessary state management logic, e.g. actual state values and their setters. Put the hooks into `src/components/chat/ChatForms.tsx` file (it's already created).

## Step 2

Extract the logic for fetching users into a dedicated hook. The hook should return an array of Users and the corresponding `usersLoading` bool indicator.  Name the hook `useUsers` and add it to the `src/components/chat/ChatForms.tsx` file below the hooks defined earlier.

## Step 3

Extract the form into a dedicated component (i.e., separate it from the dialog and the button that opens the dialog). Also, make the form component more generalized so that it could be used to define two more specific forms:
1. Create a new chat (`CreateChatForm`).
2. Edit/update an existing chat (`UpdateChatForm`).

The form state management data should be passed to the component via props. For that, a props interface that allows such parameterization should be defined. Whereas the chat participants data can be fetched inside the component via the `useUsers` hook defined earlier. This way the component could be used in a more flexible manner (i.e., not necessarily in a dialog). Add the component to the `src/components/chat/ChatForms.tsx` file below all the hooks defined earlier.

## Step 4

I've adjusted the `ChatForm` component by adding `withSubmitButton` boolean prop to be able to conditionally render the submit button.
Now let's refactor `NewChatButton` component

## Step 5

Then rewrite current form state management (based on vanilla React hooks) to TanStack Form.
