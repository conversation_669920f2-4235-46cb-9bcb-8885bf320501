{
    "AppSettings": {
        "EnableConsoleIO": true,
        "DefaultAgentId": "91492ccd-ec07-442e-851a-c2c8c8bac3ee"
    },
    "ApiProviders": {
        "TogetherAI": {
            "Id": "TogetherAI",
            "ApiEndpoint": "https://api.together.xyz/v1/"
        },
        "OpenRouter": {
            "Id": "OpenRouter",
            "ApiEndpoint": "https://openrouter.ai/api/v1/"
        },
        "MockGPT": {
            "Id": "MockGPT",
            "ApiEndpoint": "https://mockgpt.wiremockapi.cloud/v1"
        },
        "MistralAI": {
            "Id": "MistralAI",
            "ApiFormat": "MistralAI",
            "ApiEndpoint": "https://api.mistral.ai/v1/"
        }
    },
    "Agents": {
        "_8715dc86-aae5-43fb-8bf4-4569949b1f20": {
            "Id": "8715dc86-aae5-43fb-8bf4-4569949b1f20",
            "Name": "Assistant #1",
            "ProviderId": "TogetherAI",
            "ModelId": "meta-llama/Llama-3.2-3B-Instruct-Turbo",
            "MainSystemPromptFilePath": "KB/Test/MainSystemPrompt.txt",
            "KernelMemoryPluginSettings": {
                "Enabled": false,
                "SearchSettings": {
                    "Limit": 10,
                    "MinRelevance": 0.5
                }
            },
            "KbSettings": {
                "RootDirPath": "KB/Test"
            }
        },
        "_91492ccd-ec07-442e-851a-c2c8c8bac3ee": {
            "Id": "91492ccd-ec07-442e-851a-c2c8c8bac3ee",
            "Name": "Assistant #2",
            "ProviderId": "OpenRouter",
            "ModelId": "deepseek/deepseek-chat-v3-0324:free",
            //"ModelId": "moonshotai/kimi-k2:free",
            "MainSystemPromptFilePath": "KB/Test/SysPrompt_Memory.txt",
            "KernelMemoryPluginSettings": {
                "Enabled": true,
                "SearchSettings": {
                    "Limit": 10,
                    "MinRelevance": 0.5
                }
            }
        },
        "8715dc86-aae5-43fb-8bf4-4569949b1f20": {
            "Id": "8715dc86-aae5-43fb-8bf4-4569949b1f20",
            "Name": "Windshield Seller #1",
            "ProviderId": "OpenRouter",
            "ModelId": "deepseek/deepseek-chat-v3-0324:free",
            "MainSystemPromptFilePath": "KB/WindshieldSeller/System_Prompt.md",
            "KbSettings": {
                "Enabled": true,
                "RootDirPath": "KB/WindshieldSeller"
            }
        },
        "91492ccd-ec07-442e-851a-c2c8c8bac3ee": {
            "Id": "91492ccd-ec07-442e-851a-c2c8c8bac3ee",
            "Name": "Windshield Seller #2",
            //"ProviderId": "OpenRouter",
            //"ModelId": "google/gemini-2.0-flash-exp:free",
            //"ModelId": "moonshotai/kimi-k2:free",
            //"ProviderId": "TogetherAI",
            //"ModelId": "Qwen/Qwen3-235B-A22B-fp8-tput",
            "ProviderId": "MistralAI",
            "ModelId": "mistral-small-latest",
            //"ModelId": "mistral-medium-2505",
            "MainSystemPromptFilePath": "KB/WindshieldSeller/System_Prompt.md",
            "KernelMemoryPluginSettings": {
                "Enabled": false,
                "SearchSettings": {
                    "Limit": 10,
                    "MinRelevance": 0.5
                }
            },
            "KbSettings": {
                "Enabled": true,
                "RootDirPath": "KB/WindshieldSeller"
            }
        },
        "da51c4c8-5e76-464f-8a25-bc28d1c00f2d": {
            "Id": "da51c4c8-5e76-464f-8a25-bc28d1c00f2d",
            "Name": "Fake AI",
            "ProviderId": "MockGPT",
            "ModelId": "deepseek/deepseek-chat-v3-0324:free",
            "MainSystemPromptFilePath": "KB/MainSystemPrompt2.txt",
            "KbSettings": {
                "Enabled": true,
                "RootDirPath": "KB"
            }
        }
    },
    "KernelMemoryAccessSettings": {
    },
    "ChatAppSettings": {
        "BaseUrl": "http://chat-app-backend"
    },
    "Otlp": {
        "EnableSemanticKernelTrace": true
    },
    "Serilog": {
        "MinimumLevel": "Debug",
        "HttpClientLogging": {
            "LogMode": "LogAll", // Default to less verbose
            "RequestHeaderLogMode": "LogAll",
            "RequestBodyLogMode": "LogAll",
            "ResponseHeaderLogMode": "LogAll",
            "ResponseBodyLogMode": "LogAll",
            "RequestBodyLogTextLengthLimit": 5000,
            "ResponseBodyLogTextLengthLimit": 5000,
            "MaskFormat": "*****",
            "MaskedProperties": [ "*password*", "*token*", "*api_key*", "*secret*" ]
        }
    }
}
