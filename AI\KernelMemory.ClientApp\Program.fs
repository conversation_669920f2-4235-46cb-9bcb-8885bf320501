open System
open System.Threading.Tasks
open Spectre.Console
open Spectre.Console.Cli
open SpectreCoff
open Microsoft.KernelMemory
open Microsoft.KernelMemory.HTTP
open Microsoft.Extensions.Configuration
open Spectre.Console.Rendering
open Enixar.KernelMemory.Client.Import

[<CLIMutable>]
type KernelMemoryClientSettings = {
    Endpoint: string
    ApiKey: string option
}

[<CLIMutable>]
type AppSettings = {
    KernelMemoryClientSettings: KernelMemoryClientSettings
    DocumentImportsJsonFilePath: string
}

exception ConfirmationDeniedException


let createMemoryClient (settings: KernelMemoryClientSettings) =
    MemoryWebClient(settings.Endpoint, ?apiKey = settings.ApiKey)


let printInfoMessage (message: string) =
    MarkupC(Color.DeepSkyBlue1, message) |> Output.toConsole

let printErrorMessage (message: string) =
    MarkupC(Color.Red, message) |> Output.toConsole

let printSuccessMessage (message: string) =
    MarkupC(Color.Green, message) |> Output.toConsole

let printWaitingMessageWithConfirmation (message: string) =
    MarkupC(Color.Yellow, message) |> Output.toConsole
    Console.ReadKey() |> ignore

let printMessageAndGetConfirmation color message =
    let prompt = TextPrompt<bool>(message)
    AnsiConsole.Prompt(
        prompt
            .AddChoice(true)
            .AddChoice(false)
            .DefaultValue(true)
            .WithConverter(fun choice -> if choice then "y" else "n")
    )


//=====
// Main
//=====

open Microsoft.Extensions.DependencyInjection
open Microsoft.Extensions.Hosting

[<EntryPoint>]
let main (argv: string []) =

    let appBuilder = Host.CreateDefaultBuilder(argv)

    let app = appBuilder.Build()

    app.Start()

    let config = app.Services.GetRequiredService<IConfiguration>()

    let appSettings: AppSettings = config.Get<AppSettings>()

    appSettings.DocumentImportsJsonFilePath
    |> Json.readImportJsonFile
    |> Result.mapError (fun error ->
        printErrorMessage $"Ошибка чтения JSON из %A{appSettings.DocumentImportsJsonFilePath}: %A{error}"
    )
    |> Result.iter (fun imports ->
        try
            printInfoMessage "Initializing Kernel Memory Web client..."

            let memory = createMemoryClient appSettings.KernelMemoryClientSettings

            printInfoMessage $"Document imports file path: %A{appSettings.DocumentImportsJsonFilePath}"

            if
                printMessageAndGetConfirmation Color.Yellow
                    $"{imports.Length} documents to import are found. Proceed with import?"
                |> not
            then
                raise ConfirmationDeniedException

            printInfoMessage $"Importing documents described in %A{appSettings.DocumentImportsJsonFilePath}."
            printInfoMessage $"Waiting for import to complete..."

            let importedDocIds =
                Import.importAllAsync memory imports
                |> Async.AwaitTask
                |> Async.RunSynchronously

            printSuccessMessage $"{importedDocIds.Length} documents have been imported successfully."

            importedDocIds
            |> Array.mapi (fun i docId ->
                new Text($"{i + 1}. {docId}", Style(Nullable Color.Green)) :> IRenderable)
            |> Rows
            |> AnsiConsole.Write
        with
        | ConfirmationDeniedException ->
            printInfoMessage "Operation cancelled by the user."
    )

    //printWaitingMessageWithConfirmation $"Enter any key to exit..."

    app.WaitForShutdown()

    0
