import { pgTable, uuid, varchar, primaryKey } from 'drizzle-orm/pg-core';
import { chats } from './chats';

// Tags table
export const tags = pgTable('tags', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 50 }).notNull().unique(),
});

// Chat tags (many-to-many relationship)
export const chatTags = pgTable('chat_tags', {
  chatId: uuid('chat_id').notNull().references(() => chats.id, { onDelete: 'cascade' }),
  tagId: uuid('tag_id').notNull().references(() => tags.id, { onDelete: 'cascade' }),
}, (table) => {
  return [
    primaryKey({ columns: [table.chatId, table.tagId] }),
  ];
});
