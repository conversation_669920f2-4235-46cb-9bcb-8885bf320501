import React from "react";
import {
  <PERSON>,
  I<PERSON><PERSON><PERSON>on,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  TextField,
  Chip,
  Typography,
} from "@mui/material";
import {
  Close as CloseIcon,
  Check as CheckIcon,
} from "@mui/icons-material";
import {
  useMdastNodeUpdater,
  NestedLexicalEditor,
  PropertyPopover,
  type DirectiveEditorProps,
} from "@mdxeditor/editor";
import { Directives } from "mdast-util-directive";
import { PhrasingContent } from "mdast";
import { css } from "@emotion/react";
import { useTryGetUser } from "../../../queries/user";
import { UserMention } from "../UserMention1";

type Node = DirectiveEditorProps["mdastNode"];
// type MdastNodeUpdater = ReturnType<typeof useMdastNodeUpdater>;

const styles = {
  inlineEditor: css`
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  `,
  blockEditor: css`
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  `,
  genericComponentName: css`
    font-family: monospace;
    font-size: 0.75rem;
    opacity: 0.5;
  `,
};

function getUserIdFromNode(node: Node): string | undefined {
  const child = node.children?.[0];
  if (child && child.type === "text") {
    return child.value;
  }
  return undefined;
}

export const MentionDirectiveEditor: React.FC<DirectiveEditorProps> = ({
  mdastNode,
}) => {
  const userId = getUserIdFromNode(mdastNode);
  return (
    <UserMention userId={userId} />
  );
};

export const MentionDirectiveInteractiveEditor: React.FC<
  DirectiveEditorProps
> = ({ mdastNode }) => {
  const updateMdastNode = useMdastNodeUpdater();
  const [editOpen, setEditOpen] = React.useState(false);

  // Extract current value
  const userId = getUserIdFromNode(mdastNode);
  const { data: user, isLoading } = useTryGetUser(userId);
  const displayName = user?.displayName ?? userId;
  const label = isLoading ? (
    <CircularProgress size={18} />
  ) : (
    <Typography component="span">
      @
      {displayName ?? (
        <Typography component="span" sx={{ opacity: 0.5 }}>
          Unknown
        </Typography>
      )}
    </Typography>
  );

  // For editing
  const [inputValue, setInputValue] = React.useState(userId ?? "");
  const [submitting, setSubmitting] = React.useState(false);

  // Ensure inputValue syncs with node changes
  React.useEffect(() => {
    setInputValue(userId ?? "");
  }, [userId]);

  // Submit handler: update the `mdastNode` children by calling `updateMdastNode`
  const handleSubmit = async () => {
    setSubmitting(true);
    const newContent: PhrasingContent = {
      type: "text",
      value: inputValue,
    };
    updateMdastNode({
      children: [newContent],
    });
    setEditOpen(false);
    setSubmitting(false);
  };

  // Cancel handler
  const handleCancel = () => {
    setInputValue(userId ?? "");
    setEditOpen(false);
  };

  const handleMentionClick = () => {
    setEditOpen(true);
  };

  return (
    <>
      <Chip
        clickable
        label={label}
        size="small"
        variant="outlined"
        color="info"
        onClick={handleMentionClick}
      />
      <Dialog open={editOpen} onClose={handleCancel} maxWidth="xs" fullWidth>
        <DialogTitle>Edit Mention</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="User ID"
            type="text"
            fullWidth
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            disabled={submitting}
            data-testid="mention-userid-input"
          />
        </DialogContent>
        <DialogActions>
          <IconButton
            onClick={handleCancel}
            disabled={submitting}
            aria-label="Cancel"
          >
            <CloseIcon />
          </IconButton>
          <IconButton
            onClick={handleSubmit}
            color="primary"
            disabled={submitting || !inputValue.trim()}
            aria-label="Submit"
          >
            {submitting ? <CircularProgress size={20} /> : <CheckIcon />}
          </IconButton>
        </DialogActions>
      </Dialog>
    </>
  );
};

export const GenericDirectiveEditor: React.FC<
  DirectiveEditorProps
> = ({ mdastNode, descriptor }) => {
  const updateMdastNode = useMdastNodeUpdater();

  const properties = React.useMemo(() => {
    return descriptor.attributes.reduce<Record<string, string>>(
      (acc, attributeName) => {
        acc[attributeName] = mdastNode.attributes?.[attributeName] ?? "";
        return acc;
      },
      {},
    );
  }, [mdastNode, descriptor]);

  const onChange = React.useCallback(
    (values: Record<string, string>) => {
      updateMdastNode({
        attributes: Object.fromEntries(
          Object.entries(values).filter(([, value]) => value !== ""),
        ),
      });
    },
    [updateMdastNode],
  );

  return (
    <Box
      sx={
        mdastNode.type === "textDirective"
          ? styles.inlineEditor
          : styles.blockEditor
      }
    >
      {descriptor.attributes.length == 0 &&
      descriptor.hasChildren &&
      mdastNode.type !== "textDirective" ? (
        <Box component="span" sx={styles.genericComponentName}>
          {mdastNode.name}
        </Box>
      ) : null}

      {descriptor.attributes.length > 0 ? (
        <PropertyPopover
          properties={properties}
          title={mdastNode.name || ""}
          onChange={onChange}
        />
      ) : null}
      {descriptor.hasChildren ? (
        <NestedLexicalEditor<Directives>
          block={mdastNode.type === "containerDirective"}
          getContent={(node) => node.children as PhrasingContent[]}
          getUpdatedMdastNode={(mdastNode, children) => {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            return { ...mdastNode, children };
          }}
        />
      ) : (
        <Box component="span" sx={styles.genericComponentName}>
          {mdastNode.name}
        </Box>
      )}
    </Box>
  );
};
