The task is to implement a React component. Below are the requirements for the task.

## Tech stack requirements
- Language: TypeScript
- UI Library: MUI
- Form library: TanStack Form
- Internalization library: i18next

## Description

A form consisting of `Autocomplete` and a submit button. The elements of the Autocomplete dropdown list should consist of Avatar, and a text label/title. The interface of the element data:
```typescript
interface UserItem {
  avatarSrc? : string;
  userName: string;
}
```

The autocomplete should be configured to allow single selection. It also should allow filtering the list based on the input property. The filtering should be based on the `userName` property.

The Avatars should be circular. And in case of absence of `avatarSrc` property, the Avatar should be replaced with the first letter of the `userName` property.

All the labels, captions, titles, etc. shouldn't be hardcoded. Instead, they should be extracted via `t` function provided by `i18next`'s `useTranslation` hook.

By default, the user field (represented by the Autocomplete) should be initialized by the first item from the provided input list of `UserItem`s.

## Component API

```typescript
interface Props {
  onSubmit: (selectedUsers: UserItem) => void;
  users: UserItem[];
}
```
