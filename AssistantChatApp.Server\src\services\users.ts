import { db } from '../db';
import { users } from '../db/schema';
import { SafeUser, PaginatedResponse } from '../types';
import { NotFoundError } from '../utils/errors';
import { createPaginatedResponse, calculateOffset } from '../utils/pagination';
import { eq, like, and, sql } from 'drizzle-orm';

/**
 * Get users with pagination and search
 * @param page Page number
 * @param limit Items per page
 * @param search Search term
 * @returns Paginated list of users
 */
export async function getUsers(
  page: number,
  limit: number,
  search?: string
): Promise<PaginatedResponse<SafeUser>> {
  const offset = calculateOffset(page, limit);
  let query = db.select().from(users);
  
  // Apply search filter if provided
  if (search) {
    query = query.where(
      like(users.displayName, `%${search}%`)
    );
  }
  
  // Get total count
  const countResult = await db
    .select({ count: sql<number>`count(*)` })
    .from(users)
    .execute();
  
  const total = countResult[0]?.count || 0;
  
  // Get paginated results
  const results = await query
    .limit(limit)
    .offset(offset)
    .execute();
  
  // Map to safe user objects
  const safeUsers: SafeUser[] = results.map((user) => ({
    id: user.id,
    email: user.email,
    displayName: user.displayName,
    avatar: user.avatar,
    createdAt: user.createdAt,
    isAI: user.isAI,
  }));
  
  return createPaginatedResponse(safeUsers, total, page, limit);
}

/**
 * Get a user by ID
 * @param userId User ID
 * @returns User data
 */
export async function getUserById(userId: string): Promise<SafeUser> {
  const userResult = await db
    .select()
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);
  
  if (!userResult.length) {
    throw new NotFoundError('User not found');
  }
  
  const user = userResult[0];
  
  return {
    id: user.id,
    email: user.email,
    displayName: user.displayName,
    avatar: user.avatar,
    createdAt: user.createdAt,
    isAI: user.isAI,
  };
}

/**
 * Get AI assistants
 * @returns List of AI users
 */
export async function getAIAssistants(): Promise<SafeUser[]> {
  const aiUsers = await db
    .select()
    .from(users)
    .where(eq(users.isAI, true))
    .execute();
  
  return aiUsers.map((user) => ({
    id: user.id,
    email: user.email,
    displayName: user.displayName,
    avatar: user.avatar,
    createdAt: user.createdAt,
    isAI: user.isAI,
  }));
}
