﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AssistantChatApp.Server.AspNetCore.Migrations
{
    /// <inheritdoc />
    public partial class AddMessageFunctionCallAndResultContents : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "FunctionCallContents",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    MessageId = table.Column<string>(type: "text", nullable: false),
                    CallId = table.Column<string>(type: "text", nullable: false),
                    FunctionName = table.Column<string>(type: "text", nullable: false),
                    PluginName = table.Column<string>(type: "text", nullable: true),
                    Arguments = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FunctionCallContents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FunctionCallContents_Messages_MessageId",
                        column: x => x.MessageId,
                        principalTable: "Messages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FunctionResultContents",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    MessageId = table.Column<string>(type: "text", nullable: false),
                    CallId = table.Column<string>(type: "text", nullable: true),
                    Result = table.Column<string>(type: "text", nullable: false),
                    FunctionCallId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FunctionResultContents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FunctionResultContents_FunctionCallContents_FunctionCallId",
                        column: x => x.FunctionCallId,
                        principalTable: "FunctionCallContents",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_FunctionResultContents_Messages_MessageId",
                        column: x => x.MessageId,
                        principalTable: "Messages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FunctionCallContents_MessageId",
                table: "FunctionCallContents",
                column: "MessageId");

            migrationBuilder.CreateIndex(
                name: "IX_FunctionResultContents_FunctionCallId",
                table: "FunctionResultContents",
                column: "FunctionCallId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FunctionResultContents_MessageId",
                table: "FunctionResultContents",
                column: "MessageId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FunctionResultContents");

            migrationBuilder.DropTable(
                name: "FunctionCallContents");
        }
    }
}
