import React from "react";
import { User, AIChatCompletionRequest } from "../../types";
import { TypingIndicator } from "./MessageWritingProgressIndicator";
import { Box } from "@mui/material";


export interface AIRespondingIndicatorProps {
  chatParticipants: User[] | undefined;
  isPending: boolean;
  requestData: AIChatCompletionRequest | undefined;
};

export const AIRespondingIndicator: React.FC<AIRespondingIndicatorProps> = ({
  chatParticipants,
  isPending,
  requestData,
}) => {
  const respondingAIUserId = requestData?.aiUserId;
  if (isPending && respondingAIUserId) {
    const aiUserName =
      chatParticipants?.find((p) => p.id === respondingAIUserId)?.displayName ??
      respondingAIUserId;
    return (
      <Box sx={{ p: 2, borderTop: "1px solid", borderColor: "divider" }}>
        <TypingIndicator userName={aiUserName} />
      </Box>
    );
  }
  return null;
};
