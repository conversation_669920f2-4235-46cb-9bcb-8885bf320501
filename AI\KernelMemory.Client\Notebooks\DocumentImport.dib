#!meta

{"kernelInfo":{"defaultKernelName":"csharp","items":[{"name":"csharp","languageName":"C#","aliases":["c#","cs"]},{"name":"fsharp","languageName":"fsharp"},{"name":"html","languageName":"HTML"},{"name":"http","languageName":"HTTP"},{"name":"javascript","languageName":"JavaScript","aliases":["js"]},{"name":"mermaid","languageName":"Mermaid"},{"name":"pwsh","languageName":"PowerShell","aliases":["powershell"]},{"name":"value"}]}}

#!fsharp

#i "nuget: S:/Enixar/Web/AssistantChatApp/AI/KernelMemory.Client/Notebooks/packages"
#r "nuget: Enixar.KernelMemory.Client"

#!markdown

## Импорт документов

#!fsharp

open Enixar.KernelMemory.Client.Import

/// Коллекция пар `(ИД_документа, Путь_к_файлу)`
let documentMap (pairs: seq<string * string>) = Map pairs

#!fsharp

let imports: ImportEntry list = [
    DocumentImportEntry {
        Id = "labelling_ukaz_243"
        FilePath = "D:\\Enixar\\ProductMarking\\Указ №243 2021-06-10 О маркировке товаров.pdf"
        Index = Some "default"
        Tags = Some [
            "year:2021"
            "topic:product-labelling"
            "topic:product-marking"
            "country:Belarus"
        ]
    }
]
