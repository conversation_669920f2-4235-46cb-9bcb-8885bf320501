module AssistantChatApp.AIAgents.Agents

open System
open System.Threading.Tasks
open System.Threading.Channels
open System.Net.Http
open FSharp.Control
open Microsoft.SemanticKernel.Connectors.OpenAI
open Microsoft.SemanticKernel.Connectors.MistralAI
open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.Agents

open Microsoft.Extensions.Hosting
open Microsoft.Extensions.Logging
open Microsoft.Extensions.Configuration
open Microsoft.Extensions.DependencyInjection

open Microsoft.SemanticKernel.ChatCompletion
open System.Threading
open AssistantChatApp.AIAgents.Core
open AssistantChatApp.AIAgents.Shared
open AssistantChatApp.AIAgents.Shared.Api
open SemanticKernelHelpers

type AgentResponse = {
    Thread: ChatHistoryAgentThread
    AllMessages: ChatMessageContent [] //TaskSeq<ChatMessageContent>
    FinalMessages: ChatMessageContent [] //TaskSeq<ChatMessageContent>
}

type AgentSetupError =
    | UnknownAIApiFormat of formatName: string

exception AgentSetupException of error: AgentSetupError

let inline effects (effects: seq<obj>) = ignore effects


let createAgentCompletionService
    (loggerFactory: ILoggerFactory)
    (httpClientFactory: IHttpClientFactory)
    ({ ApiProvider = provider; Agent = agent } as settings: AgentFullSettings)
    : IChatCompletionService
    =
    let httpClient = Http.getApiProviderHttpClient agent.ProviderId httpClientFactory
    match provider.ApiFormat with
    | AIApiFormat.OpenAI ->
        OpenAIChatCompletionService(
            modelId = agent.ModelId,
            endpoint = Uri provider.ApiEndpoint,
            apiKey = provider.ApiKey,
            httpClient = httpClient,
            loggerFactory = loggerFactory
        )
    | AIApiFormat.MistralAI ->
        MistralAIChatCompletionService(
            modelId = agent.ModelId,
            apiKey = provider.ApiKey,
            endpoint = Uri provider.ApiEndpoint,
            httpClient = httpClient,
            loggerFactory = loggerFactory
        )
    | AIApiFormat.UnsupportedApi formatName ->
        AgentSetupError.UnknownAIApiFormat formatName
        |> AgentSetupException
        |> raise


let tryCreateAgentFileAccessPlugin (env: IHostEnvironment) (settings: AgentFullSettings) =
    settings.Agent.KbSettings
    |> Option.ofObj
    |> Option.bind (fun kbSettings ->
        if kbSettings.Enabled then
            //TODO: Ensure that the KB plugin is disposed when the agent is disposed
            KnowledgeBase.FileAccessPlugin(kbSettings, env)
            |> Some
        else None
    )

let tryReadAgentMainSystemPrompt (env: IHostEnvironment) (settings: AgentFullSettings) =
    settings.Agent.MainSystemPromptFilePath
    |> Option.ofObj
    |> Option.map (
        Utils.getFullDirPath env.ContentRootPath
        >> System.IO.File.ReadAllText
    )

(*
// This function is for the case when a Kernel Memory per agent is enabled.
// But for now, it's challenging since KM built-in Web API
// doesn't support multiple `IKernelMemory` instances in the DI container.
let tryCreateAgentKernelMemoryPlugin
    (config: IConfiguration)
    (settings: AgentFullSettings)
    =
    settings.Agent.KernelMemoryInstanceId
    |> Option.bind (fun kmId ->
        let kmInstances = Configuration.getKernelMemoryInstanceConfigs config
        kmInstances
        |> Map.tryFind kmId
        |> Option.map (fun config ->
            let memory = KernelMemory.createMemory config
            KernelMemory.ReadonlyMemoryPlugin(memory)
        )
    )
*)

let tryCreateAgentKernelMemoryPlugin
    memory
    (settings: AgentFullSettings)
    =
    settings.Agent.KernelMemoryPluginSettings
    |> Option.ofObj
    |> Option.bind (fun kmSettings ->
        if kmSettings.Enabled then
            KernelMemory.ReadonlyMemoryPlugin(memory, kmSettings)
            |> Some
        else None
    )


/// Create a new `ChatCompletionAgent` with specified name and Kernel, and Auto Function Choice Behavior set by default.
let createChatCompletionAgent
    (kernel: Kernel)
    (loggerFactory: ILoggerFactory)
    (id: string)
    (name: string)
    (instructions: string)
    =
    let execSettings = PromptExecutionSettings(
        FunctionChoiceBehavior = FunctionChoiceBehavior.Auto()
    )
    let kargs = KernelArguments(execSettings)
    ChatCompletionAgent(
        Id = id,
        Name = name,
        Kernel = kernel,
        Instructions = instructions,
        Arguments = kargs,
        LoggerFactory = loggerFactory
    )


let createKernelForAgent
    (loggerFactory: ILoggerFactory)
    (chatCompletionService: IChatCompletionService)
    (fileAccessPlugin: KnowledgeBase.FileAccessPlugin option)
    (memoryPlugin: KernelMemory.ReadonlyMemoryPlugin option)
    (configureKernel: IKernelBuilder -> unit)
    =
    let builder = Kernel.CreateBuilder()

    effects [
        builder.Services
            .AddSingleton(chatCompletionService)
            .AddSingleton(loggerFactory)
        fileAccessPlugin
        |> Option.map (fun kbPlugin -> builder.Plugins.AddFromObject(kbPlugin))

        memoryPlugin
        |> Option.map (fun memoryPlugin -> memoryPlugin.AddToKernel builder)

        builder |> configureKernel
    ]

    let kernel = builder.Build()
    kernel


let createAgentWithPlugins
    (loggerFactory: ILoggerFactory)
    (httpClientFactory: IHttpClientFactory)
    (env: IHostEnvironment)
    (memory: Microsoft.KernelMemory.IKernelMemory)
    (settings: AgentFullSettings)
    =
    let completionService =
        createAgentCompletionService loggerFactory httpClientFactory settings
    let fileAccessPluginOpt = tryCreateAgentFileAccessPlugin env settings
    let memoryPluginOpt = tryCreateAgentKernelMemoryPlugin memory settings
    let kernel =
        createKernelForAgent
            loggerFactory
            completionService
            fileAccessPluginOpt
            memoryPluginOpt
            ignore
    let instructions =
        tryReadAgentMainSystemPrompt env settings
        |> Option.defaultValue ""

    createChatCompletionAgent
        kernel loggerFactory settings.AgentId settings.Agent.Name instructions


let createProviderSpecificPromptExecutionSettings
    (chatCompletionSvc: IChatCompletionService)
    : PromptExecutionSettings
    =
    match chatCompletionSvc with
    | :? OpenAIChatCompletionService as svc ->
        let execSettings = OpenAIPromptExecutionSettings()
        execSettings.FunctionChoiceBehavior <- FunctionChoiceBehavior.Auto(autoInvoke = true)
        execSettings
    | :? MistralAIChatCompletionService ->
        let execSettings = MistralAIPromptExecutionSettings()
        execSettings.ToolCallBehavior <- MistralAIToolCallBehavior.AutoInvokeKernelFunctions
        execSettings
    | _ -> failwith "Unsupported chat completion service"

let askAgent
    (inputMessages: seq<ChatMessage>)
    (inquirer: ChatUser option)
    (cancellationToken: CancellationToken)
    (agent: Agent)
    : Task<AgentResponse>
    =
    task {
        let chatHistory = ChatHistory([
            for m in inputMessages ->
                ChatMessage.toChatMessageContent m
        ])

        let thread = ChatHistoryAgentThread(chatHistory)
        let chatCompletionSvc = agent.Kernel.GetRequiredService<IChatCompletionService>()
        let execSettings = createProviderSpecificPromptExecutionSettings chatCompletionSvc

        let args =
            inquirer
            |> Option.map (fun inq -> dict ["inquirerName", box inq.UserName])

        let intermediateMessages =
            System.Collections.Concurrent.ConcurrentQueue<ChatMessageContent>()

        let addIntermediateMessage (msg: ChatMessageContent) =
            intermediateMessages.Enqueue(msg)

        let onIntermediateMessage = Func<ChatMessageContent, Task>(fun msg ->
            task {
                addIntermediateMessage msg
            }
        )

        let options =
            AgentInvokeOptions.Create(
                settings = execSettings,
                ?args = args,
                onIntermediateMessage = onIntermediateMessage
            )

        let! finalMessages =
            agent.InvokeAsync(thread, options, cancellationToken)
            |> TaskSeq.map (fun rsp -> rsp.Message)
            |> TaskSeq.toArrayAsync

        let intermediateMessagesArray = intermediateMessages.ToArray()

        return {
            Thread = thread
            AllMessages = intermediateMessagesArray
            FinalMessages = finalMessages
        }
    }


type ChatAgentService(
    loggerFactory: ILoggerFactory,
    httpClientFactory: IHttpClientFactory,
    env: IHostEnvironment,
    memory,
    agentSettingsMap: AgentFullSettingsMap
) =
    static member AskAgentAsync(agent, inputMessages, ?inquirer: ChatUser, ?cancellationToken) =
        task {
            let ct = defaultArg cancellationToken CancellationToken.None
            let! response = agent |> askAgent inputMessages inquirer ct
            let rspAllOutputMsgs =
                response.AllMessages
                |> Array.map Core.ChatMessage.ofChatMessageContent
            return rspAllOutputMsgs
        }

    member this.TryCreateAgent(agentId: AgentId) =
        agentSettingsMap.TryFind agentId
        |> Option.map (
            createAgentWithPlugins loggerFactory httpClientFactory env memory
        )

    /// Try to create an agent with the specified ID, and if successful, invokes it and returns the response.
    member this.TryCreateAndAskAgentAsync(agentId, inputMessages, ?inquirer: ChatUser, ?cancellationToken) =
        task {
            match this.TryCreateAgent agentId with
            | Some agent ->
                let! rspMessages =
                    ChatAgentService.AskAgentAsync(
                        agent, inputMessages,
                        ?inquirer = inquirer,
                        ?cancellationToken = cancellationToken
                    )
                return Some rspMessages
            | None ->
                return None
        }
