module AssistantChatApp.ChatHistorySync.DbAccess

open System
open FSharp.Data.Sql
open FSharp.Data.Sql.PostgreSql
open FSharp.Data.LiteralProviders

module Json =

    open System.Text.Json
    open System.Text.Json.Serialization

    let options =
        let o = JsonSerializerOptions()
        o.PropertyNamingPolicy <- JsonNamingPolicy.SnakeCaseLower
        o

    let serialize (value: 't) = JsonSerializer.Serialize(value, options)


module private DbConfig =

    let [<Literal>] DbConnectionStringSample = TextFile<"dbConnectionStringSample.txt">.Text
    let [<Literal>] DbVendor = Common.DatabaseProviderTypes.POSTGRESQL


type ChatHistoryDB = SqlDataProvider<DbConfig.DbVendor, DbConfig.DbConnectionStringSample>

let createChatHistoryMessage
    (role: string)
    (content: string)
    =
    {|
        ``type`` = role
        content = content
    |}
    |> Json.serialize

let insertChatHistoryEntry
    (sessionId: string)
    (role: string)
    (content: string)
    (ctx: ChatHistoryDB.dataContext)
    =
    let message = createChatHistoryMessage role content
    ctx.Public.N8nChatHistories.``Create(message, session_id)``(message, sessionId)
