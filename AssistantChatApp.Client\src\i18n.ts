import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import enCommon from "./locales/en/common.json";
import enAuth from "./locales/en/auth.json";
import enChats from './locales/en/chats.json';
import enMessages from './locales/en/messages.json';
import enUsers from './locales/en/users.json';
import ruCommon from "./locales/ru/common.json";
import ruAuth from "./locales/ru/auth.json";
import ruChats from './locales/ru/chats.json';
import ruMessages from './locales/ru/messages.json';
import ruUsers from './locales/ru/users.json';
import enQuestionSelector from './locales/en/question_selector.json';
import ruQuestionSelector from './locales/ru/question_selector.json';

const isDev = import.meta.env.DEV;

export const resources = {
  en: {
    common: enCommon,
    chats: enChats,
    auth: enAuth,
    messages: enMessages,
    users: enUsers,
    question_selector: enQuestionSelector,
  },
  ru: {
    common: ruCommon,
    chats: ruChats,
    auth: ruAuth,
    messages: ruMessages,
    users: ruUsers,
    question_selector: ruQuestionSelector,
  }
} as const;

i18n
  .use(LanguageDetector)           // auto-detect browser language
  .use(initReactI18next)           // bind react-i18next
  .init({
    resources,
    fallbackLng: 'en',
    supportedLngs: ['en', 'ru'],
    ns: ["common", "chats", "messages", "users", "auth"],
    defaultNS: "common",
    fallbackNS: "common",
    debug: isDev,
    interpolation: {
      escapeValue: false          // React already does escaping
    },
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json'
    },
  });

export default i18n;
