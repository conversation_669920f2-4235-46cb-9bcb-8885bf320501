import React from "react";
import { Map as ImmutableMap } from "immutable";
type ComponentName = string;
type ComponentInstanceId = string;
type ActionHandlerId = string;

interface ComponentAction<
  TComponentName extends string = ComponentName,
  TActionName extends string = string,
  TData = unknown
> {
  type: TActionName;
  componentName: TComponentName;
  componentId: ComponentInstanceId;
  payload?: TData;
}

type ComponentInstanceActions<
  TActionName extends string = string,
  TActionData = unknown
> = {
  [actionName in TActionName]: (data: TActionData) => void;
};

type TransformComponentActionData<
  TComponentName extends string = ComponentName,
  TActionName extends string = string,
  TActionData = unknown,
  TOutputData = unknown
> = (
  action: ComponentAction<TComponentName, TActionName, TActionData>
) => TOutputData;

type ActionDataTransformerMap<
  TComponentName extends string = ComponentName,
  TActionName extends string = string,
  TActionData = unknown,
  TOutputData = unknown
> = ImmutableMap<
  TActionName,
  TransformComponentActionData<
    TComponentName,
    TActionName,
    TActionData,
    TOutputData
  >
>;

type InteractiveComponentProps<
  // TComponentName extends string = ComponentName,
  TActionName extends string = string,
  TProps extends object = object,
  TData = unknown
> = TProps & {
  actions: ComponentInstanceActions<TActionName, TData>;
  initialData?: TData;
  children?: React.ReactNode;
};

interface ComponentRegistration<
  TComponentName extends string = ComponentName,
  TActionName extends string = string,
  TProps extends object = object,
  TData = unknown,
  TActionOutputData = unknown
> {
  component: React.ComponentType<
    InteractiveComponentProps<TActionName, TProps, TData>
  >;
  initialData?: TData;
  actionDataMappers?: {
    [actionName in TActionName]: TransformComponentActionData<
      TComponentName,
      TActionName,
      TData,
      TActionOutputData
    >;
  }
}

export type {
  ComponentName,
  ComponentInstanceId,
  ActionHandlerId,
  TransformComponentActionData,
  ComponentAction,
  ComponentInstanceActions,
  ActionDataTransformerMap,
  InteractiveComponentProps,
  ComponentRegistration,
};
