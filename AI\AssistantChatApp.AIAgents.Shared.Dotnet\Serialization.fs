namespace AssistantChatApp.AIAgents.Shared.Dotnet.Serialization

module Json =

    open System.Text.Json
    open System.Text.Json.Serialization

    let serializerOptions =
        let o = JsonSerializerOptions()
        JsonFSharpOptions()
            .WithUnionInternalTag()
            .WithUnwrapOption()
            .WithUnionUnwrapSingleCaseUnions()
            .WithUnionUnwrapRecordCases()
            .WithUnionUnwrapFieldlessTags()
            .WithUnionAllowUnorderedTag()
            .AddToJsonSerializerOptions(o)
        o.PropertyNamingPolicy <- JsonNamingPolicy.CamelCase
        o

    let serialize (x: 't) = JsonSerializer.Serialize(x, serializerOptions)
    let serializeBoxed<'t> (x: obj) = JsonSerializer.Serialize(x, typeof<'t>, serializerOptions)
    let serializeBoxedWithType (x: obj) (t: System.Type) = JsonSerializer.Serialize(x, t, serializerOptions)
    let serializeToElement (x: 't) = JsonSerializer.SerializeToElement(x, serializerOptions)
    let deserialize<'t> (json: string): 't = JsonSerializer.Deserialize<'t>(json, serializerOptions)
