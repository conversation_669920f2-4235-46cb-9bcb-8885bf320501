using System;
using System.Text;
using AssistantChatApp.Server.AspNetCore.Data;
using AssistantChatApp.Server.AspNetCore.Helpers;
using AssistantChatApp.DataStore.Infrastructure;
using AssistantChatApp.Server.AspNetCore.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Microsoft.Extensions.Options;
using AssistantChatApp.AIAgents.Client;
using AssistantChatApp.Server.AspNetCore;
using AssistantChatApp.Server.Services;
using Serilog;
using Serilog.AspNetCore;

using Entities = AssistantChatApp.DataStore.Models;

Log.Logger =
    new LoggerConfiguration()
        .MinimumLevel.Debug()
        .WriteTo.Console()
        .WriteTo.Debug()
        .WriteTo.File("logs/bs-log-.log")
        .CreateBootstrapLogger();


var builder = WebApplication.CreateBuilder(args);

builder.AddServiceDefaults();

builder.Services.AddSerilog(logConfig =>
{
    logConfig.ReadFrom.Configuration(builder.Configuration);
});

// Add services to the container.
builder.Services.AddControllers();

// Configure PostgreSQL with Entity Framework Core
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Configure Identity
builder.Services.AddIdentity<Entities.User, IdentityRole>(options =>
{
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequiredLength = 8;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Configure JWT Authentication
var jwtSettings = builder.Configuration.GetSection(nameof (JwtSettings)).Get<JwtSettings>();
var key = Encoding.ASCII.GetBytes(jwtSettings?.Secret ?? throw new InvalidOperationException("JWT Secret not configured"));

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidIssuer = jwtSettings.Issuer,
        ValidAudience = jwtSettings.Audience,
        ClockSkew = TimeSpan.Zero
    };
});

// Register configuration
builder.Services.Configure<AiAgentSettings>(
    builder.Configuration.GetSection(nameof(AiAgentSettings))
);

// Register services
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<UserService>();
builder.Services.AddScoped<ChatService>();
builder.Services.AddScoped<MessageService>();
builder.Services.AddScoped<MessageTagService>();
builder.Services.AddScoped<AIAssistantService>();
builder.Services.AddScoped<QuestionService>();

// Configure AI Agent HTTP Client
builder.AddAgentHttpClient();
builder.AddAgentClientProxy(sp =>
    sp.GetRequiredService<IOptions<AiAgentSettings>>().Value.BaseUrl
);

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigin",
        corsBuilder => corsBuilder
            .WithOrigins(builder.Configuration[nameof(RootSettings.AllowedOrigins)]?.Split(',') ?? new[] { "http://localhost:3000" })
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials()
    );
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Assistant Chat App API", Version = "v1" });

    // Configure Swagger to use JWT Authentication
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

try
{
    var app = builder.Build();

    app.UseSerilogRequestLogging();

    // Configure the HTTP request pipeline.
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI();
    }

    // Use custom error handling middleware
    app.UseMiddleware<ErrorHandlingMiddleware>();

    // Use CORS
    app.UseCors("AllowSpecificOrigin");

    // Serve static files (from default `wwwroot` directory)
    app.UseDefaultFiles();
    app.UseStaticFiles();
    //app.MapStaticAssets();

    app.UseAuthentication();
    app.UseAuthorization();

    app.MapControllers();

    // This is crucial for SPAs:
    // It maps all unhandled requests to index.html, allowing client-side routing to take over.
    // This should be placed after all other routing (like MapControllers)
    // so that API requests are handled first.
    app.MapFallbackToFile("index.html");

    // Seed the database with Test (Dev) data
    if (app.Environment.IsDevelopment())
    {
        await TestDbSeeder.SeedAsync(app.Services);
    }
    else
    {
        await RealDbSeeder.SeedAsync(app.Services);
    }

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Unhandled exception");
}
finally
{
    Log.CloseAndFlush();
}

