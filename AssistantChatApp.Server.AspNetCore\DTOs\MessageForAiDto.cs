namespace AssistantChatApp.Server.AspNetCore.DTOs;

public record AgentMessageContents(
    string TextContent,
    IEnumerable<AIAgents.Shared.MessageContent> OtherContents
    // IEnumerable<AIAgents.Shared.FunctionCall> FunctionCalls,
    // IEnumerable<AIAgents.Shared.FunctionCallResult> FunctionResults
);

public record MessageContents(
    string TextContent,
    // IEnumerable<ContentDto> OtherContents
    IEnumerable<FunctionCallContentDto> FunctionCalls,
    IEnumerable<FunctionResultContentDto> FunctionResults
);

public record MessageForAiDto(MessageDto Message, MessageContents Contents);
