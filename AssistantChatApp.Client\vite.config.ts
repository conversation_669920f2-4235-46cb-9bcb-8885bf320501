import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import mdx from '@mdx-js/rollup';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {

  const env = loadEnv(mode, process.cwd());
  // const devServerPort: number = Number.parseInt(process.env.port ?? "5173");

  return {
    plugins: [
      {
        enforce: 'pre',
        ...mdx({
          /* jsxImportSource: …, otherOptions… */
          providerImportSource: '@mdx-js/react',
        })
      },
      react({include: /\.(jsx|js|mdx|md|tsx|ts)$/}),
    ],
    optimizeDeps: {
      exclude: ['lucide-react'],
    },
    build: {
      rollupOptions: {
        // Limit the number of parallel operations
        // Addresses `[commonjs--resolver] EMFILE: too many open files` error occurring during build.
        maxParallelFileOps: 5,
      },
      // Reduce chunk size limits to create smaller chunks
      chunkSizeWarningLimit: 1000,
    },
    server: {
      port: 5173,
      proxy: {
        '/api': {
          target: env.VITE_SERVER_URL ?? 'http://localhost:5000',
          changeOrigin: true,
          secure: false,
        }
      }
    }
  }
});
