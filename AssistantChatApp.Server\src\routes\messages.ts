import { Router } from 'express';
import {
  getM<PERSON>agesController,
  sendMessageController,
  updateMessageController,
  deleteMessageController,
} from '../controllers/messages';
import { validate } from '../middleware/validation';
import {
  getMessagesSchema,
  sendMessageSchema,
  updateMessageSchema,
  deleteMessageSchema,
} from '../validation/messages';
import { authenticate } from '../auth/middleware';

const router = Router({ mergeParams: true });

// All routes require authentication
router.use(authenticate);

// GET /chats/:chatId/messages - Get messages for a chat
router.get('/', validate(getMessagesSchema), getMessagesController);

// POST /chats/:chatId/messages - Send a message
router.post('/', validate(sendMessageSchema), sendMessageController);

// PATCH /chats/:chatId/messages/:messageId - Update a message
router.patch('/:messageId', validate(updateMessageSchema), updateMessageController);

// DELETE /chats/:chatId/messages/:messageId - Delete a message
router.delete('/:messageId', validate(deleteMessageSchema), deleteMessageController);

export default router;
