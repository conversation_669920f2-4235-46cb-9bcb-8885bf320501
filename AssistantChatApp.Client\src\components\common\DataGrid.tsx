import React, { useMemo, useState, useEffect } from 'react';
import {
  MaterialReactTable,
  type MRT_ColumnDef,
  type MRT_TableOptions,
} from 'material-react-table';
import { Box, Typography, Alert, CircularProgress } from '@mui/material';
import Papa from 'papaparse';

// Type definitions
interface ColumnSchema {
  key: string;
  label: string;
  type: 'string' | 'number' | 'boolean' | 'date';
  required?: boolean;
  sortable?: boolean;
  filterable?: boolean;
  width?: number;
  formatter?: (value: any) => string | React.ReactNode;
}

interface DataGridSchema {
  columns: ColumnSchema[];
  primaryKey?: string;
}

interface DataGridProps {
  // Data and schema (required)
  data?: Array<Record<string, any>>;
  csvData?: string | File;
  schema: DataGridSchema;

  // Schema customization
  columnOverrides?: Record<string, Partial<MRT_ColumnDef<any>>>;
  excludeColumns?: string[];

  // MaterialReactTable forwarded props
  enableColumnFilters?: boolean;
  enableSorting?: boolean;
  enableGrouping?: boolean;
  enablePagination?: boolean;
  enableRowSelection?: boolean;
  enableFullScreenToggle?: boolean;
  enableDensityToggle?: boolean;
  enableHiding?: boolean;

  // Additional configuration
  title?: string;
  pageSize?: number;
  loading?: boolean;

  // Event handlers
  onRowSelectionChange?: (selectedRows: any[]) => void;
  onDataExport?: (data: any[]) => void;
}

// Utility functions
const getTypeBasedRenderer = (type: string, customFormatter?: (value: any) => string | React.ReactNode) => {
  if (customFormatter) {
    return ({ cell }: { cell: any }) => customFormatter(cell.getValue());
  }

  switch (type) {
    case 'number':
      return ({ cell }: { cell: any }) => {
        const value = cell.getValue();
        return value != null ? value.toLocaleString() : '';
      };
    case 'boolean':
      return ({ cell }: { cell: any }) => {
        const value = cell.getValue();
        return value === true ? '✓' : value === false ? '✗' : '';
      };
    case 'date':
      return ({ cell }: { cell: any }) => {
        const value = cell.getValue();
        if (!value) return '';
        try {
          return new Date(value).toLocaleDateString();
        } catch {
          return String(value);
        }
      };
    default:
      return undefined; // Use default string rendering
  }
};

const transformSchemaToColumns = (
  schema: DataGridSchema,
  excludeColumns: string[] = [],
  overrides: Record<string, Partial<MRT_ColumnDef<any>>> = {}
): MRT_ColumnDef<any>[] => {
  return schema.columns
    .filter(columnSchema => !excludeColumns.includes(columnSchema.key))
    .map(columnSchema => {
      const baseColumn: MRT_ColumnDef<any> = {
        accessorKey: columnSchema.key,
        header: columnSchema.label,
        enableSorting: columnSchema.sortable ?? true,
        enableColumnFilter: columnSchema.filterable ?? true,
        size: columnSchema.width,
        Cell: getTypeBasedRenderer(columnSchema.type, columnSchema.formatter),
      };

      // Apply overrides if provided
      return overrides[columnSchema.key]
        ? { ...baseColumn, ...overrides[columnSchema.key] }
        : baseColumn;
    });
};

const validateSchemaAgainstData = (schema: DataGridSchema, data: Array<Record<string, any>>): string[] => {
  const errors: string[] = [];

  if (data.length === 0) return errors;

  const sampleRow = data[0];
  const availableKeys = Object.keys(sampleRow);

  schema.columns.forEach(column => {
    if (column.required && !availableKeys.includes(column.key)) {
      errors.push(`Required column '${column.key}' not found in data`);
    }
  });

  return errors;
};

const convertDataTypes = (data: Array<Record<string, any>>, schema: DataGridSchema): Array<Record<string, any>> => {
  return data.map(row => {
    const convertedRow = { ...row };

    schema.columns.forEach(column => {
      const value = convertedRow[column.key];
      if (value == null) return;

      switch (column.type) {
        case 'number':
          if (typeof value === 'string' && !isNaN(Number(value))) {
            convertedRow[column.key] = Number(value);
          }
          break;
        case 'boolean':
          if (typeof value === 'string') {
            const lowerValue = value.toLowerCase();
            if (lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes') {
              convertedRow[column.key] = true;
            } else if (lowerValue === 'false' || lowerValue === '0' || lowerValue === 'no') {
              convertedRow[column.key] = false;
            }
          }
          break;
        case 'date':
          if (typeof value === 'string') {
            const dateValue = new Date(value);
            if (!isNaN(dateValue.getTime())) {
              convertedRow[column.key] = dateValue;
            }
          }
          break;
      }
    });

    return convertedRow;
  });
};

// Main DataGrid component
const DataGrid: React.FC<DataGridProps> = ({
  data,
  csvData,
  schema,
  columnOverrides = {},
  excludeColumns = [],
  enableColumnFilters = true,
  enableSorting = true,
  enableGrouping = false,
  enablePagination = true,
  enableRowSelection = false,
  enableFullScreenToggle = true,
  enableDensityToggle = true,
  enableHiding = true,
  title,
  pageSize = 10,
  loading = false,
  onRowSelectionChange,
  onDataExport,
}) => {
  const [processedData, setProcessedData] = useState<Array<Record<string, any>>>([]);
  const [error, setError] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Process CSV data
  useEffect(() => {
    if (csvData) {
      setIsProcessing(true);
      setError('');

      const parseCSV = (csvContent: string) => {
        Papa.parse(csvContent, {
          header: true,
          skipEmptyLines: true,
          dynamicTyping: false, // We'll handle type conversion ourselves
          complete: (results) => {
            if (results.errors.length > 0) {
              setError(`CSV parsing error: ${results.errors[0].message}`);
              setIsProcessing(false);
              return;
            }

            const validationErrors = validateSchemaAgainstData(schema, results.data as Array<Record<string, any>>);
            if (validationErrors.length > 0) {
              setError(`Schema validation errors: ${validationErrors.join(', ')}`);
              setIsProcessing(false);
              return;
            }

            const convertedData = convertDataTypes(results.data as Array<Record<string, any>>, schema);
            setProcessedData(convertedData);
            setIsProcessing(false);
          },
          error: (error) => {
            setError(`CSV parsing failed: ${error.message}`);
            setIsProcessing(false);
          }
        });
      };

      if (typeof csvData === 'string') {
        parseCSV(csvData);
      } else if (csvData instanceof File) {
        const reader = new FileReader();
        reader.onload = (e) => parseCSV(e.target?.result as string);
        reader.onerror = () => {
          setError('Failed to read CSV file');
          setIsProcessing(false);
        };
        reader.readAsText(csvData);
      }
    }
  }, [csvData, schema]);

  // Process JSON data
  useEffect(() => {
    if (data) {
      setIsProcessing(true);
      setError('');

      const validationErrors = validateSchemaAgainstData(schema, data);
      if (validationErrors.length > 0) {
        setError(`Schema validation errors: ${validationErrors.join(', ')}`);
        setIsProcessing(false);
        return;
      }

      const convertedData = convertDataTypes(data, schema);
      setProcessedData(convertedData);
      setIsProcessing(false);
    }
  }, [data, schema]);

  // Generate columns from schema
  const columns = useMemo(() => {
    return transformSchemaToColumns(schema, excludeColumns, columnOverrides);
  }, [schema, excludeColumns, columnOverrides]);

  // Handle row selection
  const handleRowSelectionChange = (rowSelection: Record<string, boolean>) => {
    if (onRowSelectionChange) {
      const selectedRows = Object.keys(rowSelection)
        .filter(key => rowSelection[key])
        .map(key => processedData[parseInt(key)]);
      onRowSelectionChange(selectedRows);
    }
  };

  // MaterialReactTable configuration
  const tableOptions: Partial<MRT_TableOptions<Record<string, any>>> = {
    data: processedData,
    columns,
    enableColumnFilters,
    enableSorting,
    enableGrouping,
    enablePagination,
    enableRowSelection,
    enableFullScreenToggle,
    enableDensityToggle,
    enableHiding,
    initialState: {
      pagination: { pageSize },
    },
    ...(enableRowSelection && {
      onRowSelectionChange: handleRowSelectionChange,
    }),
    ...(onDataExport && {
      enableTopToolbar: true,
      renderTopToolbarCustomActions: () => (
        <button onClick={() => onDataExport(processedData)}>
          Export Data
        </button>
      ),
    }),
  };

  if (loading || isProcessing) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!data && !csvData) {
    return (
      <Alert severity="warning" sx={{ m: 2 }}>
        No data provided. Please provide either 'data' or 'csvData' prop.
      </Alert>
    );
  }

  return (
    <Box>
      {title && (
        <Typography variant="h6" component="h2" gutterBottom sx={{ p: 2 }}>
          {title}
        </Typography>
      )}
      <MaterialReactTable {...tableOptions} />
    </Box>
  );
};

// Example usage component
const DataGridExample: React.FC = () => {
  // Sample data
  const sampleData = [
    {
      id: 1,
      customerName: 'John Doe',
      amount: 1250.50,
      date: '2024-01-15',
      completed: true,
      category: 'Electronics'
    },
    {
      id: 2,
      customerName: 'Jane Smith',
      amount: 875.25,
      date: '2024-01-16',
      completed: false,
      category: 'Clothing'
    },
    {
      id: 3,
      customerName: 'Bob Johnson',
      amount: 2100.00,
      date: '2024-01-17',
      completed: true,
      category: 'Electronics'
    },
    {
      id: 4,
      customerName: 'Alice Brown',
      amount: 450.75,
      date: '2024-01-18',
      completed: false,
      category: 'Books'
    }
  ];

  // Sample schema
  const salesSchema: DataGridSchema = {
    columns: [
      { key: 'id', label: 'ID', type: 'number', sortable: true, width: 80 },
      { key: 'customerName', label: 'Customer Name', type: 'string', required: true },
      {
        key: 'amount',
        label: 'Amount',
        type: 'number',
        formatter: (val) => `$${val?.toFixed(2) || '0.00'}`
      },
      { key: 'date', label: 'Sale Date', type: 'date' },
      { key: 'completed', label: 'Completed', type: 'boolean' },
      { key: 'category', label: 'Category', type: 'string' }
    ],
    primaryKey: 'id'
  };

  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  const handleRowSelection = (rows: any[]) => {
    setSelectedRows(rows);
    console.log('Selected rows:', rows);
  };

  const handleDataExport = (data: any[]) => {
    console.log('Exporting data:', data);
    // In a real implementation, you might convert to CSV and download
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" gutterBottom>
        DataGrid Component Demo
      </Typography>

      <DataGrid
        data={sampleData}
        schema={salesSchema}
        title="Sales Dashboard"
        enableColumnFilters
        enableSorting
        enableGrouping
        enableRowSelection
        pageSize={10}
        onRowSelectionChange={handleRowSelection}
        onDataExport={handleDataExport}
        columnOverrides={{
          amount: {
            Cell: ({ cell }) => (
              <Box sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                ${cell.getValue()?.toFixed(2)}
              </Box>
            )
          }
        }}
      />

      {selectedRows.length > 0 && (
        <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
          <Typography variant="h6">Selected Rows:</Typography>
          <pre>{JSON.stringify(selectedRows, null, 2)}</pre>
        </Box>
      )}
    </Box>
  );
};

export default DataGridExample;
