import React from "react";
import { Box, Typography, Divider, Stack } from "@mui/material";
import { Message } from "../../types";
import MessageItem from "./MessageItem";
import { useTranslation } from "react-i18next";
import {
  i18nLocaleToDateFnsLocale,
  formatAsYesterdayTodayOrDate,
} from "../common/dateTimeUtils";

interface MessageListProps {
  messages: Message[];
  chatId: string;
}

export const MessageList: React.FC<MessageListProps> = ({ messages, chatId }) => {
  const { t, i18n } = useTranslation(["common", "messages"]);
  const dateFnsLocale = i18nLocaleToDateFnsLocale(i18n.language);
  const formatDateHeader = React.useCallback(
    (dateString: string) => {
      const date = new Date(dateString);
      return formatAsYesterdayTodayOrDate(date, dateFnsLocale, t);
    },
    [t, dateFnsLocale],
  );

  if (messages.length === 0) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
          py: 8,
        }}
      >
        <Typography variant="body1" color="text.secondary">
          {t("no_messages_yet", { ns: "messages" })}
        </Typography>
      </Box>
    );
  }

  // Group messages by date
  const messagesByDate: Record<string, Message[]> = {};
  messages.forEach((message) => {
    const date = new Date(message.createdAt).toDateString();
    if (!messagesByDate[date]) {
      messagesByDate[date] = [];
    }
    messagesByDate[date].push(message);
  });

  return (
    <Box sx={{ flexGrow: 1, width: "100%" }}>
      {Object.keys(messagesByDate).map((date) => (
        <Stack key={date} spacing={1}>
          <Stack alignItems="center" justifyContent="center" spacing={0.5}>
            <Divider sx={{ flexGrow: 1 }} />
            <Typography variant="caption" color="text.secondary">
              {formatDateHeader(date)}
            </Typography>
            <Divider sx={{ flexGrow: 1 }} />
          </Stack>

          {messagesByDate[date].map((message, index) => (
            <MessageItem
              key={message.id}
              message={message}
              chatId={chatId}
              isConsecutive={
                index > 0 &&
                messagesByDate[date][index - 1].author.id === message.author.id
              }
            />
          ))}
        </Stack>
      ))}
    </Box>
  );
};
