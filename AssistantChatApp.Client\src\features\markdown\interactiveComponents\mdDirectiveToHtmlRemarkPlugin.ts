import { h } from "hastscript";
import type { Root } from "mdast";
import type { Node } from "unist";
import type {
  ContainerDirective,
  LeafDirective,
  TextDirective,
} from "mdast-util-directive";
import { visit } from "unist-util-visit";
import { tryParseJsonObject } from "./utils";

type DirectiveNode = TextDirective | LeafDirective | ContainerDirective;

const isDirectiveNode = (node: Node): node is DirectiveNode => {
  const { type } = node;
  return (
    type === "textDirective" ||
    type === "leafDirective" ||
    type === "containerDirective"
  );
};

/** Extracts raw text from directive node 1st child if it's a paragraph or a JSON code block */
const tryExtractTextFromDirectiveNodeChildren = (node: DirectiveNode) => {
  if (node.children.length <= 0) return undefined;

  const child = node.children[0];
  switch (child.type) {
    case "paragraph": {
      const textNode =
        child.children.length > 0 ? child.children[0] : undefined;
      if (textNode && textNode.type === "text") {
        return textNode.value;
      }
      return undefined;
    }
    case "code": {
      if (child.lang === "json") {
        return child.value;
      }
      return undefined;
    }
    default:
      return undefined;
  }
};

export const tryParseJsonObjectFromDirectiveNodeChildren = (
  node: DirectiveNode
) => {
  const text = tryExtractTextFromDirectiveNodeChildren(node);
  if (!text) return undefined;
  return tryParseJsonObject(text);
};

/** Remark plugin that creates HAST nodes from MDAST directive nodes. */
export function mdDirectiveToHtmlPlugin() {
  /**
   * @param {Root} tree
   *   Tree.
   * @returns {undefined}
   *   Nothing.
   */
  return function (tree: Root): undefined {
    visit(tree, function (node) {
      if (isDirectiveNode(node)) {
        // console.debug("Directive node: ", node)
        const hast = h(node.name, node.attributes || {});
        const data = node.data || (node.data = {});
        data.hName = hast.tagName;
        data.hProperties = hast.properties;
        //data.hChildren = hast.children
        //node.children = []
      }
    });
  };
}
