import React from "react";
import { User, Chat } from "../../types";
import { useGetUsers } from "../../queries/user";

const getChatFormInitialValues = (chat?: Chat) =>
  chat
    ? {
        title: chat.title ?? "",
        description: chat.description ?? "",
        participants: chat.participants,
        tags: chat.tags ?? [],
        inputTag: "",
      }
    : {
        title: "",
        description: "",
        participants: [],
        tags: [],
        inputTag: "",
      };

export const useChatFormState = (chat?: Chat) => {
  const initialValues = getChatFormInitialValues(chat);
  const [title, setTitle] = React.useState(initialValues.title);
  const [description, setDescription] = React.useState(
    initialValues.description,
  );
  const [selectedParticipants, setSelectedParticipants] = React.useState<
    User[]
  >(initialValues.participants);
  const [tags, setTags] = React.useState<string[]>(initialValues.tags);
  const [inputTag, setInputTag] = React.useState(initialValues.inputTag);

  const reset = () => {
    setTitle(initialValues.title);
    setDescription(initialValues.description);
    setSelectedParticipants(initialValues.participants);
    setTags(initialValues.tags);
    setInputTag(initialValues.inputTag);
  }

  return {
    formValues: {
      title,
      description,
      participants: selectedParticipants,
      tags,
      inputTag,
    },
    setTitle,
    setDescription,
    setSelectedParticipants,
    setTags,
    setInputTag,
    reset,
  };
};

export const useUsers = (enabled?: boolean) => {
  const { data, isLoading } = useGetUsers({ enabled });

  const users: User[] = data?.data ?? [];
  const usersLoading = isLoading;

  return { users, usersLoading };
};
