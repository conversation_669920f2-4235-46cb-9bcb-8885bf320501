﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>0a11203c-ff0f-4208-bc8d-997a89a94abf</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <None Include="appsettings.Example.json" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.14" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.OpenTelemetry" Version="4.2.0" />
    <PackageReference Include="Serilog.Sinks.Trace" Version="4.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AI\AssistantChatApp.AIAgents.Client\AssistantChatApp.AIAgents.Client.fsproj" />
    <ProjectReference Include="..\AssistantChatApp.Aspire.ServiceDefaults\AssistantChatApp.Aspire.ServiceDefaults.csproj" />
    <ProjectReference Include="..\AssistantChatApp.DataStore.Migrations.PostgreSQL\AssistantChatApp.DataStore.Migrations.PostgreSQL.csproj" />
    <ProjectReference Include="..\AssistantChatApp.DataStore\AssistantChatApp.DataStore.csproj" />
    <ProjectReference Include="..\AssistantChatApp.Server.Services\AssistantChatApp.Server.Services.csproj" />
  </ItemGroup>
</Project>
