# SearchQuery


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**index** | **string** |  | [optional] [default to undefined]
**query** | **string** |  | [optional] [default to undefined]
**filters** | **Array&lt;{ [key: string]: Array&lt;string&gt;; }&gt;** |  | [optional] [default to undefined]
**minRelevance** | **number** |  | [optional] [default to undefined]
**limit** | **number** |  | [optional] [default to undefined]
**args** | **{ [key: string]: any | null; }** |  | [optional] [default to undefined]

## Example

```typescript
import { SearchQuery } from './api';

const instance: SearchQuery = {
    index,
    query,
    filters,
    minRelevance,
    limit,
    args,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
