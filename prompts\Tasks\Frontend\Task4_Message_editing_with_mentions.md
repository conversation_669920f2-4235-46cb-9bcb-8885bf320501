# v1: What to do to implement the functionality

I'm developing a multi-use chat web app. I need to implement a mentions-functionality (i.e., a capability to explicitly specify recipients of the message in a group chat rather than addressing the message to all). For that, I have a special mentions property defined in the `Message` domain type, e.g.:

```typescript
export interface Message {
  id: string;
  chatId: string;
  author: User;
  content: string;
  otherContent: MessageAdditionalContent;
  createdAt: string;
  updatedAt: string;
  mentions?: string[]; // User IDs mentioned in the message
  tags?: { id: string; name: string } [];
}
```

Now I want to implement a Message editing field. It should support formatted text editing (for now, Markdown-based and powered by `@mdxeditor/editor`) React component. I want to be able to specify the recipients (mentions) in a user-friendly manner. This means that it should be easy to add new mention to the message text (i.e. not only by typing some special character combinations, but also by just clicking a button on the toolbar). And I also want to make the mentions rendered in a  more interactive manner, i.e. as an intuitive UI element with user display name rather that raw text with user ID.

Another feature I'd like to have is to show a list of all mentioned users somewhere near the message field (similar to recipients list in email clients). And probably it'd be nice to be able to edit this list instead of the message text. However, in that case I see the issue with synchronization of the message text's mentions and actual mentions.

So, I want your advice on how to implement this, or your corrections or even different proposals.

## Mentions parsing

A function that parses text and extracts user mentions from it. A mention is represented as a `@` symbol followed by a user ID. The function should return an array of user IDs.


---

# v2: Fix MDXEditor Plugin implementation

I'm developing a multi-use chat web app. I need to implement a mentions-functionality (i.e., a capability to explicitly specify recipients of the message in a group chat rather than addressing the message to all). For that, I have a special mentions property defined in the Message domain type, e.g.:

```typescript
export interface Message {
  id: string;
  chatId: string;
  author: User;
  content: string;
  otherContent: MessageAdditionalContent;
  createdAt: string;
  updatedAt: string;
  mentions?: string[]; // User IDs mentioned in the message
  tags?: { id: string; name: string } [];
}
```

Currently I'm working on a Message editing field. It should support formatted text editing (for now, Markdown-based and powered by `@mdxeditor/editor`) React component. I want to be able to specify the recipients (mentions) in a user-friendly manner. This means that it should be easy to add new mention to the message text (i.e. not only by typing some special character combinations, but also by just clicking a button on the toolbar). And I also want to make the mentions rendered in a more interactive manner, i.e. as an intuitive UI element with user display name rather that raw text with user ID.

Another feature I'd like to have is to show a list of all mentioned users somewhere near the message field (similar to recipients list in email clients). And probably it'd be nice to be able to edit this list instead of the message text. However, in that case I see the issue with synchronization of the message text's mentions and actual mentions.


So, to make mentions look more user-friendly, it seems like I need a custom MDXEditor plugin that renders a custom component (provided as a parameter to the plugin) instead of raw mention text (i.e., text fragments started by @ and followed by a user ID; but it should recognize only valid user IDs, so that anything else would be treated as a plain text).

The implementation I have now is in the attached file.

The problem is that it has some TypeScript errors in the `mentionsPlugin` implementation, particularly in the "Register import/export visitors" part:

// Register import/export visitors
realm.pub(addImportVisitor$, ['mention', importVisitor]);
realm.pub(addExportVisitor$, [MentionNode, exportVisitor]);

The errors are:

`Type 'string' is not assignable to type 'MdastImportVisitor'.ts(2322)`

`Type 'typeof MentionNode' has no properties in common with type 'LexicalVisitor'.ts(2559)`

Please, check what is exactly wrong, and fix the errors.


# v3: How to implement mention rendering inside the MDXEditor

I'm developing a multi-use chat web app. I need to implement a mentions-functionality (i.e., a capability to explicitly specify recipients of the message in a group chat rather than addressing the message to all). For that, I have a special mentions property defined in the Message domain type, e.g.:

```typescript
export interface User {
  id: string;
  displayName: string;
  avatar?: string;
  createdAt: string;
  isAI: boolean;
}

export interface Message {
  id: string;
  chatId: string;
  author: User;
  content: string;
  otherContent: MessageAdditionalContent;
  createdAt: string;
  updatedAt: string;
  mentions?: string[]; // User IDs mentioned in the message
  tags?: { id: string; name: string } [];
}
```

Currently I'm working on a Message editing field. It should support formatted text editing (for now, Markdown-based and powered by `@mdxeditor/editor`) React component. I want to be able to specify the recipients (mentions) in a user-friendly manner. For now, I've decided to add mentions in a typical for chat/messaging apps, e.g. by adding to the message text `@` character followed by the valid user ID. And if the user ID is valid, this fragment is recognized as a mention (otherwise, it's treated as a regular text). But I also want to make these mentions rendered inside the MDXEditor in a more intuitive manner, i.e. as a highlighted user display name rather that raw text containing the user ID (the ID can look confusing and ugly as it can be a UUID).
So, what would be the easiest way to implement such mention rendering inside the MDXEditor?

# v4: User mentions via Markdown directives

I'm developing a group chat web app frontend via React.js and TypeScript. Currently I'm working on the message editor. It supports formatted text via Markdown. For that, I've decided to use MDXEditor: it supports almost all the necessary formatting features. The only thing it lacks out-of-the box is "user mentions" (it's not a part of Markdown standard). So, I want to add such feature in some way. The requirements are:
* User mention must be a some kind of a text construct that encodes a mentioned user ID. The construct must be clearly distinguished from the rest of the text.
* Being a special text construct, it should provide a user-friendly rendered representation. I.e., instead of showing a special text, for example, it should be rendered as a highlighted user's display name.
* A popup with a list of users to select a mentioned user should be provided.
However, I'd like to have a simple solution, i.e. if possible, leveraging already implemented MDXEditor features. After investigating MDXEditor's documentation, I've discovered the "directives" feature (https://mdxeditor.dev/editor/docs/custom-directive-editors). So, maybe, this is what could be used to implement user mentions? Give me your thoughts on that matter.
And here is the Message domain type I expect to be created by submitting an edited message:

```typescript
export interface User {
  id: string;
  displayName: string;
  createdAt: string;
}
export interface Message {
  id: string;
  chatId: string;
  author: User;
  content: string;
  mentions?: string[]; // User IDs mentioned in the message
}
```

# v5: User mentions via `@`-prefix

I'm developing a group chat web app frontend. Currently I'm working on the message editor with text formatting based on the Markdown. The tech stack I use for that:
* Framework: React.js
* Language: TypeScript
* UI kit: Material UI (MUI)
* Markdown editor for the message editor: MDXEditor

For the message editor, I need to implement the user mentions feature. The requirements are:
* User mention must be a some kind of a recognizable text construct that encodes a mentioned user ID (i.e., the construct must be clearly distinguished from the rest of the text). I've decided to go with `@`-prefix, e.g. `@john_doe`: it's already a common way to denote a user mention in many chat apps.
* For the better UX, in the MDXEditor's "rich text view" mode, I want this user mentions to be shown in a more intuitive manner, e.g., as a highlighted text span consisting of `@` and a user's display name instead of the user's ID.

So, implement me such feature for the MDXEditor-based message editor. Here are some of the elements needed to be implemented:
1. A user-friendly component to render a user mention. As I understand, for now it could be a very simple component that renders a text span with `@` and user's display name. The user's display name can be obtained by `useGetUser: (userId: string) => User` hook that I have already implemented (you don't need to implement it, just use it as is).
2. UI component that facilitates user mention adding. It should be a popup list of users that opened after user types `@` in the editor or clicks a dedicated button on the toolbar. Clicking on a user list item results in inserting a corresponding user mention on the current cursor position (but check that `@` prefix is not repeated). It'd be also nice to filter the list by the user's display name or ID as user types. The list of available users can be obtained by `useChatParticipants: (chatId: string) => User[]` hook (you don't need to implement it, just use it as is).
3. Besides that, also implement a minimalistic example that integrates the above components into the component of message editor based on the MDXEditor. The message editor should represent a form that creates a valid `Message` value on submit. To submit a message, you can use `useSendMessage` mutation (implemented via TanStack Query). Its `mutateFn` accepts `{ content: string, mentions: string[], chatId: string }`.

Additional remarks:
- I suppose, the list of mentioned users should be constructed by parsing the message text and extracting the mention user IDs from the user mention directives (rather than mutating the mentions list during the message editing what can cause a need to synchronize the mentions in text with the mentions list).
- The message editor component props: `{ chatId: string, author: User }`

The necessary domain types are:
```typescript
export interface User {
  id: string;
  displayName: string;
  createdAt: string;
}
export interface Message {
  id: string;
  chatId: string;
  author: User;
  content: string;
  mentions?: string[]; // User IDs mentioned in the message
}
```
