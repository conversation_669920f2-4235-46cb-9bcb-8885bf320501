using AssistantChatApp.Server.AspNetCore.Data;
using AssistantChatApp.Server.AspNetCore.Models;

using Microsoft.Extensions.Configuration;
using AssistantChatApp.AIAgents.Shared;
using AssistantChatApp.AIAgents.Client;
// using AssistantChatApp.Server.AspNetCore.DTOs;
using Microsoft.EntityFrameworkCore;

using AgentMessage = AssistantChatApp.AIAgents.Shared.ChatMessage;
using AssistantChatApp.AIAgents.Shared.Api;
using System.Linq;
using AssistantChatApp.Server.AspNetCore.Services.Helpers;
using Microsoft.IdentityModel.Tokens;

namespace AssistantChatApp.Server.AspNetCore.Services
{
    public class AIAssistantService(
        UserService userService,
        MessageService messageService,
        AgentClientProxy agentClient
    )
    {
        /// <summary>
        /// Holds the default quantity of the recent chat messages sent to the AI assistant.
        /// </summary>
        private const int chatHistoryWindowSize = 50;

        public async Task<IReadOnlyList<DTOs.MessageDto>> AskAsync(
            string chatId,
            DTOs.UserDto inquirer,
            DTOs.UserDto assistant,
            int chatHistoryWindowSize = chatHistoryWindowSize
        )
        {
            var messagesForAiPage =
                await messageService.GetMessagesForAIAsync(
                    chatId, inquirer.Id,
                    page: 1, limit: chatHistoryWindowSize
                );
            // TODO: Move message ordering to the database query
            var chatMessages =
                from m in messagesForAiPage.Data
                orderby m.Message.CreatedAt
                select m.ToAgentMessage();
            var aiAgentUser = new ChatUser(assistant.Id, assistant.DisplayName);
            var inquirerUser = new ChatUser(inquirer.Id, inquirer.DisplayName);
            var rspMessages =
                await agentClient.GetChatCompletion(aiAgentUser, inquirerUser, [.. chatMessages]);
            var savedRspMessages =
                await AddAssistantAgentMessagesAsync(rspMessages, chatId, assistant.Id);
            return savedRspMessages;
        }

        // It's important to not return an `IAsyncEnumerable` from the method,
        // otherwise it will result in duplicated messages in the DB
        // if the calling code uses the `IAsyncEnumerable` directly without materializing it first.
        public async Task<IReadOnlyList<DTOs.MessageDto>> AddAssistantAgentMessagesAsync(
            AgentMessage[] aiMessages,
            string chatId,
            string assistantUserId
        )
        {
            var messageDtos =
                from m in aiMessages
                select m.ToCreateMessageDto();
            //TODO: Create many messages at once
            var savedMessages = new List<DTOs.MessageDto>();
            foreach (var message in messageDtos)
            {
                var createdMessage = await messageService.CreateMessageAsync(chatId, assistantUserId, message);
                savedMessages.Add(createdMessage);
            }
            return savedMessages;
        }

        public async Task<List<DTOs.UserDto>> TryGetMentionedAIUsers(DTOs.MessageDto message)
        {
            var aiUsers = new List<DTOs.UserDto>();
            if (message.Mentions != null)
            {
                var mentionedUserTasks =
                    message.Mentions.Select(userService.GetUserByIdAsync);
                var mentionedUsers = await Task.WhenAll(mentionedUserTasks);
                aiUsers.AddRange(
                    from user in mentionedUsers
                    where user?.IsAI ?? false
                    select user
                );
            }
            return aiUsers;
        }

        public async Task<IReadOnlyList<DTOs.MessageDto>> AskFirstMentionedAsync(
            DTOs.MessageDto message
        )
        {
            var mentionedAIs = await TryGetMentionedAIUsers(message);
            var firstMentionedAi = mentionedAIs.FirstOrDefault();
            if (firstMentionedAi != null)
            {
                return await AskAsync(message.ChatId, message.Author, firstMentionedAi);
            }
            return [];
        }

        public async Task<IReadOnlyList<DTOs.MessageDto>> AskAllMentionedAsync(
            DTOs.MessageDto message
        )
        {
            var mentionedAIs = await TryGetMentionedAIUsers(message);
            if (mentionedAIs.IsNullOrEmpty())
            {
                return [];
            }
            var aiUserTasks =
                mentionedAIs.Select(async aiUser =>
                {
                    var rspMsgs = await AskAsync(message.ChatId, message.Author, aiUser);
                    return rspMsgs;
                });
            var aiUserResponses = await Task.WhenAll(aiUserTasks);
            return [.. aiUserResponses.SelectMany(r => r)];
        }
    }
}
