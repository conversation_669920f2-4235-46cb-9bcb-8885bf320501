import { Response, NextFunction } from 'express';
import { verifyToken } from './jwt';
import { AuthRequest } from '../types';
import { db } from '../db';
import { users } from '../db/schema';
import { eq } from 'drizzle-orm';

/**
 * Authentication middleware
 * Verifies JW<PERSON> token and attaches user to request
 */
export async function authenticate(req: AuthRequest, res: Response, next: NextFunction) {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const token = authHeader.split(' ')[1];
    const payload = verifyToken(token);

    if (!payload) {
      return res.status(401).json({ message: 'Invalid or expired token' });
    }

    // Get user from database
    const userResult = await db.select().from(users).where(eq(users.id, payload.userId)).limit(1);
    
    if (!userResult.length) {
      return res.status(401).json({ message: 'User not found' });
    }

    const user = userResult[0];

    // Attach user to request
    req.user = {
      id: user.id,
      email: user.email,
      displayName: user.displayName,
      avatar: user.avatar,
      createdAt: user.createdAt,
      isAI: user.isAI,
    };
    req.userId = user.id;

    next();
  } catch (error) {
    next(error);
  }
}
