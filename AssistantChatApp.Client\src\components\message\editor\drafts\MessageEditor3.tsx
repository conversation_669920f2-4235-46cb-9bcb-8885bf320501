import React, { useState, useRef } from 'react';
import {
  MDXEditor,
  MDXEditorMethods,
  toolbarPlugin,
  headingsPlugin,
  listsPlugin,
  quotePlugin,
  thematicBreakPlugin,
  markdownShortcutPlugin,
  linkPlugin,
  linkDialogPlugin,
  imagePlugin,
  tablePlugin,
  codeBlockPlugin,
  codeMirrorPlugin,
  diffSourcePlugin,
  frontmatterPlugin,
  directivesPlugin,
  // AdmonitionDirectiveDescriptor,
  // GenericDirectiveEditor,
  // NestedLexicalEditor,
  // insertDirective$,
  // directiveDescriptors$,
  // createDirectiveDescriptor,
  // DirectiveNode,
  // $createDirectiveNode,
  // $isDirectiveNode,
} from '@mdxeditor/editor';
import {
  Box,
  Paper,
  Button,
  Typography,
  IconButton,
  Toolbar,
} from '@mui/material';
import { PersonAdd as PersonAddIcon, Send as SendIcon } from '@mui/icons-material';
// import { $getSelection, $isRangeSelection, $createTextNode } from 'lexical';
// import { $getNodeByKey } from 'lexical';
import { SendMessageRequest } from '../../../../types';
// import { useSendMessage } from '../../mutations/message';
import { useMutation } from '@tanstack/react-query';
import { UserSelectionPopup } from '../../UserSelectionPopup';

import '@mdxeditor/editor/style.css';
import { extractMentions, mentionDirectiveDescriptor } from '../../messageEditorUtils';

// Domain types
export interface User {
  id: string;
  displayName: string;
  createdAt: string;
}

export interface Message {
  id: string;
  chatId: string;
  author: User;
  content: string;
  mentions?: string[];
}

/*
// Mock hooks (replace with your actual implementations)
const useGetUser = (userId: string): User | undefined => {
  // Mock implementation - replace with your actual hook
  const mockUsers: User[] = [
    { id: 'user1', displayName: 'John Doe', createdAt: '2024-01-01' },
    { id: 'user2', displayName: 'Jane Smith', createdAt: '2024-01-02' },
    { id: 'user3', displayName: 'Bob Johnson', createdAt: '2024-01-03' },
  ];
  return mockUsers.find(user => user.id === userId);
};

const useChatParticipants = (chatId: string): User[] => {
  // Mock implementation - replace with your actual hook
  return [
    { id: 'user1', displayName: 'John Doe', createdAt: '2024-01-01' },
    { id: 'user2', displayName: 'Jane Smith', createdAt: '2024-01-02' },
    { id: 'user3', displayName: 'Bob Johnson', createdAt: '2024-01-03' },
    { id: 'user4', displayName: 'Alice Brown', createdAt: '2024-01-04' },
  ];
};
*/

const useSendMessage = () => {
  return useMutation({
    mutationFn: async ({ textContent, mentions, chatId }: SendMessageRequest) => {
      // Mock implementation - replace with your actual mutation
      console.log('Sending message:', { textContent, mentions, chatId });
      return { success: true };
    },
  });
};

// Message Editor Component
interface MessageEditorProps {
  chatId: string;
}

export const MessageEditor: React.FC<MessageEditorProps> = ({ chatId }) => {
  const [content, setContent] = useState('');
  const [mentionPopupAnchor, setMentionPopupAnchor] = useState<HTMLElement | null>(null);
  const [mentionSearchQuery, setMentionSearchQuery] = useState('');
  const editorRef = useRef<MDXEditorMethods>(null);
  const sendMessageMutation = useSendMessage();

  const handleSubmit = async () => {
    if (!content.trim()) return;

    const mentions = extractMentions(content);

    try {
      await sendMessageMutation.mutateAsync({
        textContent: content,
        mentions,
        chatId,
      });

      // Clear the editor after successful send
      setContent('');
      editorRef.current?.setMarkdown('');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleMentionButtonClick = (event: React.MouseEvent<HTMLElement>) => {
    setMentionPopupAnchor(event.currentTarget);
  };

  const handleMentionPopupClose = () => {
    setMentionPopupAnchor(null);
    setMentionSearchQuery('');
  };

  const handleUserSelect = (user: User) => {
    // const mentionMarkdown = `:mention[userId="${user.id}"]`;
    const mentionMarkdown = `:mention[${user.id}]`;

    // Insert the mention at cursor position
    if (editorRef.current) {
      const currentContent = editorRef.current.getMarkdown();
      const newContent = currentContent + mentionMarkdown;
      editorRef.current.setMarkdown(newContent);
      setContent(newContent);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleSubmit();
    }
  };

  return (
    <Box>
      <Paper variant="outlined" sx={{ mb: 2 }}>
        <Toolbar variant="dense" sx={{ minHeight: 48 }}>
          <IconButton
            size="small"
            onClick={handleMentionButtonClick}
            title="Mention user"
          >
            <PersonAddIcon />
          </IconButton>
          <Box sx={{ flexGrow: 1 }} />
          <Button
            variant="contained"
            size="small"
            startIcon={<SendIcon />}
            onClick={handleSubmit}
            disabled={!content.trim() || sendMessageMutation.isPending}
          >
            {sendMessageMutation.isPending ? 'Sending...' : 'Send'}
          </Button>
        </Toolbar>

        <Box sx={{ p: 2, minHeight: 150 }} onKeyDown={handleKeyDown}>
          <MDXEditor
            ref={editorRef}
            markdown={content}
            onChange={setContent}
            plugins={[
              toolbarPlugin({
                toolbarContents: () => <></>, // Empty toolbar since we have custom toolbar
              }),
              headingsPlugin(),
              listsPlugin(),
              quotePlugin(),
              thematicBreakPlugin(),
              markdownShortcutPlugin(),
              linkPlugin(),
              linkDialogPlugin(),
              imagePlugin(),
              tablePlugin(),
              codeBlockPlugin(),
              codeMirrorPlugin(),
              diffSourcePlugin(),
              frontmatterPlugin(),
              directivesPlugin({
                directiveDescriptors: [mentionDirectiveDescriptor],
              }),
            ]}
            placeholder="Type your message... (Ctrl+Enter to send)"
          />
        </Box>
      </Paper>

      <UserSelectionPopup
        chatId={chatId}
        anchorEl={mentionPopupAnchor}
        open={Boolean(mentionPopupAnchor)}
        onClose={handleMentionPopupClose}
        onSelectUser={handleUserSelect}
        searchQuery={mentionSearchQuery}
      />

      {/* Debug info */}
      <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
        <Typography variant="caption" component="div">
          Debug: Extracted mentions: {JSON.stringify(extractMentions(content))}
        </Typography>
        <Typography variant="caption" component="div">
          Raw content: {content}
        </Typography>
      </Box>
    </Box>
  );
};

// Example usage
export const Demo: React.FC = () => {
  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Message Editor with User Mentions
      </Typography>

      <MessageEditor chatId="chat-1" />
    </Box>
  );
};
