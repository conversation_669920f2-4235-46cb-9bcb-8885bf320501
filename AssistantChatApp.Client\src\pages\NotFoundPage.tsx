import React from 'react';
import { Box, Button, Container, Typography } from '@mui/material';
import { Link } from '@tanstack/react-router';
import { MessageSquare } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const NotFoundPage: React.FC = () => {
  const { t } = useTranslation("common");

  return (
    <Container maxWidth="md">
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 'calc(100vh - 200px)',
          textAlign: 'center',
          py: 8,
        }}
      >
        <MessageSquare size={120} strokeWidth={1} color="#3a7bd5" style={{ marginBottom: 24 }} />

        <Typography variant="h2" component="h1" gutterBottom fontWeight="bold">
          404
        </Typography>

        <Typography variant="h4" gutterBottom>
          {t("pageNotFoundTitle")}
        </Typography>

        <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 500 }}>
          {t("pageNotFoundMessage")}
        </Typography>

        <Button
          component={Link}
          to="/"
          variant="contained"
          size="large"
          disableElevation
        >
          {t("goToHome")}
        </Button>
      </Box>
    </Container>
  );
};

export default NotFoundPage;
