I need you to develop TanStack Query (React Query) queries and mutations and put them to the dedicated directories (`src/queries` and `src/mutations`, under the frontend app root directory). Inside these directories, queries and mutations should be grouped by the entity they operate on (e.g., `queries/chat`, `mutations/chat`, `queries/message`, `mutations/message`, etc.).
Queries and mutations should be based on the existing API wrappers (`src/api/userApi.ts`, `src/api/chatApi.ts`) and the data models (`src/types/index.ts`).

When implementing queries, define the keys for them as separate reusable values or functions, to be able to reference them in a more type-safe manner (in a query definition, when invalidating a query, and so on).

Below are the queries and mutations you need to develop.

### Queries

- `getChats`: Fetches a list of chats with pagination and filtering capabilities.
- `getChat`: Fetches a single chat by ID.
- `getMessages`: Fetches a list of messages for a chat with pagination.
- `getUsers`: Fetches a list of users with pagination and filtering capabilities.
- `getUser`: Fetches a single user by ID.
- `getAIAssistants`: Fetches a list of AI assistants.

### Mutations

- `createChat`: Creates a new chat.
- `updateChat`: Updates an existing chat.
- `deleteChat`: Deletes a chat.
- `sendMessage`: Sends a new message to a chat.
- `updateMessage`: Updates an existing message.
- `deleteMessage`: Deletes a message.
