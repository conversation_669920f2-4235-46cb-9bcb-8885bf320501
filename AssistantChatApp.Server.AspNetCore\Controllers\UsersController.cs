using AssistantChatApp.Server.Contracts.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AssistantChatApp.Server.Services;

namespace AssistantChatApp.Server.AspNetCore.Controllers
{
    [ApiController]
    [Route("/api/users")]
    [Authorize]
    public class UsersController : ControllerBase
    {
        private readonly UserService _userService;

        public UsersController(UserService userService)
        {
            _userService = userService;
        }

        [HttpGet]
        public async Task<IActionResult> GetUsers([FromQuery] int page = 1, [FromQuery] int limit = 20, [FromQuery] string? search = null)
        {
            var users = await _userService.GetUsersAsync(page, limit, search);
            return Ok(users);
        }

        [HttpGet("{userId}")]
        public async Task<IActionResult> GetUser(string userId)
        {
            var user = await _userService.GetUserByIdAsync(userId);

            if (user == null)
            {
                return NotFound(new ErrorResponseDto { Message = "User not found" });
            }

            return Ok(user);
        }

        [HttpGet("ai-assistants")]
        public async Task<IActionResult> GetAIAssistants()
        {
            var aiAssistants = await _userService.GetAIAssistantsAsync();
            return Ok(aiAssistants);
        }
    }
}
