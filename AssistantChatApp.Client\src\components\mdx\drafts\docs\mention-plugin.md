MDXEditor plugin that handles mention rendering and editing. This plugin will parse `@userId` patterns, validate them against a user list, and render them as custom components.

## Key Features

### 1. **Custom Lexical Node**

* `MentionNode` extends `DecoratorNode` to render custom React components
* Handles serialization/deserialization for persistence
* Supports DOM import/export for copy-paste scenarios

### 2. **Auto-transformation**

* Automatically converts `@userId` patterns to mention nodes
* Only converts valid user IDs (validates against provided user list)
* Debounced to avoid excessive processing

### 3. **Flexible Rendering**

* Accepts custom mention component via plugin parameters
* Falls back to a default styled component
* Material-UI integration example with chips and avatars

### 4. **Dual Management**

* Mentions can be added via typing `@userId` or toolbar button
* Recipients list shows all mentions with remove functionality
* Bidirectional synchronization between editor and recipients list

### 5. **Event Handling**

* `onMentionClick`: Handle mention interactions
* `onMentionAdd`/`onMentionRemove`: Track mention changes
* Programmatic mention insertion via `insertMention` utility

## Integration Notes

1. **User Validation**: Only `@userId` patterns with valid user IDs are converted to mentions
2. **Markdown Export**: Mentions are exported as plain text `@userId` in markdown
3. **State Management**: Use the `useMentions` hook for programmatic access

## The plugin will:

1. **Register the custom MentionNode** with Lexical
2. **Auto-transform `@userId` patterns** to interactive mention components
3. **Handle markdown import/export** properly
4. **Allow dynamic configuration** through plugin parameters
5. **Provide hooks and utilities** for programmatic mention management

## Usage Pattern

```typescript
const mentionsPluginInstance = mentionsPlugin({
  users: yourUsersList,
  mentionComponent: YourCustomMentionComponent,
  onMentionClick: (user) => console.log('Clicked:', user),
  onMentionAdd: (user) => console.log('Added:', user),
  onMentionRemove: (userId) => console.log('Removed:', userId),
  autoTransform: true, // Enable `@userId` auto-conversion
});
```
