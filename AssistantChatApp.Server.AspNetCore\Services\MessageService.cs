using AssistantChatApp.Server.AspNetCore.Data;
using AssistantChatApp.Server.AspNetCore.DTOs;
using AssistantChatApp.Server.AspNetCore.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using AssistantChatApp.Server.AspNetCore.Services.Helpers;

namespace AssistantChatApp.Server.AspNetCore.Services
{
    public class MessageService
    {
        private readonly ApplicationDbContext _context;

        public MessageService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// Check whether user is a participant in the chat
        private async Task EnsureUserIsChatParticipant(string chatId, string userId)
        {
            var isParticipant = await _context.ChatParticipants
                .AnyAsync(cp => cp.ChatId == chatId && cp.UserId == userId);

            if (!isParticipant)
            {
                throw new UnauthorizedAccessException($"User \"{userId}\" is not a participant in chat \"{chatId}\".");
            }
        }

        private async Task<PaginatedResponseDto<T>> MapToPaginatedMessagesAsync<T>(
            IQueryable<Message> query,
            Func<Message, T> mapper,
            int page,
            int limit
        )
        {
            var total = await query.CountAsync();
            var messages = await query
                .OrderBy(m => m.CreatedAt)
                .Skip((page - 1) * limit)
                .Take(limit)
                .ToListAsync();

            return new PaginatedResponseDto<T>
            {
                Data = messages.Select(mapper).ToList(),
                Total = total,
                Page = page,
                Limit = limit
            };
        }


        private async Task<IQueryable<Message>> GetAndFilterChatMessagesAsync(
            string chatId,
            string userId,
            Func<IQueryable<Message>, IQueryable<Message>> filter
        )
        {
            await EnsureUserIsChatParticipant(chatId, userId);

            var query = _context.Messages
                .Include(m => m.Author)
                .Include(m => m.Mentions)
                .ThenInclude(mm => mm.User)
                .Include(m => m.Tags)
                .Include(m => m.Contents)
                .Where(m => m.ChatId == chatId);

            return filter(query);
        }

        public async Task<PaginatedResponseDto<MessageDto>> GetMessagesAsync(
            string chatId,
            string userId,
            int page = 1,
            int limit = 50,
            IEnumerable<string>? exclusionTagIds = null
        )
        {
            IQueryable<Message> Filter(IQueryable<Message> query) =>
                exclusionTagIds == null
                ? query
                : query.Where(m => !m.Tags.Any(tag => exclusionTagIds.Contains(tag.Id)));
            var query = await GetAndFilterChatMessagesAsync(chatId, userId, Filter);
            return await MapToPaginatedMessagesAsync(
                query, MessageHelpers.MapMessageToDto, page, limit
            );
        }

        public async Task<PaginatedResponseDto<MessageForAiDto>> GetMessagesForAIAsync(
            string chatId,
            string userId,
            int page = 1,
            int limit = 50
        )
        {
            static IQueryable<Message> Filter(IQueryable<Message> query) => query;
            var query = await GetAndFilterChatMessagesAsync(chatId, userId, Filter);
            return await MapToPaginatedMessagesAsync(
                query, MessageHelpers.MapMessageToMessageForAiDto, page, limit
            );
        }


        public async Task<MessageDto?> GetMessageByIdAsync(string chatId, string messageId, string userId)
        {
            await EnsureUserIsChatParticipant(chatId, userId);

            var message = await _context.Messages
                .Include(m => m.Author)
                .Include(m => m.Mentions)
                .ThenInclude(mm => mm.User)
                .Include(m => m.Tags)
                .Include(m => m.Contents)
                .FirstOrDefaultAsync(m => m.Id == messageId && m.ChatId == chatId);

            return message != null ? MessageHelpers.MapMessageToDto(message) : null;
        }

        public async Task<List<MessageDto>> GetMessagesByIdsAsync(string chatId, string userId, List<string> messageIds)
        {
            await EnsureUserIsChatParticipant(chatId, userId);

            var messages = await _context.Messages
                .Include(m => m.Author)
                .Include(m => m.Mentions)
                .ThenInclude(mm => mm.User)
                .Include(m => m.Tags)
                .Include(m => m.Contents)
                .Where(m => m.ChatId == chatId && messageIds.Contains(m.Id))
                .ToListAsync();

            return messages.Select(MessageHelpers.MapMessageToDto).ToList();
        }


        private async Task<EntityEntry<Message>> AddMessageWithoutSaveAsync(string chatId, string userId, CreateMessageDto createMessageDto)
        {
            await EnsureUserIsChatParticipant(chatId, userId);

            var message = new Message
            {
                ChatId = chatId,
                AuthorId = userId,
                Content = createMessageDto.TextContent,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Add other content items
            if (createMessageDto.OtherContents != null)
            {
                foreach (var contentItem in createMessageDto.OtherContents)
                {
                    switch (contentItem)
                    {
                        case FunctionCallContentDto callDto:
                            message.Contents.Add(new FunctionCallContent
                            {
                                CallId = callDto.CallId,
                                PluginName = callDto.PluginName,
                                FunctionName = callDto.FunctionName,
                                Arguments = callDto.Arguments,
                                Message = message,
                                MessageId = message.Id
                            });
                            break;
                        case FunctionResultContentDto resultDto:
                            message.Contents.Add(new FunctionResultContent
                            {
                                CallId = resultDto.CallId,
                                Result = resultDto.Result,
                                Message = message,
                                MessageId = message.Id
                            });
                            break;
                        default:
                            throw new ArgumentOutOfRangeException(
                                paramName: nameof(contentItem),
                                message: "Unknown content item type"
                            );
                    }
                }
            }

            // Add mentions
            if (createMessageDto.Mentions != null)
            {
                foreach (var mentionedUserId in createMessageDto.Mentions)
                {
                    // Check if the mentioned user exists and is a participant in the chat
                    var isMentionedUserParticipant = await _context.ChatParticipants
                        .AnyAsync(cp => cp.ChatId == chatId && cp.UserId == mentionedUserId);

                    if (isMentionedUserParticipant)
                    {
                        message.Mentions.Add(new MessageMention { UserId = mentionedUserId });
                    }
                }
            }

            // Add tags
            if (createMessageDto.Tags != null)
            {
                foreach (var tag in createMessageDto.Tags)
                {
                    var existingTag = await _context.Tags.FirstOrDefaultAsync(t => t.Id == tag.Id);
                    if (existingTag == null)
                    {
                        existingTag = new Tag { Id = tag.Id, Name = tag.Name };
                        _context.Tags.Add(existingTag);
                    }
                    message.Tags.Add(existingTag);
                }
            }

            return _context.Messages.Add(message);
        }

        /// Update the chat's UpdatedAt timestamp
        public async ValueTask<Chat?> UpdateChatUpdatedAtAsync(string chatId)
        {
            var chat = await _context.Chats.FindAsync(chatId);
            if (chat != null)
            {
                chat.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
            return chat;
        }

        public async Task<MessageDto> CreateMessageAsync(string chatId, string userId, CreateMessageDto createMessageDto)
        {
            var messageEntity = await AddMessageWithoutSaveAsync(chatId, userId, createMessageDto);
            await UpdateChatUpdatedAtAsync(chatId);

            await _context.SaveChangesAsync();

            return await GetMessageByIdAsync(chatId, messageEntity.Entity.Id, userId) ?? throw new InvalidOperationException("Failed to retrieve created message");
        }

        public async Task<MessageDto?> UpdateMessageAsync(string chatId, string messageId, string userId, UpdateMessageDto updateMessageDto)
        {
            // TODO: Include Tags and Contents update
            var message = await _context.Messages
                .Include(m => m.Mentions)
                .FirstOrDefaultAsync(m => m.Id == messageId && m.ChatId == chatId && m.AuthorId == userId);

            if (message == null)
            {
                return null;
            }

            message.Content = updateMessageDto.Content;
            message.UpdatedAt = DateTime.UtcNow;

            // Update mentions
            if (updateMessageDto.Mentions != null)
            {
                // Remove all existing mentions
                message.Mentions.Clear();

                // Add new mentions
                foreach (var mentionedUserId in updateMessageDto.Mentions)
                {
                    // Check if the mentioned user exists and is a participant in the chat
                    var isMentionedUserParticipant = await _context.ChatParticipants
                        .AnyAsync(cp => cp.ChatId == chatId && cp.UserId == mentionedUserId);

                    if (isMentionedUserParticipant)
                    {
                        message.Mentions.Add(new MessageMention { UserId = mentionedUserId });
                    }
                }
            }

            await _context.SaveChangesAsync();

            return await GetMessageByIdAsync(chatId, message.Id, userId);
        }

        public async Task<bool> DeleteMessageAsync(string chatId, string messageId, string userId)
        {
            var message = await _context.Messages
                .FirstOrDefaultAsync(m => m.Id == messageId && m.ChatId == chatId && m.AuthorId == userId);

            if (message == null)
            {
                return false;
            }

            _context.Messages.Remove(message);
            await _context.SaveChangesAsync();

            return true;
        }
    }
}
