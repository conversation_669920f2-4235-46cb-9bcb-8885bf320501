using System;
using System.Collections.Generic;

namespace AssistantChatApp.Server.AspNetCore.Models
{
    public class Message
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();

        // Chat relationship
        public string ChatId { get; set; } = string.Empty;
        public virtual Chat Chat { get; set; } = null!;

        // Author relationship
        public string AuthorId { get; set; } = string.Empty;
        public virtual User Author { get; set; } = null!;
        public string Content { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<MessageMention> Mentions { get; set; } = [];
        public virtual ICollection<MessageTag> MessageTags { get; set; } = [];
        public virtual ICollection<Tag> Tags { get; set; } = [];
        public ICollection<Content> Contents { get; set; } = [];
    }
}
