import { queryOptions, useMutation, QueryClient } from "@tanstack/react-query";
import type { SearchQuery, SearchResult } from "../../api";
import { kernelMemoryClient } from "../../api";

const queryKeys = {
  search: (queryId: string) => ["search", queryId],
} as const;

type QueryKeys = typeof queryKeys;

type Queries = {
  search: {
    key: QueryKeys["search"],
    data: SearchResult
  }
}

const searchQueryOptions =


const useSearchMutation = () => {
  const queryClient = new QueryClient();

  return useMutation({
    mutationFn: async (query: SearchQuery) => {
      return await kernelMemoryClient.searchDocumentSnippets(query);
    },
    onSuccess: (response, query) => {
      queryClient.setQueryData(["search", query], response.data.results);
    },
  });
};