{"AiAgentSettings": {"BaseUrl": "http://localhost:5174"}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=AssistantChatApp;Username=********;Password=********"}, "JwtSettings": {"Secret": "YourSuperSecretKeyHereMakeItLongEnoughForSecurity", "Issuer": "Assistant<PERSON><PERSON><PERSON><PERSON>", "Audience": "AssistantChatAppClient", "ExpiryInDays": 7}, "AllowedHosts": "*", "AllowedOrigins": "http://localhost:3000,http://localhost:5173,http://localhost:5105", "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Debug", "Serilog.Sinks.OpenTelemetry"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore.Hosting": "Warning", "Microsoft.AspNetCore.Mvc": "Warning", "Microsoft.AspNetCore.Routing": "Warning"}}, "Enrich": ["FromLogContext"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {RequestId} {SourceContext} {Message}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/log-.log", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {RequestId} {SourceContext} {Message}{NewLine}{Exception}"}}, {"Name": "Debug", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {RequestId} {SourceContext} {Message}{NewLine}{Exception}"}}, {"Name": "OpenTelemetry"}]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}}