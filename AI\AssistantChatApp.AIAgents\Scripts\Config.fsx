#load "Env.fsx"

module OpenRouter =

    let [<Literal>] ApiEndpoint = "https://openrouter.ai/api/v1"
    let [<Literal>] HttpClientName = "OpenRouterHttpClient"

    let apiKey = Env.env["OpenRouterApiKey"]

    module Models =

        let [<Literal>] ``Llama-3.3-8B-Free`` = "meta-llama/llama-3.3-8b-instruct:free"
        let [<Literal>] ``DeepHermes-3-Mistral-24B-Free`` = "nousresearch/deephermes-3-mistral-24b-preview:free"
        let [<Literal>] ``Qwen3-14B-Free`` = "qwen/qwen3-14b:free"
        let [<Literal>] ``Mistral-Small-3.1-24B-Free`` = "mistralai/mistral-small-3.1-24b-instruct:free"
        let [<Literal>] ``Meta-Llama-Scout-Free`` = "meta-llama/llama-4-scout:free"
        let [<Literal>] ``Meta-Llama-3.3-70B-Free`` = "meta-llama/llama-3.3-70b-instruct:free"


module TogetherAI =

    let [<Literal>] ApiEndpoint = "https://api.together.xyz/v1/"
    let [<Literal>] HttpClientName = "TogetherAIHttpClient"

    let apiKey = Env.env["TogetherAIApiKey"]

    module Models =
        let [<Literal>] ``Llama3.3_Free`` = "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free"
        let [<Literal>] LlamaVisionFree = "meta-llama/Llama-Vision-Free"
        let [<Literal>] DeepseekR1DistillLlama = "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free"
        let [<Literal>] ``Llama3.2-3B-Instruct-Turbo`` = "meta-llama/Llama-3.2-3B-Instruct-Turbo"
