using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AssistantChatApp.Server.AspNetCore.DTOs;
using AssistantChatApp.Server.AspNetCore.Services;

namespace AssistantChatApp.Server.AspNetCore.Controllers;

[ApiController]
[Route("api/ai")]
[Authorize]
public class AIController(
    MessageService messageService,
    AIAssistantService aiAssistantService,
    UserService userService
) : ControllerBase()
{
    private IActionResult InvalidUserError() =>
            Unauthorized(new ErrorResponseDto { Message = "Invalid token" });

    private Task<string?> TryGetUserIdAsync() =>
        Task.FromResult(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value);

    [HttpPost("chat-completion/recent")]
    public async Task<IActionResult> GetChatCompletion([FromBody] AIChatCompletionRequestDto requestDto)
    {
        var inquirerId = await TryGetUserIdAsync();
        if (string.IsNullOrEmpty(inquirerId))
        {
            return InvalidUserError();
        }
        var inquirer = await userService.GetUserByIdAsync(inquirerId);
        if (inquirer == null)
        {
            return NotFound(new ErrorResponseDto { Message = "Inquirer user not found" });
        }

        var aiUser = await userService.GetUserByIdAsync(requestDto.AIUserId);

        if (aiUser == null)
        {
            return NotFound(new ErrorResponseDto { Message = "AI user not found" });
        }

        if (!aiUser.IsAI)
        {
            return BadRequest(new ErrorResponseDto { Message = "Asked user is not an AI" });
        }

        var messages =
            await messageService.GetMessagesForAIAsync(requestDto.ChatId, inquirerId);

        if (messages == null)
        {
            return NotFound(new ErrorResponseDto { Message = "Messages not found" });
        }

        var responseMessages = await aiAssistantService.AskAsync(
            requestDto.ChatId,
            inquirer,
            aiUser
        );
        var messageIds = responseMessages.Select(m => m.Id);
        return CreatedAtAction(
            nameof(MessagesController.GetMessagesByIds),
            "Messages",
            new { chatId = requestDto.ChatId, messageIds },
            new AIChatCompletionResponseDto(responseMessages)
        );
    }
}
