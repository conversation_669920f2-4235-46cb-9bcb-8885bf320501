﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <VersionPrefix>1.0.0</VersionPrefix>
    </PropertyGroup>

    <ItemGroup>
        <Compile Include="ImportTypes.fs" />
        <Compile Include="ImportCE.fs" />
        <Compile Include="ImportJson.fs" />
        <Compile Include="Import.fs" />
    </ItemGroup>

    <ItemGroup>
        <Content Include="appsettings.example.json" CopyToOutputDirectory="PreserveNewest" />
        <Content Include="documentImports.example.json" />
    </ItemGroup>

    <ItemGroup>
        <None Include="pack_for_notebook.cmd" />
        <None Include="Notebooks\**\*.fsx" />
        <None Include="Notebooks\**\*.dib" />
        <None Include="Notebooks\**\*.ipynb" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="FSharp.SystemTextJson" Version="1.4.36" />
        <PackageReference Include="Microsoft.KernelMemory.WebClient" Version="0.98.250508.3" />
        <PackageReference Include="Thoth.Json.System.Text.Json" Version="0.2.1" />
    </ItemGroup>
    
</Project>
