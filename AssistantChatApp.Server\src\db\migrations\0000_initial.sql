-- Initial migration for the Assistant Cha<PERSON> App database

-- Create users table
CREATE TABLE IF NOT EXISTS "users" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "email" VARCHAR(255) NOT NULL UNIQUE,
  "password" VARCHAR(255) NOT NULL,
  "display_name" VARCHAR(100) NOT NULL,
  "avatar" VARCHAR(255),
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "is_ai" BOOLEAN NOT NULL DEFAULT FALSE
);

-- Create chats table
CREATE TABLE IF NOT EXISTS "chats" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "title" VARCHAR(100) NOT NULL,
  "description" VARCHAR(500),
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create messages table
CREATE TABLE IF NOT EXISTS "messages" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "chat_id" UUID NOT NULL REFERENCES "chats"("id") ON DELETE CASCADE,
  "author_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "content" TEXT NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create chat_participants table (many-to-many)
CREATE TABLE IF NOT EXISTS "chat_participants" (
  "chat_id" UUID NOT NULL REFERENCES "chats"("id") ON DELETE CASCADE,
  "user_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "joined_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  PRIMARY KEY ("chat_id", "user_id")
);

-- Create tags table
CREATE TABLE IF NOT EXISTS "tags" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" VARCHAR(50) NOT NULL UNIQUE
);

-- Create chat_tags table (many-to-many)
CREATE TABLE IF NOT EXISTS "chat_tags" (
  "chat_id" UUID NOT NULL REFERENCES "chats"("id") ON DELETE CASCADE,
  "tag_id" UUID NOT NULL REFERENCES "tags"("id") ON DELETE CASCADE,
  PRIMARY KEY ("chat_id", "tag_id")
);

-- Create mentions table (many-to-many)
CREATE TABLE IF NOT EXISTS "mentions" (
  "message_id" UUID NOT NULL REFERENCES "messages"("id") ON DELETE CASCADE,
  "user_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  PRIMARY KEY ("message_id", "user_id")
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_messages_chat_id" ON "messages"("chat_id");
CREATE INDEX IF NOT EXISTS "idx_messages_author_id" ON "messages"("author_id");
CREATE INDEX IF NOT EXISTS "idx_chat_participants_chat_id" ON "chat_participants"("chat_id");
CREATE INDEX IF NOT EXISTS "idx_chat_participants_user_id" ON "chat_participants"("user_id");
CREATE INDEX IF NOT EXISTS "idx_chat_tags_chat_id" ON "chat_tags"("chat_id");
CREATE INDEX IF NOT EXISTS "idx_chat_tags_tag_id" ON "chat_tags"("tag_id");
CREATE INDEX IF NOT EXISTS "idx_mentions_message_id" ON "mentions"("message_id");
CREATE INDEX IF NOT EXISTS "idx_mentions_user_id" ON "mentions"("user_id");
