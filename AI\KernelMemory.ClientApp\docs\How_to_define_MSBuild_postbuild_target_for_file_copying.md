# How to define an MSBuild post-build target that copies files to the project subfolder

## Demo Example

```xml
<ItemGroup>
    <!-- Explicitly define the specific files you want to copy -->
    <SourceAsScript Include="DocumentImportConfig.fs" />
    <SourceAsScript Include="Import.fs" />
    <!-- Add more files as needed -->
</ItemGroup>
<Target Name="CopyFSharpSourcesToScripts" AfterTargets="Build">
<Copy
    SourceFiles="@(SourceAsScript)"
    DestinationFiles="@(SourceAsScript->'$(ProjectDir)Scripts\%(Filename).fsx')"
    OverwriteReadOnlyFiles="true"
    SkipUnchangedFiles="true"
    Condition="'@(SourceAsScript)' != ''"
/>
</Target>
```

Key attributes explained:

* `SourceFiles`: Specifies the input files to copy
* `DestinationFolder`: Where to copy files (if no specific destination specified)
* `DestinationFiles`: Allows custom destination path/name for each file
* `OverwriteReadOnlyFiles`: Determines if read-only files can be overwritten
* `Condition`: Optional boolean check before executing the copy
* `SkipUnchangedFiles`: Avoids unnecessary file operations
* `UseHardlinksIfPossible`: Can optimize file operations on supporting systems

The transform `@(SourceAsScript->'$(ProjectDir)Scripts\%(Filename).fsx` is particularly interesting:

* It maps each input file to a new destination
* Replaces the original extension with `.fsx`
* Preserves the original filename
* the `->` in MSBuild is a special transformation operator called the "item transformation" syntax. It allows you to apply a transformation to each item in an item group.
  * Examples:
    ```
    <!-- Simple extension change -->
    @(SourceFiles->'%(Filename).backup')

    <!-- Path transformation -->
    @(SourceFiles->'$(OutputPath)\%(Filename)%(Extension)')

    <!-- Multiple transformations -->
    @(SourceFiles->'%(Filename).bak', '%(Filename).temp')
    ```

Variables and metadata expressions used:

* The `$(ProjectDir)` variable in MSBuild represents the directory where the project file (.csproj, .vbproj, etc.) is located. The value of `$(ProjectDir)` **does include a path separator at the end**. This means that if your project is located at `C:\Projects\MyProject`, the value of `$(ProjectDir)` would be `C:\Projects\MyProject\`.
* `%(Filename)` and `%(Extension)` are metadata extraction expression that extracts the filename and extension respectively from the source file path.

Additional remarks:
* When you reference an item group with `@(ItemGroupName)`, it returns a string representation of the items. If no items are defined, this returns an empty string.
* The condition `Condition="'@(SourceAsScript)' != ''"` prevents the `Copy` task from running if the item group is empty.
