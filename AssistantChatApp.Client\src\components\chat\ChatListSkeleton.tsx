import React from 'react';
import { Box, Card, Skeleton } from '@mui/material';

interface ChatListSkeletonProps {
  count?: number;
}

const ChatListSkeleton: React.FC<ChatListSkeletonProps> = ({ count = 3 }) => {
  return (
    <>
      {Array.from(new Array(count)).map((_, index) => (
        <Card key={index} variant="outlined" sx={{ mb: 2, p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
            <Skeleton variant="circular" width={48} height={48} sx={{ mr: 2 }} />
            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Skeleton variant="text" width="40%" height={32} />
                <Skeleton variant="text" width="20%" height={24} />
              </Box>
              <Skeleton variant="text" width="80%" />
              <Skeleton variant="text" width="70%" />
              
              <Box sx={{ display: 'flex', mt: 2, gap: 1 }}>
                <Skeleton variant="rectangular" width={60} height={24} sx={{ borderRadius: 4 }} />
                <Skeleton variant="rectangular" width={60} height={24} sx={{ borderRadius: 4 }} />
                <Skeleton variant="rectangular" width={60} height={24} sx={{ borderRadius: 4 }} />
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <Skeleton variant="text" width={80} height={24} />
                <Box sx={{ display: 'flex', ml: 2 }}>
                  <Skeleton variant="circular" width={24} height={24} />
                  <Skeleton variant="circular" width={24} height={24} sx={{ ml: -0.5 }} />
                  <Skeleton variant="circular" width={24} height={24} sx={{ ml: -0.5 }} />
                </Box>
              </Box>
            </Box>
          </Box>
        </Card>
      ))}
    </>
  );
};

export default ChatListSkeleton;