import React from 'react';
import {
  Button,
} from '@mui/material';
import { MessageSquarePlus } from 'lucide-react';
import { useNavigate } from '@tanstack/react-router';
import { useCreateChat } from '../../mutations/chat';
import { ChatFormDialog } from "./ChatForms";
import { useChatFormState } from "./hooks";
import { useTranslation } from 'react-i18next';

const NewChatButton: React.FC = () => {
  const { t } = useTranslation('chats');
  const formState = useChatFormState();
  const navigate = useNavigate();
  const [isDialogOpened, setDialogOpened] = React.useState(false);

  const createChatMutation = useCreateChat((newChat) => {
    navigate({ to: '/chats/$chatId', params: { chatId: newChat.id } });
    handleClose();
  });

  const openDialog = () => {
    setDialogOpened(true);
  };

  const handleClose = () => {
    formState.reset();
    setDialogOpened(false);
  };

  const handleCreateChat = () => {
    const { title, description, participants, tags } = formState.formValues;
    if (!title.trim()) return;
    createChatMutation.mutate({
      title,
      description,
      tags,
      participants: participants.map((p) => p.id),
    });
  };

  return (
    <>
      <Button
        variant="contained"
        startIcon={<MessageSquarePlus size={20} />}
        onClick={openDialog}
      >
        {t("new_chat")}
      </Button>

      <ChatFormDialog
        dialogTitle={t("create_new_chat")}
        isOpened={isDialogOpened}
        formState={formState}
        isSubmitting={createChatMutation.isPending}
        onSubmit={handleCreateChat}
        onClose={handleClose}
      />
    </>
  );
};

export default NewChatButton;
