import React, { useCallback, useEffect, useMemo } from "react";
import { use<PERSON>tomValue, useSet<PERSON><PERSON> } from "jotai";
import { useMediaQuery } from "@mui/material";
import {
  artifactViewerAtomFamily,
  multiModalStateAtom,
  activeViewersAtom,
  focusedViewerAtom,
  initializeViewerAtom,
  openViewerAtom,
  closeViewerAtom,
  focusViewerAtom,
  updateViewerStateAtom,
  toggleCollapseAtom,
  toggleFullscreen<PERSON>tom,
} from "./atoms";
import {
  ArtifactViewerState,
  Position,
  Size,
  ResponsiveConfig,
  DEFAULT_RESPONSIVE_CONFIG,
} from "./types";
import {
  getViewportSize,
  getResponsiveSize,
  getResponsivePosition,
  constrainPosition,
  constrainSize,
  debounce,
} from "./utils";

/**
 * Hook for managing responsive breakpoints
 */
export const useResponsiveBreakpoints = (
  config: ResponsiveConfig = DEFAULT_RESPONSIVE_CONFIG
) => {
  const isMobileView = useMediaQuery(`(max-width:${config.mobile}px)`);
  const isTabletView = useMediaQuery(
    `(min-width:${config.mobile}px) and (max-width:${config.desktop}px)`
  );
  const isDesktopView = useMediaQuery(`(min-width:${config.desktop}px)`);

  const deviceType = useMemo(() => {
    if (isMobileView) return "mobile";
    if (isTabletView) return "tablet";
    return "desktop";
  }, [isMobileView, isTabletView]);

  return {
    isMobile: isMobileView,
    isTablet: isTabletView,
    isDesktop: isDesktopView,
    deviceType,
    breakpoints: config,
  };
};

/**
 * Hook for managing viewport size changes
 */
export const useViewportSize = () => {
  const [viewportSize, setViewportSize] = React.useState(getViewportSize);

  useEffect(() => {
    const handleResize = debounce(() => {
      setViewportSize(getViewportSize());
    }, 100);

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return viewportSize;
};

/**
 * Main hook for managing an individual artifact viewer
 */
export const useArtifactViewer = (
  artifactId: string,
  initialTitle?: React.ReactNode,
  initialDescription?: string,
  initialPosition?: Position,
  initialSize?: Size
) => {
  const viewerState = useAtomValue(artifactViewerAtomFamily(artifactId));
  const initializeViewer = useSetAtom(initializeViewerAtom);
  const openViewer = useSetAtom(openViewerAtom);
  const closeViewer = useSetAtom(closeViewerAtom);
  const focusViewer = useSetAtom(focusViewerAtom);
  const updateViewerState = useSetAtom(updateViewerStateAtom);
  const toggleCollapse = useSetAtom(toggleCollapseAtom);
  const toggleFullscreen = useSetAtom(toggleFullscreenAtom);

  const { deviceType } = useResponsiveBreakpoints();
  const viewportSize = useViewportSize();

  // Initialize the viewer if it doesn't exist
  useEffect(() => {
    if (!viewerState && initialTitle) {
      initializeViewer({
        artifactId,
        title: initialTitle,
        description: initialDescription,
        initialPosition,
        initialSize,
      });
    }
  }, [
    artifactId,
    initialTitle,
    initialDescription,
    initialPosition,
    initialSize,
    viewerState,
    initializeViewer,
  ]);

  // Handle responsive adjustments
  useEffect(() => {
    if (viewerState && viewerState.isOpen && !viewerState.isFullscreen) {
      const responsiveSize = getResponsiveSize(viewerState.size);
      const responsivePosition = getResponsivePosition(
        viewerState.position,
        responsiveSize
      );

      if (
        responsiveSize.width !== viewerState.size.width ||
        responsiveSize.height !== viewerState.size.height ||
        responsivePosition.x !== viewerState.position.x ||
        responsivePosition.y !== viewerState.position.y
      ) {
        updateViewerState(artifactId, {
          size: responsiveSize,
          position: responsivePosition,
        });
      }
    }
  }, [deviceType, viewportSize, viewerState, artifactId, updateViewerState]);

  const open = useCallback(() => {
    openViewer(artifactId);
  }, [artifactId, openViewer]);

  const close = useCallback(() => {
    closeViewer(artifactId);
  }, [artifactId, closeViewer]);

  const focus = useCallback(() => {
    focusViewer(artifactId);
  }, [artifactId, focusViewer]);

  const toggle = useCallback(() => {
    if (viewerState?.isOpen) {
      close();
    } else {
      open();
    }
  }, [viewerState?.isOpen, open, close]);

  const collapse = useCallback(() => {
    toggleCollapse(artifactId);
  }, [artifactId, toggleCollapse]);

  const fullscreen = useCallback(() => {
    toggleFullscreen(artifactId);
  }, [artifactId, toggleFullscreen]);

  const updateState = useCallback(
    (updates: Partial<ArtifactViewerState>) => {
      updateViewerState(artifactId, updates);
    },
    [artifactId, updateViewerState]
  );

  const updatePosition = useCallback(
    (position: Position) => {
      const constrainedPosition = constrainPosition(
        position,
        viewerState?.size || { width: 600, height: 450 }
      );
      updateState({ position: constrainedPosition });
    },
    [updateState, viewerState?.size]
  );

  const updateSize = useCallback(
    (size: Size) => {
      const constrainedSize = constrainSize(size);
      updateState({ size: constrainedSize });
    },
    [updateState]
  );

  return {
    // State
    state: viewerState,
    isOpen: viewerState?.isOpen || false,
    isCollapsed: viewerState?.isCollapsed || false,
    isFullscreen: viewerState?.isFullscreen || false,
    isFocused: viewerState?.isFocused || false,
    position: viewerState?.position,
    size: viewerState?.size,
    zIndex: viewerState?.zIndex,

    // Actions
    open,
    close,
    toggle,
    focus,
    collapse,
    fullscreen,
    updateState,
    updatePosition,
    updateSize,

    // Responsive info
    deviceType,
    viewportSize,
  };
};

/**
 * Hook for managing multiple artifact viewers
 */
export const useArtifactViewerManager = () => {
  const multiModalState = useAtomValue(multiModalStateAtom);
  const activeViewers = useAtomValue(activeViewersAtom);
  const focusedViewer = useAtomValue(focusedViewerAtom);
  const closeViewer = useSetAtom(closeViewerAtom);
  const focusViewer = useSetAtom(focusViewerAtom);

  const closeAll = useCallback(() => {
    activeViewers.forEach((viewer) => {
      closeViewer(viewer.id);
    });
  }, [activeViewers, closeViewer]);

  const focusNext = useCallback(() => {
    if (activeViewers.length > 1) {
      const currentIndex = focusedViewer
        ? activeViewers.findIndex((v) => v.id === focusedViewer.id)
        : -1;
      const nextIndex = (currentIndex + 1) % activeViewers.length;
      focusViewer(activeViewers[nextIndex].id);
    }
  }, [activeViewers, focusedViewer, focusViewer]);

  const focusPrevious = useCallback(() => {
    if (activeViewers.length > 1) {
      const currentIndex = focusedViewer
        ? activeViewers.findIndex((v) => v.id === focusedViewer.id)
        : -1;
      const prevIndex =
        currentIndex <= 0 ? activeViewers.length - 1 : currentIndex - 1;
      focusViewer(activeViewers[prevIndex].id);
    }
  }, [activeViewers, focusedViewer, focusViewer]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle shortcuts when there are active viewers
      if (activeViewers.length === 0) return;

      // Escape key - close focused viewer or all viewers
      if (event.key === "Escape") {
        if (focusedViewer) {
          closeViewer(focusedViewer.id);
        } else if (activeViewers.length > 0) {
          closeViewer(activeViewers[0].id);
        }
        return;
      }

      // Tab key - cycle through viewers
      if (event.key === "Tab" && event.altKey) {
        event.preventDefault();
        if (event.shiftKey) {
          focusPrevious();
        } else {
          focusNext();
        }
        return;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [activeViewers, focusedViewer, closeViewer, focusNext, focusPrevious]);

  return {
    // State
    multiModalState,
    activeViewers,
    focusedViewer,
    activeCount: activeViewers.length,

    // Actions
    closeAll,
    focusNext,
    focusPrevious,
  };
};
