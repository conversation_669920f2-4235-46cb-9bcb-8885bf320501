# Task Description

Draggable and resizable modal window for showing various interactive components embedded in chat messages (aka "message artifacts"). Examples of such components: forms, polls, data tables (DataGrid), etc. The idea is to show the component separately from the message and chat to not clutter the chat message list with big and complex content. Instead, a special button-like element should be shown in the place where the component appears in the message. And when the user clicks on it, the modal window should be shown.
The modal window should support the following features:
- **Dragging and resizing**: to move the modal window to a desired location and resize it to a desired size.
- **Expand-collapse**: when collapsed, only the title and a button to expand should be shown. When expanded, the full content of the component should be shown.
- **Close button**: to close the modal window.
- **Fullscreen mode**: to switch between the normal and fullscreen modes.
- **Responsive design**: the modal window should look and work well on both desktop and mobile devices.
- **Multiple modal windows handling**: this is optional feature. I.e., in a base setup consists of a single modal window. But it should be possible to support multiple modal windows by providing a set of corresponding props (such as "z-index", "is focused", etc.)

## Technical Requirements

### Structure

- The described functionality must form a self-contained, encapsulated and reusable module ("module" here means a set of TypeScript files). I.e., all its dependencies (types, functions, React hooks, React components) are defined within its scope or explicitly imported.

### Tech Stack Involved

- Framework: React
- Language: TypeScript
- UI kit: Material UI (MUI)
- State management: React Hooks + Jotai (`jotai`)
- Draggable and resizable components: `react-rnd`

## Final instructions

Destination folder: `AssistantChatApp.Client/src/components/message/artifactViewer`
Before writing any code, show me a plan and a structure of your implementation (e.g., what types, hooks, components, etc. you are going to define, with their signatures and relations between them).

---

# Follow-up Questions

<Question1>

I have Artifact Viewer components module implemented. Now I need to bring a proper backend support of that feature. The goal is to be able to enrich current text-only messages with additional content that can be rendered in the UI via special components (the content should be marked in a certain way so that the UI logic could distinguish which component to select for rendering the content). So, the questions are:
- How such content should be represented in messages written by LLM?
- How to represent the content in a type for AI chat message (currently, it's defined in `AI\AssistantChatApp.AIAgents.Shared\Types.fs`)? Extract it as a separate part (like it's done with Function Calls and Function Results, or keep it inside unstructured text content)?
- How to represent the content in a message entity to be stored in the chat app DB (currently, it's defined in `AssistantChatApp.DataStore\Models\Message.cs`)? Extract it as a separate entity (like it's done with Function Calls and Function Results), or keep it inside Message's `Content` text property?

Current hypothesis: I've already prototyped similar concept of embedding interactive component definitions inside messages via Markdown directives. Here is such Markdown message content example:

```md
This is a message with a survey embedded in it. Please, select an answer:

:::survey
{
  "question": "Yes or no?",
  "answers": {
    "yes": "✅ Yes!",
    "no": "🟥 No."
  },
  "multiple": false,
  "showKeys": true
}
:::

Looking forward to your reply!
```

Is it a good idea to leverage this approach for the Artifact Viewer feature as well? Or you'd suggest something else?

</Question1>

<Answer1>

Based on your existing architecture and the Artifact Viewer implementation, I recommend **leveraging and extending your Markdown directive approach**. Here's why and how:

## Why Markdown Directives Are Ideal

Your current approach with `:::survey` directives is excellent because:
- **Consistency**: Aligns with your existing interactive components system
- **LLM-friendly**: Easy for AI models to generate structured content
- **Backward compatibility**: Graceful degradation when components aren't available
- **Extensibility**: Simple to add new artifact types

## Recommended Architecture

### 1. LLM Message Representation
Extend your directive syntax for artifacts:

```md
Here's the sales data analysis you requested:

:::artifact
{
  "type": "data-table",
  "title": "Q4 Sales Report",
  "description": "Interactive sales data with filtering",
  "data": {
    "columns": ["Product", "Sales", "Growth"],
    "rows": [["Product A", 15000, "12%"], ["Product B", 8500, "-3%"]]
  }
}
:::

The table shows significant growth in Product A.
```

### 2. AI Chat Message Types (F#)
Extend your existing pattern - keep artifacts **within text content** but add parsing support:

````fsharp path=AI/AssistantChatApp.AIAgents.Shared/Types.fs
// Add artifact-related types
type ArtifactType =
    | Survey
    | DataTable
    | Form
    | Chart
    | Custom of string

type ArtifactContent = {
    Type: ArtifactType
    Title: string option
    Description: string option
    Data: JsonValue
}

// Add parsing utilities
module MessageParsing =
    let parseArtifacts (content: string) : ArtifactContent list =
        // Parse :::artifact directives from markdown content
        // Implementation details...
````

### 3. Database Storage
Follow your existing `Content` entity pattern - add `ArtifactContent` as a new content type:

````csharp path=AssistantChatApp.DataStore/Models/Message.cs
public class ArtifactContent : Content
{
    public string ArtifactType { get; set; } = string.Empty;
    public string? Title { get; set; }
    public string? Description { get; set; }
    public string DataJson { get; set; } = string.Empty;

    // Position within the text content for proper rendering order
    public int Position { get; set; }
}
````

### 4. Frontend Integration
Extend your existing interactive components system:

````typescript path=AssistantChatApp.Client/src/components/message/interactiveComponents.ts
const getMessageComponentRegistrations = (): StringOutputComponentRegistrations => ({
  survey: { /* existing */ },
  artifact: {
    component: ArtifactTriggerComponent,
    initialData: { isOpen: false },
    actionDataMappers: {
      open: (action) => `[Artifact opened: ${action.payload.title}]`,
      close: (action) => undefined,
    },
  },
});
````

## Benefits of This Approach

1. **Unified System**: Leverages your existing directive parsing and component registry
2. **Flexible Storage**: Can store both inline (small artifacts) and separate entities (large artifacts)
3. **LLM Integration**: AI models can easily generate structured artifact content
4. **Progressive Enhancement**: Works with existing markdown rendering, enhances with interactive components

## Implementation Strategy

1. **Phase 1**: Extend directive parsing to handle `:::artifact` blocks
2. **Phase 2**: Add `ArtifactContent` entity and migration
3. **Phase 3**: Create `ArtifactTriggerComponent` that integrates with your Artifact Viewer
4. **Phase 4**: Update AI agents to generate artifact directives

This approach maintains consistency with your existing architecture while providing the flexibility needed for rich interactive content.

</Answer1 request-id="82676fa7-4955-42ed-8fba-95af06ca2ab0">

---

<Question2>

Understood, thanks. Now, to receive properly formatted message from an LLM, I need a special system prompt that will instruct how to embed directive-based interactive component definitions into a message. Current syntax is pretty straightforward: it's a block directive with a JSON body. The JSON is 1-to-1 mapping of the props of the component to be embedded. For example, for the `Survey` component mentioned earlier, the props to be defined in the directive's JSON are:

```typescript
interface SurveyComponentProps {
  question: string;
  answers: Record<string, string>;
  multiple: boolean;
  showKeys: boolean;
}
```

As I see it, the prompt should contain the following:
1. A list of available component names/identifiers (e.g., "survey", "data-table", "form", "chart", etc.), with a description of each component purpose, and a specification of the props it accepts
2. Probably, some examples of components usages
3. An instruction on how to embed a component into a message (i.e., using a block directive with a JSON body)
4. Instructions on when to embed a component, and what to response can be expected from the user. I.e., some components could be interactive, and from the business logic perspective imply certain user actions (e.g., selecting an answer in a survey, filling a form, etc.). Ideally, such business rules should be strict, and maybe imposed on the app-logic side. But for now, it's okay to leave it on the LLM side by providing corresponding instructions on the component usage.

So, please, provide me with such prompt template. By "template" I mean a text prompt that I can later customize for a specific use case by plugging in a list of available components, their props, and other necessary details.


</Question2>

<Answer2 request-id="7325c322-4cac-4992-87be-c04a44eb6b6a">

Here's a comprehensive system prompt template for LLM interactive component generation:

````markdown path=prompts/system/interactive-components-template.md mode=EDIT
# Interactive Components Usage Instructions

You can enhance your messages with interactive components that users can interact with directly in the chat interface. These components are embedded using block directives with JSON configuration.

## Syntax

Use the following syntax to embed a component:

```
:::component-name
{
  "prop1": "value1",
  "prop2": "value2"
}
:::
```

**Important Rules:**
- Component directives must be on separate lines
- JSON must be valid and properly formatted
- All required props must be provided
- Use double quotes for JSON strings

## Available Components

{COMPONENTS_LIST}

## Usage Guidelines

### When to Use Components
- **Data Collection**: Use surveys, forms when you need structured user input
- **Data Presentation**: Use tables, charts for displaying structured data
- **User Interaction**: Use components when simple text response isn't sufficient
- **Enhanced UX**: When interactive elements improve user experience

### User Interaction Expectations
- **Interactive Components**: Users will interact directly with the component (clicking, selecting, typing)
- **Response Handling**: User actions will be automatically processed and may generate follow-up messages
- **State Management**: Component state is managed automatically - don't ask users to "remember" selections

### Best Practices
1. **Context**: Always provide context before embedding a component
2. **Instructions**: Give clear instructions on how to use the component
3. **Follow-up**: Explain what happens after user interaction
4. **Fallback**: Ensure your message makes sense even if the component fails to render

## Examples

{COMPONENT_EXAMPLES}

## Error Handling
- If a component fails to render, users will see the raw directive
- Always ensure your message is readable without the interactive component
- Test component JSON syntax before using in production

Remember: Interactive components enhance the conversation but should never be the sole means of communication. Always provide clear text context and instructions.
````

Here's how you would customize this template:

````markdown
# Example Customization

Replace `{COMPONENTS_LIST}` with:

### survey
**Purpose**: Collect user feedback through multiple choice questions
**Props**:
- `question` (string, required): The question to ask
- `answers` (Record<string, string>, required): Answer options as key-value pairs
- `multiple` (boolean, required): Allow multiple selections
- `showKeys` (boolean, required): Display answer keys to users

**User Interaction**: Users select answers and submit. Their selection is automatically sent as a follow-up message.

Replace `{COMPONENT_EXAMPLES}` with:

### Survey Example
```
I'd like to understand your preferences better:

:::survey
{
  "question": "Which features are most important to you?",
  "answers": {
    "speed": "⚡ Fast performance",
    "ui": "🎨 Beautiful interface",
    "features": "🔧 Rich functionality"
  },
  "multiple": true,
  "showKeys": false
}
:::

Based on your selection, I'll provide more targeted recommendations.
```
````

This template provides:
1. **Clear syntax rules** with examples
2. **Placeholder sections** for component specifications
3. **Usage guidelines** for when and how to use components
4. **Best practices** for user experience
5. **Error handling** considerations
6. **Extensible structure** for adding new components

You can customize it by replacing the placeholder sections with your specific component definitions and examples.

</Answer2>

---

<Question3>

The implemented `ArtifactViewer` component is close to being good (from what I've been able to test it so far). But I´ve noticed some issues. The major one: the `ArtifactViewer` isn't rendered as a modal window (i.e., outside of the parent element), but rather as a nested element. This leads to several issues:
- The component is bounded by the parent element. So, the viewer's coordinates are calculated relative to the parent's position rather than the viewport's. As a result, for example, the viewer's vertical position relative to the viewport cannot be moved above the parent's position in the viewport.
- The component's z-index is not high enough to be shown on top of other elements in the viewport.

In general, it's good to have such version as well (i.e., a nested, non-modal element). But to be used as I've planned, there should be a modal version as well (i.e., rendered outside of the parent element, as a root-level HTML element).

If it would help, in the official MUI docs there's an example of a draggable `Dialog` (it uses `react-draggable` directly instead of `react-rnd`, but it doesn't matter, I suppose). Here is the example code:

```ts
import React, { useRef, useState } from 'react';
import { Dialog, DialogTitle, DialogContent, Paper, type PaperProps } from '@mui/material';
import Draggable from 'react-draggable';

function DraggablePaper(props: PaperProps) {
  const nodeRef = useRef<HTMLDivElement>(null);
  return (
    <Draggable
      nodeRef={nodeRef}
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} ref={nodeRef} />
    </Draggable>
  );
}

export default function DraggableDialog() {
  const [open, setOpen] = useState(false);

  return (
    <>
      <button onClick={() => setOpen(true)}>Open Draggable Dialog</button>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        PaperComponent={DraggablePaper}
        aria-labelledby="draggable-dialog-title"
      >
        <DialogTitle
          style={{ cursor: 'move' }}
          id="draggable-dialog-title"
        >
          Draggable Dialog
        </DialogTitle>
        <DialogContent>
          Drag this dialog by its title bar
        </DialogContent>
      </Dialog>
    </>
  );
}
```

Besides, there are some other issues not related to the "modality" of the viewer:
- The `SizeSwitcher` menu appears below the viewer rather than on top of it. I suppose, its z-index should be configured properly.
- The "Collapse" action doesn't work as I've expected. Instead of collapsing the whole window, it collapses only the inner content, leaving the root Viewer element size unchanged (so it looks like a window of the same size with a header and an empty content).

Please, elaborate on the above issues. Before introducing any changes to the existing code, explain what you're planning to do to address the issues.

</Question3>

---

<Answer3 request-id="5a8b122a-de10-406b-87c6-a4f374b63145">

You've identified some critical issues with the current implementation. Let me analyze each problem and explain my planned solutions:

## Issue Analysis and Solutions

### 1. **Major Issue: Non-Modal Rendering**

**Problem**: The current `ArtifactViewer` renders as a nested element within its parent, causing:
- Positioning relative to parent instead of viewport
- Z-index conflicts with other page elements
- Movement constraints within parent boundaries

**Root Cause**: The current implementation uses `react-rnd` directly without a portal mechanism to render outside the DOM hierarchy.

**Planned Solution**: Create a hybrid approach with two rendering modes:

#### Option A: Portal-based Modal (Primary)
```typescript
// New modal version using React Portal
const ArtifactViewerModal: React.FC<ArtifactViewerProps & { modal?: boolean }> = ({
  modal = true,
  ...props
}) => {
  if (modal) {
    return createPortal(
      <ArtifactViewerCore {...props} />,
      document.body // Render at root level
    );
  }
  return <ArtifactViewerCore {...props} />;
};
```

#### Option B: MUI Dialog Integration (Alternative)
```typescript
// Using MUI Dialog as container with custom PaperComponent
const ArtifactViewerDialog: React.FC<ArtifactViewerProps> = (props) => {
  return (
    <Dialog
      open={props.isOpen}
      onClose={props.onClose}
      PaperComponent={DraggableResizablePaper}
      maxWidth={false}
      sx={{
        '& .MuiDialog-paper': {
          margin: 0,
          maxHeight: 'none',
          maxWidth: 'none',
        }
      }}
    >
      <ArtifactViewerContent {...props} />
    </Dialog>
  );
};
```

**Recommendation**: I'll implement **Option A** as the primary solution because:
- More flexible than MUI Dialog constraints
- Better integration with `react-rnd`
- Maintains our existing component structure
- Allows for both modal and non-modal modes

### 2. **SizeSwitcher Z-Index Issue**

**Problem**: The dropdown menu appears behind the viewer instead of on top.

**Root Cause**: The menu's z-index is not properly configured relative to the modal's z-index.

**Planned Solution**:
```typescript
// In SizeSwitcher component
<Menu
  anchorEl={anchorEl}
  open={open}
  onClose={handleCloseMenu}
  sx={{
    '& .MuiPaper-root': {
      zIndex: (theme) => theme.zIndex.modal + 100, // Higher than modal
    }
  }}
  // ... other props
/>
```

### 3. **Collapse Behavior Issue**

**Problem**: Collapse only hides content but doesn't resize the modal window itself.

**Root Cause**: The collapse animation only affects the content area, not the `react-rnd` container size.

**Planned Solution**: Implement true window collapse by:

```typescript
// Update the Rnd size when collapsed
const getEffectiveSize = (state: ArtifactViewerState) => {
  if (state.isCollapsed) {
    return {
      width: state.size.width,
      height: HEADER_HEIGHT, // Only header height when collapsed
    };
  }
  return state.size;
};

// In ArtifactViewer component
<Rnd
  size={getEffectiveSize(state)}
  // ... other props
/>
```

## Implementation Plan

### Phase 1: Modal Rendering Fix
1. **Create Portal-based Wrapper**:
   ```typescript
   // New component: ArtifactViewerPortal
   const ArtifactViewerPortal: React.FC<ArtifactViewerProps & {
     renderMode?: 'modal' | 'inline'
   }> = ({ renderMode = 'modal', ...props }) => {
     const content = <ArtifactViewerCore {...props} />;

     if (renderMode === 'modal') {
       return createPortal(content, document.body);
     }
     return content;
   };
   ```

2. **Update Z-Index Management**:
   ```typescript
   // Ensure modal-level z-index
   const getModalZIndex = (baseZIndex: number) => {
     return Math.max(baseZIndex, 1300); // MUI modal level
   };
   ```

3. **Update Positioning Logic**:
   ```typescript
   // Ensure viewport-relative positioning
   const getViewportPosition = (position: Position) => {
     return {
       x: Math.max(0, position.x),
       y: Math.max(0, position.y),
     };
   };
   ```

### Phase 2: SizeSwitcher Z-Index Fix
1. **Update Menu Z-Index**:
   ```typescript
   // In SizeSwitcher component
   const menuZIndex = useMemo(() => {
     return (viewerZIndex || BASE_Z_INDEX) + 50;
   }, [viewerZIndex]);
   ```

2. **Pass Z-Index Context**:
   ```typescript
   // Update ArtifactViewerHeader to pass z-index to SizeSwitcher
   <SizeSwitcher
     currentSize={state.size}
     availableSizes={availableSizes}
     onSizeChange={handleSizeChange}
     parentZIndex={state.zIndex} // New prop
   />
   ```

### Phase 3: Collapse Behavior Fix
1. **Implement True Window Collapse**:
   ```typescript
   // Store expanded size when collapsing
   const handleCollapse = useCallback(() => {
     if (!state.isCollapsed) {
       // Store current size before collapsing
       updateState({
         isCollapsed: true,
         expandedSize: state.size, // Store for restoration
       });
     } else {
       // Restore previous size when expanding
       updateState({
         isCollapsed: false,
         size: state.expandedSize || state.size,
       });
     }
   }, [state, updateState]);
   ```

2. **Update Size Calculation**:
   ```typescript
   // Dynamic size based on collapse state
   const effectiveSize = useMemo(() => {
     if (state.isCollapsed) {
       return {
         width: state.size.width,
         height: COLLAPSED_HEIGHT, // ~48px for header only
       };
     }
     return state.size;
   }, [state.isCollapsed, state.size]);
   ```

### Phase 4: Enhanced Type System
1. **Update Props Interface**:
   ```typescript
   interface ArtifactViewerProps {
     // ... existing props
     renderMode?: 'modal' | 'inline';
     portal?: boolean; // Alternative API
     container?: HTMLElement; // Custom portal container
   }
   ```

2. **Update State Interface**:
   ```typescript
   interface ArtifactViewerState {
     // ... existing fields
     expandedSize?: Size; // Store size when collapsed
     renderMode: 'modal' | 'inline';
   }
   ```

## Backward Compatibility

To maintain backward compatibility:

1. **Default to Modal Mode**:
   ```typescript
   // Default behavior will be modal
   <ArtifactViewer {...props} /> // Renders as modal by default
   ```

2. **Explicit Inline Mode**:
   ```typescript
   // Opt-in to inline rendering
   <ArtifactViewer {...props} renderMode="inline" />
   ```

3. **Gradual Migration**:
   ```typescript
   // Support both old and new APIs temporarily
   const ArtifactViewer = (props) => {
     const mode = props.renderMode || (props.modal !== false ? 'modal' : 'inline');
     return <ArtifactViewerPortal {...props} renderMode={mode} />;
   };
   ```

## Testing Strategy

1. **Modal Rendering**: Verify positioning relative to viewport, not parent
2. **Z-Index Hierarchy**: Test with multiple modals and dropdowns
3. **Collapse Animation**: Ensure smooth size transitions
4. **Cross-Browser**: Test portal rendering across browsers
5. **Mobile Responsiveness**: Verify modal behavior on mobile devices

This approach will solve all the identified issues while maintaining the existing API and adding the flexibility for both modal and inline rendering modes. Would you like me to proceed with implementing these changes?

</Answer3>
