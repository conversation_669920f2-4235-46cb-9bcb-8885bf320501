{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Debug", "Serilog.Sinks.OpenTelemetry"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore.Hosting": "Warning", "Microsoft.AspNetCore.Mvc": "Warning", "Microsoft.AspNetCore.Routing": "Warning"}}, "Enrich": ["FromLogContext"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {RequestId} {SourceContext} {Message}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/log-.log", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {RequestId} {SourceContext} {Message}{NewLine}{Exception}"}}, {"Name": "Debug", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {RequestId} {SourceContext} {Message}{NewLine}{Exception}"}}, {"Name": "OpenTelemetry"}]}, "AllowedHosts": "*", "AllowedOrigins": "http://localhost:3000,http://localhost:5173"}