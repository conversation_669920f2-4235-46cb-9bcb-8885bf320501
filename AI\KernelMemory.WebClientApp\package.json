{"name": "kernelmemory-webclientapp", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"fable-watch-ts": "dotnet fable watch ./src/fable -s --lang typescript -e .ts -o ./src/fable/ts --fableLib @fable-org/fable-library-ts", "fable-build-dev-ts": "dotnet fable ./src/fable -s --lang typescript -e .ts -o ./src/fable/ts --fableLib @fable-org/fable-library-ts", "fable-build-ts": "dotnet fable ./src/fable -s --lang typescript -e .ts -o ./src/fable/ts --fableLib @fable-org/fable-library-ts --noCache", "dev": "concurrently \"pnpm run fable-watch-ts\" \"pnpm run vite-dev\"", "build": "pnpm run fable-build-ts && pnpm run vite-build"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fable-org/fable-library-ts": "^1.9.0", "@fontsource/roboto": "^5.1.1", "@mui/icons-material": "^6.3.1", "axios": "^1.11.0", "@mui/lab": "6.0.1-beta.35", "@mui/material": "^6.3.1", "@mui/x-date-pickers": "^7.23.3", "@tanstack/react-query": "^5.83.0", "@tanstack/react-router": "^1.130.1", "@tanstack/zod-adapter": "^1.122.0", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "material-react-table": "^3.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.0", "zod": "^3.25.67"}, "devDependencies": {"@tanstack/react-router-devtools": "^1.125.4", "@tanstack/router-plugin": "^1.125.5", "@types/node": "^24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "typescript": "^5.8.3", "vite": "^6"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}