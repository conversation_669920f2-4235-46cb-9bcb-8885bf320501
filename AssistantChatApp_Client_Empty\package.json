{"name": "Assistant<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^7.0.2", "@mui/lab": "7.0.0-beta.11", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "^8.1.0", "@tanstack/react-query": "^5.74.4", "@tanstack/react-router": "^1.117.0", "@tanstack/zod-adapter": "^1.117.0", "react": "^19.1.0", "react-dom": "^19.1.0", "zod": "^3.24.3"}, "devDependencies": {"@tanstack/router-devtools": "^1.117.0", "@tanstack/router-plugin": "^1.117.0", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "typescript": "^5.8.3", "vite": "^6.3.3"}}