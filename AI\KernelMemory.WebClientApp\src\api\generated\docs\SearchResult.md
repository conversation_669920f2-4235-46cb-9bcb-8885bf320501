# SearchResult


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**query** | **string** |  | [optional] [default to undefined]
**noResult** | **boolean** |  | [optional] [readonly] [default to undefined]
**results** | [**Array&lt;Citation&gt;**](Citation.md) |  | [optional] [default to undefined]

## Example

```typescript
import { SearchResult } from './api';

const instance: SearchResult = {
    query,
    noResult,
    results,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
