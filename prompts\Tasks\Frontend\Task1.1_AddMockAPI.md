# Additional task: Mock API

## Instruction

Please, write and setup a simulated backend module for this frontend. I.e., write a client-side module that acts like a backend for the API defined, but handles everything in-memory. It should allow using core interactivity features (creating and deleting chats, sending messages, and so on), although some data could be hardcoded and mocked (such as authentication-related). When writing this backend simulation code, change the existing app code as less as possible (basically, you should change only the part that sets up the connection to a backend, so that this backend simulation code would work).

---

## Task Plan

Create a simulated backend module that runs entirely on the client-side, allowing you to test the frontend's core functionality without a real backend. This will involve modifying the `apiClient` to use in-memory data and mocking the API endpoints.

### Steps

1. Create `src/api/mockApi.ts`: This file will contain the mock API implementation. It will include:
    - In-memory data stores for users, chats, and messages.
    -Mock implementations of the API functions (`login`, `getMe`, `getChats`, `getChat`, `createChat`, `updateChat`, `deleteChat`, `getMessages`, `sendMessage`, `updateMessage`, `deleteMessage`, `getUsers`, `getUser`, `getAIAssistants`).
    - Logic to simulate API behavior, such as generating IDs, filtering data, and handling pagination.
    - A function to seed the in-memory data with some initial values.
2. Modify `src/api/apiClient.ts`: Add a conditional check to use either the real API client or the mock API based on an environment variable (e.g., `VITE_USE_MOCK_API`).

3. Update API calls: No changes are needed, as the `apiClient` will handle the switch between the real and mock APIs.

4. Update src/App.tsx: Set the `VITE_USE_MOCK_API` environment variable to true to use the mock API.
