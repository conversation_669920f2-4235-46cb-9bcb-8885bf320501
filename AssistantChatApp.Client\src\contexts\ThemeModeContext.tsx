import React, {
  createContext,
  useState,
  useMemo,
  useContext,
  ReactNode,
} from "react";
import { type Theme, ThemeProvider, createTheme } from "@mui/material";

// Define the theme modes
export type ThemeMode = "light" | "dark" | "system";

// Context type
interface ThemeModeContextType {
  mode: ThemeMode;
  setMode: (mode: ThemeMode) => void;
}

// Create the context
const ThemeModeContext = createContext<ThemeModeContextType | undefined>(
  undefined,
);

// Theme mode provider component
interface ThemeModeProviderProps {
  baseTheme?: Theme,
  children: ReactNode;
}

export const ThemeModeProvider: React.FC<ThemeModeProviderProps> = ({
  baseTheme,
  children,
}) => {
  const [mode, setMode] = useState<ThemeMode>("system");

  // Determine the actual theme mode
  const computedMode = useMemo(() => {
    if (mode === "system") {
      return window.matchMedia("(prefers-color-scheme: dark)").matches
        ? "dark"
        : "light";
    }
    return mode;
  }, [mode]);

  // Create theme based on mode
  const theme = useMemo(
    () =>
      createTheme({
        ...baseTheme,
        palette: {
          mode: computedMode,
        },
      }),
    [baseTheme, computedMode],
  );

  // Value for context provider
  const contextValue = useMemo(
    () => ({
      mode,
      setMode,
    }),
    [mode],
  );

  return (
    <ThemeModeContext.Provider value={contextValue}>
      <ThemeProvider theme={theme}>{children}</ThemeProvider>
    </ThemeModeContext.Provider>
  );
};

// Custom hook to use theme mode context
export const useThemeMode = () => {
  const context = useContext(ThemeModeContext);
  if (context === undefined) {
    throw new Error("useThemeMode must be used within a ThemeModeProvider");
  }
  return context;
};
