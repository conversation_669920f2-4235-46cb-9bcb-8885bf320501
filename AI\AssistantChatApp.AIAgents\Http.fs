module AssistantChatApp.AIAgents.Http

open System
open Microsoft.Extensions.Logging
open Microsoft.Extensions.Http
open Microsoft.Extensions.Http.Logging
open Microsoft.Extensions.Configuration
open Microsoft.Extensions.DependencyInjection
open Serilog.HttpClient
open Serilog.HttpClient.Extensions

module ClientNames =

    let [<Literal>] TogetherAIOpenAIClient = "TogetherAIOpenAIClient"
    let [<Literal>] OpenRouterOpenAIClient = "OpenRouterOpenAIClient"


type HttpClientLoggingOptions() =
    member val LogMode = LogMode.LogAll with get, set
    member val RequestHeaderLogMode = LogMode.LogAll with get, set
    member val RequestBodyLogMode = LogMode.LogAll with get, set
    member val ResponseHeaderLogMode = LogMode.LogAll with get, set
    member val ResponseBodyLogMode = LogMode.LogAll with get, set
    member val RequestBodyLogTextLengthLimit = 5000 with get, set
    member val ResponseBodyLogTextLengthLimit = 5000 with get, set
    member val MaskFormat = "*****" with get, set
    member val MaskedProperties = ResizeArray<string>() with get, set


module RequestLoggingOptions =

    let setDefaults (p: RequestLoggingOptions) =
        // Log all requests and responses by default
        p.LogMode <- LogMode.LogAll
        p.RequestHeaderLogMode <- LogMode.LogAll
        p.RequestBodyLogMode <- LogMode.LogAll
        p.ResponseHeaderLogMode <- LogMode.LogAll
        p.ResponseBodyLogMode <- LogMode.LogAll

        // Truncate large bodies to prevent performance penalties and log bloat
        p.RequestBodyLogTextLengthLimit <- 5000 // Log up to 5000 characters of the request body
        p.ResponseBodyLogTextLengthLimit <- 5000 // Log up to 5000 characters of the response body

        // Configure sensitive data masking
        p.MaskFormat <- "*****" // The replacement string for masked properties
        p.MaskedProperties.Clear() // Clear default masked properties if any
        p.MaskedProperties.Add("*password*") // Mask properties containing "password"
        p.MaskedProperties.Add("*token*")    // Mask properties containing "token"
        p.MaskedProperties.Add("*api_key*")  // Mask API keys
        p.MaskedProperties.Add("*secret*")   // Mask secrets

/// Registers Options Configuration for detailed `HttpClient` request logging
/// via `Serilog.HttpClient` logging handler.
let configureHttpClientSerilogLoggingOptions
    (config: IConfiguration)
    (services: IServiceCollection)
    =
    let configSection = config.GetSection("Serilog:HttpClientLogging")
    services.Configure<HttpClientLoggingOptions>(configSection)


let configureHttpClient (name: string) (config: IConfiguration) (services: IServiceCollection) =
    services
        .AddHttpClient(name)
        .RemoveAllLoggers()
        .LogRequestResponse(fun o ->
            let configOptions =
                let configOptions = HttpClientLoggingOptions()
                config.GetSection("Serilog:HttpClientLogging")
                    .Bind(configOptions)
                configOptions
            // Example: Log all requests and responses by default
            o.LogMode <- configOptions.LogMode
            o.RequestHeaderLogMode <- configOptions.RequestHeaderLogMode
            o.RequestBodyLogMode <- configOptions.RequestBodyLogMode
            o.ResponseHeaderLogMode <- configOptions.ResponseHeaderLogMode
            o.ResponseBodyLogMode <- configOptions.ResponseBodyLogMode

            // Truncate large bodies to prevent performance penalties and log bloat
            o.RequestBodyLogTextLengthLimit <- configOptions.RequestBodyLogTextLengthLimit // Log up to 5000 characters of the request body
            o.ResponseBodyLogTextLengthLimit <- configOptions.ResponseBodyLogTextLengthLimit // Log up to 5000 characters of the response body

            // Configure sensitive data masking
            o.MaskFormat <- configOptions.MaskFormat // The replacement string for masked properties
            o.MaskedProperties.Clear()
            for prop in configOptions.MaskedProperties do
                o.MaskedProperties.Add(prop)
        )
    |> fun _ -> services
