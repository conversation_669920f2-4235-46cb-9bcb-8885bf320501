using Microsoft.KernelMemory;
using Microsoft.KernelMemory.Service.AspNetCore;
using KernelMemory.WebService;

var builder = WebApplication.CreateBuilder(args);

// Read KM settings, needed before building the app.
KernelMemoryConfig config =
    builder.Configuration.GetSection("KernelMemory").Get<KernelMemoryConfig>()
        ?? throw new ConfigurationException("Unable to load configuration");

builder.ConfigureSwagger(config);

builder.AddServiceDefaults();
builder.AddKernelMemory(km =>
{
    km.ConfigureDependencies(builder.Configuration);
});

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

if (config.Service.RunWebService && config.Service.OpenApiEnabled)
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.MapDefaultEndpoints();

if (config.Service.RunWebService)
{
    app.AddKernelMemoryEndpoints();
}

app.Run();
