# IndexCollection


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**results** | [**Array&lt;IndexDetails&gt;**](IndexDetails.md) |  | [optional] [default to undefined]

## Example

```typescript
import { IndexCollection } from './api';

const instance: IndexCollection = {
    results,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
