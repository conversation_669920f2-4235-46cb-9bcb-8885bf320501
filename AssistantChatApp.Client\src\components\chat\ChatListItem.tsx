import React from 'react';
import { Avatar, Box, <PERSON>ton, Card, CardActionArea, CardContent, CardHeader, Chip, IconButton, Stack, Typography } from '@mui/material';
import { MoreVertical as MoreIcon } from "lucide-react";
import { useNavigate } from '@tanstack/react-router';
import { Chat } from '../../types';
import { useTranslation } from 'react-i18next';
import { i18nLocaleToDateFnsLocale, formatAsYesterdayTodayOrDateTimeUpToMinutes } from "../common/dateTimeUtils";
import { ChatMenu } from "./ChatMenu";

interface ChatListItemProps {
  chat: Chat;
}

const useFormatDate = () => {
  const { t, i18n } = useTranslation(["common"]);
  const dateFnsLocale = i18nLocaleToDateFnsLocale(i18n.language);

  return React.useCallback((dateString: string) => {
    const date = new Date(dateString);
    return formatAsYesterdayTodayOrDateTimeUpToMinutes(date, dateFnsLocale, t);
  }, [t, dateFnsLocale]);
};

export const ChatListItemOld: React.FC<ChatListItemProps> = ({ chat }) => {
  const { t } = useTranslation(["chats", "common"]);
  const navigate = useNavigate();
  const formatDate = useFormatDate();
  const [menuAnchorEl, setMenuAnchorEl] = React.useState<null | HTMLElement>(null);
  const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setMenuAnchorEl(null);
  };

  const handleClick = () => {
    navigate({ to: '/chats/$chatId', params: { chatId: chat.id } });
  };

  return (
    <Card
      variant="outlined"
      sx={{
        mb: 1,
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        },
      }}
    >
      <CardActionArea onClick={handleClick} sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
          <Avatar
            alt={chat.participants[0]?.displayName || t('user', { ns: 'chats' })}
            src={chat.participants[0]?.avatar}
            sx={{ width: 48, height: 48, mr: 2 }}
          />
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
              <Typography variant="h6" component="h2" noWrap sx={{ fontWeight: 'medium' }}>
                {chat.title}
              </Typography>
              <Stack direction="row" spacing={0.5} alignItems="center">
                <Typography variant="caption" color="text.secondary">
                  {formatDate(chat.updatedAt)}
                </Typography>
                <IconButton onClick={handleOpenMenu}>
                  <MoreIcon size={20} />
                </IconButton>
              </Stack>
            </Box>

            {chat.lastMessage && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  mb: 1,
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  height: '40px',
                }}
              >
                <Typography component="span" variant="body2" fontWeight="medium" color="text.primary">
                  {chat.lastMessage.author.displayName}:
                </Typography>{' '}
                {chat.lastMessage.content}
              </Typography>
            )}

            {chat.tags && chat.tags.length > 0 && (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                {chat.tags.slice(0, 3).map((tag) => (
                  <Chip
                    key={tag}
                    label={tag}
                    size="small"
                    sx={{ height: 24 }}
                  />
                ))}
                {chat.tags.length > 3 && (
                  <Chip
                    label={`+${chat.tags.length - 3}`}
                    size="small"
                    variant="outlined"
                    sx={{ height: 24 }}
                  />
                )}
              </Box>
            )}

            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1.5 }}>
              <Typography variant="body2" color="text.secondary">
                {t('chats:participant_count', { count: chat.participants.length })}
              </Typography>
              <Box sx={{ display: 'flex', ml: 2 }}>
                {chat.participants.slice(0, 3).map((participant, i) => (
                  <Avatar
                    key={participant.id}
                    alt={participant.displayName}
                    src={participant.avatar}
                    sx={{
                      width: 24,
                      height: 24,
                      ml: i > 0 ? -0.75 : 0,
                      border: '1px solid white',
                    }}
                  />
                ))}
                {chat.participants.length > 3 && (
                  <Avatar
                    sx={{
                      width: 24,
                      height: 24,
                      ml: -0.75,
                      border: '1px solid white',
                      fontSize: '0.75rem',
                      bgcolor: 'primary.light',
                    }}
                  >
                    +{chat.participants.length - 3}
                  </Avatar>
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      </CardActionArea>
      <ChatMenu
        chat={chat}
        menuAnchorEl={menuAnchorEl}
        onClose={handleCloseMenu}
      />
    </Card>
  );
};

export const ChatListItem: React.FC<ChatListItemProps> = ({ chat }) => {
  const { t } = useTranslation(["chats", "common"]);
  const navigate = useNavigate();
  const formatDate = useFormatDate();
  const [menuAnchorEl, setMenuAnchorEl] = React.useState<null | HTMLElement>(null);
  const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setMenuAnchorEl(null);
  };

  const handleClick = () => {
    navigate({ to: '/chats/$chatId', params: { chatId: chat.id } });
  };
  return (
    <Card
      className="chat-list-item"
      variant="outlined"
      sx={{
        mb: 1,
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        },
      }}
    >
      <CardHeader
        avatar={
          <Avatar
            alt={chat.participants[0]?.displayName || t('user', { ns: 'chats' })}
            src={chat.participants[0]?.avatar}
            sx={{ width: 48, height: 48, mr: 2 }}
          />
        }
        action={
          <IconButton onClick={handleOpenMenu}>
            <MoreIcon size={20} />
            </IconButton>
        }
        title={chat.title}
        subheader={formatDate(chat.updatedAt)}
      />
      <CardContent
        sx={{
          display: 'flex',
          flexDirection: 'column',
          // gap: 1,
        }}
      >
        {chat.lastMessage && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mb: 1,
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            <Typography component="span" variant="body2" fontWeight="medium" color="text.primary">
              {chat.lastMessage.author.displayName}:
            </Typography>{' '}
            {chat.lastMessage.content}
          </Typography>
        )}

        {chat.tags && chat.tags.length > 0 && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
            {chat.tags.slice(0, 3).map((tag) => (
              <Chip
                key={tag}
                label={tag}
                size="small"
                sx={{ height: 24 }}
              />
            ))}
            {chat.tags.length > 3 && (
              <Chip
                label={`+${chat.tags.length - 3}`}
                size="small"
                variant="outlined"
                sx={{ height: 24 }}
              />
            )}
          </Box>
        )}
        <Stack direction="row" justifyContent="space-between">
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1.5 }}>
            <Typography variant="body2" color="text.secondary">
              {t('chats:participant_count', { count: chat.participants.length })}
            </Typography>
            <Box sx={{ display: 'flex', ml: 2 }}>
              {chat.participants.slice(0, 3).map((participant, i) => (
                <Avatar
                  key={participant.id}
                  alt={participant.displayName}
                  src={participant.avatar}
                  sx={{
                    width: 24,
                    height: 24,
                    ml: i > 0 ? -0.75 : 0,
                    border: '1px solid white',
                  }}
                />
              ))}
              {chat.participants.length > 3 && (
                <Avatar
                  sx={{
                    width: 24,
                    height: 24,
                    ml: -0.75,
                    border: '1px solid white',
                    fontSize: '0.75rem',
                    bgcolor: 'primary.light',
                  }}
                >
                  +{chat.participants.length - 3}
                </Avatar>
              )}
            </Box>
          </Box>
          <Button
            variant="contained"
            color="primary"
            onClick={handleClick}
            sx={{
              opacity: 0,
              transition: 'opacity 0.2s',
              '&:hover': { opacity: 1 },
              ".chat-list-item:hover &": { opacity: 1 }
            }}
          >
            {t("chats:open_chat")}
          </Button>
        </Stack>
        <ChatMenu
          chat={chat}
          menuAnchorEl={menuAnchorEl}
          onClose={handleCloseMenu}
        />
      </CardContent>
    </Card>
  );
}
