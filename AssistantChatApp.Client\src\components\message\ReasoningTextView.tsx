import React, { useState } from "react";
import { Box, Collapse, Typography, IconButton, Paper } from "@mui/material";
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from "@mui/icons-material";

// interface ReasoningTextViewProps {
//   reasoningText: string
// }

interface ReasoningContainerProps {
  children: React.ReactNode;
}

export const ReasoningContainer: React.FC<ReasoningContainerProps> = ({
  children,
}) => {
  const [expanded, setExpanded] = useState(false);

  const handleToggleExpand = () => {
    setExpanded(!expanded);
  };

  // Early return if no reasoning text
  if (!children) return null;

  return (
    <Paper
      elevation={0}
      sx={{
        mt: 1,
        mb: 1,
      }}
    >
      <IconButton
        onClick={handleToggleExpand}
        size="small"
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-start",
          width: "100%",
          padding: 0,
        }}
      >
        <Typography variant="caption" color="text.secondary">
          Поток мыслей
        </Typography>
        {expanded ? (
          <ExpandLessIcon fontSize="small" />
        ) : (
          <ExpandMoreIcon fontSize="small" />
        )}
      </IconButton>

      <Collapse in={expanded}>
        <Box
          sx={(t) => ({
            color: t.palette.text.secondary,
            fontSize: 0.75 * t.typography.fontSize,
          })}
        >
          {children}
        </Box>
      </Collapse>
    </Paper>
  );
};
