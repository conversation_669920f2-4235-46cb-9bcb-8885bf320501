import React from "react";
import { create } from "zustand";
import { Map } from "immutable";
import {
  ComponentName,
  ActionHandlerId,
  ComponentAction,
  ComponentRegistration,
} from "./types";

export interface ComponentRegistryData {
  components: Map<ComponentName, ComponentRegistration>;
  actionHandlers: Map<ActionHandlerId, (action: ComponentAction) => void>;
}

export interface ComponentRegistryCommands {
  register<
    TComponentName extends string = ComponentName,
    TComponentActionName extends string = string,
    TProps extends object = object,
    TData = unknown
  >(
    name: TComponentName,
    registration: ComponentRegistration<
      TComponentName,
      TComponentActionName,
      TProps,
      TData
    >
  ): void;

  setActionHandler<
    TComponentName extends string = ComponentName,
    TComponentActionName extends string = string,
    THandler<PERSON><PERSON> extends string = string,
    TData = unknown
  >(
    id: TH<PERSON><PERSON><PERSON><PERSON>,
    handler: (action: ComponentAction<TComponentName, TComponentActionName, TData>) => void
  ): void;

  /** Process action by applying a data processing function defined in the component registration */
  applyAction<
    TComponentName extends string = ComponentName,
    TComponentActionName extends string = string,
    TData = unknown,
    TActionOutputData = unknown
  >(
    action: ComponentAction<TComponentName, TComponentActionName, TData>
  ): TActionOutputData | undefined;
}

export interface ComponentRegistrySelectors {
  getComponentRegistration<
    TComponentName extends string = ComponentName,
    TComponentActionName extends string = string,
    TProps extends object = object,
    TData = unknown
  >(
    name: TComponentName
  ): ComponentRegistration<TComponentName, TComponentActionName, TProps, TData>;
}

export interface ComponentRegistry
  extends ComponentRegistryCommands,
    ComponentRegistryData {}

export const useComponentRegistryStore = create<ComponentRegistry>(
  (set, get) => ({
    components: Map<ComponentName, ComponentRegistration>(),
    actionHandlers: Map<ActionHandlerId, (action: ComponentAction) => void>(),

    register<
      TComponentName extends string = ComponentName,
      TComponentActionName extends string = string,
      TProps extends object = object,
      TData = unknown
    >(
      name: TComponentName,
      registration: ComponentRegistration<
        TComponentName,
        TComponentActionName,
        TProps,
        TData
      >
    ): void {
      set(({ components }) => {
        console.debug("Registering component", name, registration);
        console.debug("Previously registered components", components);
        return {
          components: components.set(
            name,
            registration as unknown as ComponentRegistration
          ),
        };
      });
    },

    setActionHandler<
      TComponentName extends string = ComponentName,
      TComponentActionName extends string = string,
      THandlerKey extends string = string,
      TData = unknown
    >(
      id: THandlerKey,
      handler: (action: ComponentAction<TComponentName, TComponentActionName, TData>) => void
    ): void {
      set(({ actionHandlers }) => {
        return {
          actionHandlers: actionHandlers.set(id, handler as (action: ComponentAction) => void),
        }
      });
    },

    //TODO: Get rid of this function or replace ith with the logic implemented currently inside `RegisteredComponent`. Because for now the function is not used.
    applyAction<
      TComponentName extends string = ComponentName,
      TComponentActionName extends string = string,
      TData = unknown,
      TActionOutputData = unknown
    >(
      action: ComponentAction<TComponentName, TComponentActionName, TData>
    ): TActionOutputData | undefined {
      const { components } = get();
      const registration = components.get(action.componentName);
      const actionDataMappers = registration?.actionDataMappers;
      if (actionDataMappers) {
        const mapActionData = actionDataMappers[action.type];
        const result = mapActionData ? mapActionData(action) as TActionOutputData : undefined;
        return result;
      }
      else {
        return undefined;
      }
    },
  })
);

export const useGetComponentRegistration = <
  TComponentName extends string = ComponentName,
  TComponentActionName extends string = string,
  TProps extends object = object,
  TData = unknown
>(
  name: TComponentName
): ComponentRegistration<
  TComponentName,
  TComponentActionName,
  TProps,
  TData
> => {
  const registration = useComponentRegistryStore((s) => s.components.get(name));
  return registration as unknown as ComponentRegistration<
    TComponentName,
    TComponentActionName,
    TProps,
    TData
  >;
};

export const useComponentsMap = () => {
  const registrations = useComponentRegistryStore((s) => s.components);
  const components = React.useMemo(
    () => registrations.map((r) => r.component).toObject(),
    [registrations]
  );
  return components;
};
