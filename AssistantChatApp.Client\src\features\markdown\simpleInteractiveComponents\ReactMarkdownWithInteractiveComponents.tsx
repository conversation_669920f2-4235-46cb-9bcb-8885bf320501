import React, { useMemo, useCallback } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkDirective from 'remark-directive';
import remarkDirectiveRehype from 'remark-directive-rehype';
import { Submitter, SubmissionData, InteractiveComponentProps } from './types';

interface InteractiveMarkdownProps {
  children: string;
  messageId: string;
  onSubmit?: (data: SubmissionData) => void;
}

interface PollProps extends InteractiveComponentProps {
  question: string;
  options: string[] | string;
}

const Counter: React.FC<InteractiveComponentProps> = (props) => {
  const { submitter, messageId, ...otherProps } = props;
  const [count, setCount] = React.useState(0);

  const handleIncrement = () => {
    const newCount = count + 1;
    setCount(newCount);
    submitter({
      componentType: 'counter',
      data: { action: 'increment', count: newCount },
      messageId,
      componentProps: otherProps
    });
  };

  const handleDecrement = () => {
    const newCount = count - 1;
    setCount(newCount);
    submitter({
      componentType: 'counter',
      data: { action: 'decrement', count: newCount },
      messageId,
      componentProps: otherProps
    });
  };

  return (
    <div style={{ border: '1px solid #ccc', padding: '10px', margin: '10px 0', borderRadius: '4px' }}>
      <h4>Counter: {count}</h4>
      <button onClick={handleDecrement} style={{ marginRight: '10px' }}>-</button>
      <span style={{ margin: '0 10px', fontSize: '18px', fontWeight: 'bold' }}>{count}</span>
      <button onClick={handleIncrement}>+</button>
    </div>
  );
};

const Poll: React.FC<PollProps> = (props) => {
  const { children, question, options = [], messageId, submitter, ...otherProps } = props;
  const [selectedOption, setSelectedOption] = React.useState<string | null>(null);
  const [hasVoted, setHasVoted] = React.useState(false);

  const handleVote = (option: string) => {
    setSelectedOption(option);
    setHasVoted(true);
    submitter({
      componentType: 'poll',
      data: { question, selectedOption: option },
      messageId,
      componentProps: otherProps
    });
  };

  // Parse options if they come as a string
  const optionList = Array.isArray(options) ? options :
    typeof options === 'string' ? options.split(',').map(s => s.trim()) : [];

  return (
    <div style={{ border: '1px solid #ddd', padding: '15px', margin: '10px 0', borderRadius: '4px' }}>
      <h4>{question}</h4>
      {children && <div style={{ marginBottom: '10px' }}>{children}</div>}
      {optionList.map((option: string, index: number) => (
        <div key={index} style={{ margin: '5px 0' }}>
          <button
            onClick={() => handleVote(option)}
            disabled={hasVoted}
            style={{
              backgroundColor: selectedOption === option ? '#007bff' : '#f8f9fa',
              color: selectedOption === option ? 'white' : 'black',
              border: '1px solid #ccc',
              padding: '8px 12px',
              cursor: hasVoted ? 'not-allowed' : 'pointer',
              opacity: hasVoted && selectedOption !== option ? 0.6 : 1,
              borderRadius: '4px'
            }}
          >
            {option}
          </button>
        </div>
      ))}
      {hasVoted && (
        <p style={{ marginTop: '10px', fontStyle: 'italic', color: '#28a745' }}>
          ✓ You voted for: {selectedOption}
        </p>
      )}
    </div>
  );
};

export const InteractiveMarkdown: React.FC<InteractiveMarkdownProps> = ({
  children,
  messageId,
  onSubmit
}) => {
  const submitter: Submitter = useCallback((data: SubmissionData) => {
    if (onSubmit) {
      onSubmit(data);
    }
  }, [onSubmit]);

  const components = useMemo(() => ({
    // Simple Counter Component
    'counter': Counter,
    // Simple Poll Component
    'poll': Poll,
  }), []);

  return (
    <ReactMarkdown
      remarkPlugins={[
        remarkGfm,
        remarkDirective,
        remarkDirectiveRehype
      ]}
      components={components}
    >
      {children}
    </ReactMarkdown>
  );
};
