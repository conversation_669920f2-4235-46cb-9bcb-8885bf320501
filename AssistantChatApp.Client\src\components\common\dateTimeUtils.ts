import React from "react";
import { type Locale, format, isYesterday, isToday } from "date-fns";
import { ru as dateFnsRu, enUS as dateFnsEn } from "date-fns/locale";
import { useTranslation } from "react-i18next";
import type { TFunction } from "i18next";

export const i18nLocaleToDateFnsLocale = (i18nLocale: string): Locale => {
  switch (i18nLocale) {
    case "ru":
      return dateFnsRu;
    case "en":
      return dateFnsEn;
    default:
      return dateFnsRu;
  }
};

export const useDateFnsLocale = () => {
  const { i18n } = useTranslation(["common"]);

  return React.useMemo(
    () => i18nLocaleToDateFnsLocale(i18n.language),
    [i18n.language],
  );
};

export const formatAsDateWithMonthInText = (date: Date, locale: Locale) => {
  switch (locale.code) {
    case "ru":
      return format(date, "d MMMM yyyy", { locale });
    case "en-US":
      return format(date, "MMM d, yyyy", { locale });
    default:
      return format(date, "d MMMM yyyy", { locale });
  }
};

export const formatAsTimeInHoursAndMinutes = (date: Date, locale: Locale) => {
  switch (locale.code) {
    case "ru":
      return format(date, "HH:mm", { locale });
    case "en-US":
      return format(date, "h:mm a", { locale });
    default:
      return format(date, "HH:mm", { locale });
  }
};



export const formatAsYesterdayTodayOrDate = (
  date: Date,
  locale: Locale,
  t: TFunction<"common">
) => {
  if (isToday(date)) {
    return t("today");
  } else if (isYesterday(date)) {
    return t("yesterday");
  } else {
    return formatAsDateWithMonthInText(date, locale);
  }
};

/**
 * Format `Date` as date plus time up to minutes
 */
export const formatAsDateTimeUpToMinutes = (date: Date, locale: Locale) => {
  return `${formatAsDateWithMonthInText(date, locale)} ${formatAsTimeInHoursAndMinutes(date, locale)}`;
};

/**
 * Format `Date` as today, yesterday, or date plus time up to minutes
 */
export const formatAsYesterdayTodayOrDateTimeUpToMinutes = (
  date: Date,
  locale: Locale,
  t: TFunction<"common">
) => {
  const dateStr = formatAsYesterdayTodayOrDate(date, locale, t);
  const timeStr = formatAsTimeInHoursAndMinutes(date, locale);
  return `${dateStr} ${timeStr}`;
};
