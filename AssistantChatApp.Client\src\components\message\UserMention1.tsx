import React from "react";
import {
  Box,
  Chip,
  CircularProgress,
  IconButton,
  Tooltip,
  Typography,
} from "@mui/material";
import { AlternateEmail as AtSignIcon } from "@mui/icons-material";
import { useTryGetUserSuspense } from "../../queries/user";
import { useTranslation } from "react-i18next";
import { User } from "../../types";

interface UserMentionProps {
  userId: string | undefined;
}

export const UserMentionChip: React.FC<{ user: User | undefined }> = ({
  user,
}) => {
  const { t } = useTranslation(["common", "users"]);
  const userName = user?.displayName;
  return (
    <Chip
      size="small"
      variant="outlined"
      color={userName ? "info" : "error"}
      label={
        <Typography component="span">
          @
          {userName ?? (
            <Typography component="span" sx={{ opacity: 0.5 }}>
              {t("users:unknown")}
            </Typography>
          )}
        </Typography>
      }
    />
  );
};

export const UserMention: React.FC<UserMentionProps> = ({ userId }) => {
  const { t } = useTranslation(["common", "users"]);
  const { data: user } = useTryGetUserSuspense(userId);
  const userName = user?.displayName;
  const tooltip = userName ?? t("users:unknown_user");

  return (
    <React.Suspense
      fallback={
        <Chip
          size="small"
          variant="outlined"
          color="info"
          label={<CircularProgress />}
        />
      }
    >
      <Tooltip title={tooltip}>
        <Box component="span">
          <UserMentionChip user={user} />
        </Box>
      </Tooltip>
    </React.Suspense>
  );
};

export const AddUserMentionIconButton: React.FC<{
  onClick: (e: React.MouseEvent<HTMLElement>) => void;
}> = ({ onClick }) => {
  const { t } = useTranslation("messages");
  return (
    <Tooltip title={t("add_user_mention")}>
      <IconButton onClick={onClick}>
        <AtSignIcon fontSize="inherit" />
      </IconButton>
    </Tooltip>
  );
};
