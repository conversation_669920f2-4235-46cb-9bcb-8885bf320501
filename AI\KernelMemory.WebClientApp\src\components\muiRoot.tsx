import React, { PropsWithChildren } from "react";
import { CssBaseline, Theme, ThemeProvider, useMediaQuery, createTheme, ThemeOptions } from "@mui/material";
import { darkTheme, lightTheme, baseTheme } from "./muiTheme";

export const MuiRoot: React.FC<PropsWithChildren> = ({ children }) => {
    const isDark = useMediaQuery("(prefers-color-scheme: dark)");
    const theme = React.useMemo(() =>{
        const t = isDark ? darkTheme : lightTheme;
        return t;
    }, [isDark]);
    return (
        <ThemeProvider theme={theme}>
            <CssBaseline enableColorScheme>
                {children}
            </CssBaseline>
        </ThemeProvider>
    );
};
