import { z } from 'zod';

export const getMessagesSchema = z.object({
  params: z.object({
    chatId: z.string().uuid('Invalid chat ID'),
  }),
  query: z.object({
    page: z.string().regex(/^\d+$/, 'Page must be a number').optional(),
    limit: z.string().regex(/^\d+$/, 'Limit must be a number').optional(),
  }).optional(),
});

export const sendMessageSchema = z.object({
  params: z.object({
    chatId: z.string().uuid('Invalid chat ID'),
  }),
  body: z.object({
    content: z.string().min(1, 'Message content is required'),
    mentions: z.array(z.string().uuid('Invalid user ID')).optional(),
  }),
});

export const updateMessageSchema = z.object({
  params: z.object({
    chatId: z.string().uuid('Invalid chat ID'),
    messageId: z.string().uuid('Invalid message ID'),
  }),
  body: z.object({
    content: z.string().min(1, 'Message content is required'),
    mentions: z.array(z.string().uuid('Invalid user ID')).optional(),
  }),
});

export const deleteMessageSchema = z.object({
  params: z.object({
    chatId: z.string().uuid('Invalid chat ID'),
    messageId: z.string().uuid('Invalid message ID'),
  }),
});
