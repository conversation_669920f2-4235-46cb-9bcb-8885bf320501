#load "Env.fsx"

let [<Literal>] ApiUrl = "https://openrouter.ai/api/v1"

let apiKey = Env.env["OpenRouterApiKey"]

module Models =

    let [<Literal>] ``Llama-3.3-8B-Free`` = "meta-llama/llama-3.3-8b-instruct:free"
    let [<Literal>] ``DeepHermes-3-Mistral-24B-Free`` = "nousresearch/deephermes-3-mistral-24b-preview:free"
    let [<Literal>] ``Qwen3-14B-Free`` = "qwen/qwen3-14b:free"
    let [<Literal>] ``Mistral-Small-3.1-24B-Free`` = "mistralai/mistral-small-3.1-24b-instruct:free"
    let [<Literal>] ``Meta-Llama-Scout-Free`` = "meta-llama/llama-4-scout:free"
    let [<Literal>] ``Meta-Llama-3.3-70B-Free`` = "meta-llama/llama-3.3-70b-instruct:free"
