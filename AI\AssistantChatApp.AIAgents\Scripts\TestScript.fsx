//#r "nuget: Removable.OpenRouterClient, 0.0.4"
//#r "nuget: Microsoft.Extensions.AI.Abstractions"

#r "nuget: FSharp.Control.TaskSeq, 0.4.0"
#r "nuget: FSharp.Control.AsyncSeq, 3.2.1"
#r "nuget: Microsoft.SemanticKernel, 1.52.1"
#r "nuget: Microsoft.SemanticKernel.Agents.Core, 1.54"
#r "nuget: Microsoft.Extensions.Logging.Console, 9.0.5"
#r "nuget: Microsoft.Extensions.Http, 9.0.5"

// #r "nuget: HttpClientToCurl, 2.0.6"

//#r @"C:\Users\<USER>\.nuget\packages\microsoft.semantickernel\1.51.0\lib\net8.0\Microsoft.SemanticKernel.dll"
//#r @"C:\Users\<USER>\.nuget\packages\microsoft.semantickernel.agents.core\1.51.0\lib\net8.0\Microsoft.SemanticKernel.Agents.Core.dll"
#r "../bin/Debug/net9.0/Together.dll"
#r "../bin/Debug/net9.0/Together.SemanticKernel.dll"
#r "../bin/Debug/net9.0/AssistantChatApp.AIAgents.dll"

#load "TogetherAI.fsx"
#load "OpenRouter.fsx"
#load "Serialization.fsx"
#load "Chat.fsx"
#load "Plugins.fsx"
#load "Http.fsx"
#load "Helpers.fsx"
//#load "OpenRouterKernel.fs"

open System
open System.Net.Http
open System.Collections.Generic
open Microsoft.Extensions.DependencyInjection
open Microsoft.Extensions.Logging
open Microsoft.Extensions.Http
open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.ChatCompletion
//open AssistantChatApp.AIAgents
open Together.SemanticKernel
open Together.SemanticKernel.Extensions
open Microsoft.Extensions.AI
open FSharp.Control
open AssistantChatApp.AIAgents.SemanticKernelHelpers

module HttpClients =
    let [<Literal>] OpenRouterClient = "OpenRouterHttpClient"
    let [<Literal>] TogetherClient = "TogetherHttpClient"

(*
type PromptExecutionSettings with

    member this.WithAutoFunctionChoiceBehavior(?functions, ?autoInvoke, ?options) =
        this.FunctionChoiceBehavior <-
            FunctionChoiceBehavior.Auto(
                ?functions = functions,
                ?autoInvoke = autoInvoke,
                ?options = options
            )
        this

    member this.WithRequiredFunctionChoiceBehavior() =
        this.FunctionChoiceBehavior <-
            FunctionChoiceBehavior.Required()
        this

    member this.WithModelId(modelId: string) =
        this.ModelId <- modelId
        this
 *)

let ignoreMany (services: seq<obj>) = ignore services

let rootServices =
    ServiceCollection()
    |> Http.addHttpLoggingHandler
    |> Http.addNamedHttpClient HttpClients.OpenRouterClient
    |> Http.addNamedHttpClient HttpClients.TogetherClient
    |> fun s -> s.AddLogging(fun builder ->
        builder.AddSimpleConsole()
            .SetMinimumLevel(LogLevel.Information)
        |> ignore
    )
    |> fun s -> s.BuildServiceProvider()

let togetherHttpClient = Http.getHttpClientByName HttpClients.TogetherClient rootServices
let openRouterHttpClient = Http.getHttpClientByName HttpClients.OpenRouterClient rootServices


let addTogetherAIChatCompletion modelId (builder: IKernelBuilder) =
    builder.AddTogetherChatCompletion(
        apiKey = TogetherAI.apiKey,
        model = modelId,
        httpClient = togetherHttpClient
    )

let addOpenRouterChatCompletion = Helpers.addOpenRouterChatCompletion openRouterHttpClient
let addTogetherOpenAIChatCompletion = Helpers.addTogetherOpenAIChatCompletion togetherHttpClient

let builder = Kernel.CreateBuilder()

do Helpers.ignoreMany [
    builder
    |> addOpenRouterChatCompletion OpenRouter.Models.``Meta-Llama-Scout-Free``
    |> addOpenRouterChatCompletion OpenRouter.Models.``Qwen3-14B-Free``
    |> addOpenRouterChatCompletion OpenRouter.Models.``DeepHermes-3-Mistral-24B-Free``
    |> addOpenRouterChatCompletion "meta-llama/llama-3.3-70b-instruct:free"
    |> addOpenRouterChatCompletion "meta-llama/llama-4-maverick:free"
    //|> addOpenRouterChatCompletion OpenRouter.Models.``Llama-3.3-8B-Free``
    //|> addTogetherAIChatCompletion TogetherAI.Models.``Llama3.3_Free``
    |> addTogetherOpenAIChatCompletion TogetherAI.Models.``Llama3.2-3B-Instruct-Turbo``

    builder.Plugins
        //.AddFromType<Plugins.MathPlugin>()
        .AddFromType<Plugins.TimePlugin>()

    builder.Services.AddLogging(fun builder ->
        builder.AddSimpleConsole()
            .SetMinimumLevel(LogLevel.Debug)
        |> ignore
    )
]

let kernel = builder.Build()

let completionService = kernel.GetRequiredService<IChatCompletionService>()
let loggerFactory = kernel.GetRequiredService<ILoggerFactory>()

// let sendUserMessage = sendUserMessageBase (fun _ -> simulateStreamingResponse ())
// let sendUserMessageTaskSeq = sendUserMessageTaskSeqBase (fun _ -> simulateStreamingResponse ())


let getChat (history: ChatHistory) (settings: PromptExecutionSettings) =
    { new Chat.IChat with
        member this.AddAssistantMessage modelId message =
            history
            |> Chat.ChatHistory.addAssistantMessage modelId message
        member this.AddUserMessage(message) =
            history.AddUserMessage(message)
        member this.GetCompletionStreamAsync() =
            let retryPause = TimeSpan.FromSeconds(10.)
            let retryAttempts = 3
            let retryPauseMultiplier = 2.
            let makeRequest () =
                completionService.GetStreamingChatMessageContentsAsync(
                    history, settings, kernel
                )
            Helpers.makeStreamingRequestWithRetryLoop
                retryAttempts retryPause retryPauseMultiplier makeRequest

        member this.GetCompletionMessageAsync (): Threading.Tasks.Task<ChatMessageContent> =
            completionService.GetChatMessageContentAsync(
                history, settings, kernel
            )
    }

let execSettings1 =
    Connectors.OpenAI.OpenAIPromptExecutionSettings()
    |> PromptExecutionSettings.withFunctionChoiceRequired None (Some true) None
        //.WithAutoFunctionChoiceBehavior(autoInvoke = true)
    |> PromptExecutionSettings.withModelId
        TogetherAI.Models.``Llama3.2-3B-Instruct-Turbo``
        //TogetherAI.Models.``Llama3.2-3B-Instruct-Turbo``
        //OpenRouter.Models.``Mistral-Small-3.1-24B-Free``

// Verbose system prompt, that for some reason disrupts Llama 4 Scrout's ability to call functions.
let systemPrompt1 = "
    You're helpful assistant. To answer user questions, you should be given a set of functions/tools to use.
    So, when there is a function suitable for a task available, use it, don't solve the task on your own.
    If the task implies passing a result of one function to another, do not nest one function call into another as a parameter - this will cause parsing error (function parameters can be only JSON-compatible values).
    So, you should first obtain result value of the inner function, and then use the value as an argument to make a call to the outer function.
"

let systemPrompt2 = "
    You are a helpful assistant. Use the provided tools to answer user questions.
    If a task requires a sequence of tool calls, perform them one by one.
    Do not nest tool calls within parameters.
"

let systemPrompt3 = "You are a helpful assistant that uses tools to answer user questions."

let history = ChatHistory()

history.AddSystemMessage(systemPrompt3)

let chat = getChat history execSettings1

let sendUserMessage = Chat.sendUserMessageAndPrintResults chat



sendUserMessage "Write me a very short joke."
sendUserMessage "Do another one"
sendUserMessage "One more please, a very short one."

sendUserMessage "Cuéntame un chiste corto."
sendUserMessage "Una más, por favor."

sendUserMessage "What time is it now?"

Chat.namedUserMessage "David" "Tell a very short dad joke."
|> sendUserMessage

Chat.namedUserMessage "Alex" "Quite a mediocre joke, I think."
|> sendUserMessage

Chat.namedUserMessage "John" "How many parcitipants are there in this chat?"
|> sendUserMessage


sendUserMessage "What is natural logarithm of 8.65?"

sendUserMessage "What is logarithm of 25.25 with base 5?"

sendUserMessage "What time is it now?"
sendUserMessage "Yo do have a way to request current time. Check thoroughly the list of provided functions, specifically, functions related to `TimePlugin`."
sendUserMessage "No, make a proper function/tool call as you've been trained. Hint: the function you should call to know current time is `TimePlugin-get_current_datetime`."

sendUserMessage "What exact time will it be 2 hours 16 min from now (current DateTime)?"

let kargs = KernelArguments(execSettings1)

let fresult1 =
    kernel.InvokePromptAsync("What is natural logarithm of 9.34?", kargs)
    |> Async.AwaitTask
    |> Async.RunSynchronously

let fresult2 =
    kernel.InvokePromptAsync("What is logarithm of 25.25 with base 5?", kargs)
    |> Async.AwaitTask
    |> Async.RunSynchronously

let fresult3 =
    kernel.InvokePromptAsync("What time is it now?", kargs)
    |> Async.AwaitTask
    |> Async.RunSynchronously

module Agent =

    open Microsoft.SemanticKernel.Agents

    let instruction = $"""
Answer to user questions. You should be given a set of available functions/tools.
So, if you see there is a tool/function available to simplify resolving the question, use it, don't try doing everything on your own.
"""

    let openAIExecSettings =
        Connectors.OpenAI.OpenAIPromptExecutionSettings(
            //FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(
            //    autoInvoke = true
            //    //[kernel.Plugins.GetFunction("TimePlugin", "get_current_datetime")]
            //),
            ModelId = OpenRouter.Models.``Meta-Llama-Scout-Free``,
            FunctionChoiceBehavior = FunctionChoiceBehavior.Required(
                functions = [
                    for p in kernel.Plugins do
                        yield! p :> seq<KernelFunction>
                ],
                autoInvoke = true
            )
        )

    let togetherExecSettings =
        Together.SemanticKernel.TogetherPromptExecutionSettings(
            FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(),
            ModelId = TogetherAI.Models.``Llama3.3_Free``
        )

    let togetherAIKernelArgs = KernelArguments(togetherExecSettings)
    let openRouterOpenAIKernelArgs = KernelArguments(openAIExecSettings)

    let onIntermediateMessage (msg: ChatMessageContent): System.Threading.Tasks.Task =
        task {
            let msgOutput = {|
                Role = msg.Role
                Content = msg.Content
                //Metadata = string [for kv in msg.Metadata -> $"{kv.Key}: %A{kv.Value}"]
                Author = msg.AuthorName
                Model = msg.ModelId
                OtherContent = string [
                    for c in msg.Items ->
                        string c
                ]
                Source = string msg.Source
            |}
            printfn "[Agent Message]: %A" msgOutput
        }

    let openRouterAgentOptions =
        AgentInvokeOptions(
            Kernel = kernel,
            KernelArguments = openRouterOpenAIKernelArgs,
            OnIntermediateMessage = onIntermediateMessage
        )

    let togetherAIAgentOptions =
        AgentInvokeOptions(
            Kernel = kernel,
            KernelArguments = togetherAIKernelArgs,
            OnIntermediateMessage = onIntermediateMessage
        )

    let createTogetherAIAgent () =
        ChatCompletionAgent(
            Name = "AssistantAgent",
            Kernel = kernel,
            Instructions = instruction,
            Arguments = togetherAIKernelArgs
        )

    let createOpenRouterOpenAIAgent () =
        ChatCompletionAgent(
            Name = "AssistantAgent",
            Kernel = kernel,
            Instructions = instruction,
            Arguments = openRouterOpenAIKernelArgs,
            LoggerFactory = loggerFactory
        )


(*
    let agentOptions =
        Agents.AgentInvokeOptions(KernelArguments = kargs)

    let agent = Agents.ChatCompletionAgent(
        Name = "AssistantAgent",
        Kernel = kernel,
        Instructions = instruction,
        Arguments = kargs
    )
*)

open AssistantChatApp.AIAgents.Kernel

//let agent = AIAssistantAgent(
//    kernel,
//    OpenRouter.Models.``Mistral-Small-3.1-24B-Free``,
//    Agent.instruction
//)



let agent = Agent.createOpenRouterOpenAIAgent ()

//agent.Arguments.ExecutionSettings = Agent.openRouterAgentOptions.KernelArguments.ExecutionSettings

let chatThread = Agents.ChatHistoryAgentThread(history)

let agentResult1 =
    agent.InvokeAsync("What is logarithm of 25.25 with base 5?", chatThread)
    |> TaskSeq.toList

let agentResult2 =
    let query = String.concat " " [
        "What time is it now?"
        //"(`TimePlugin.get_current_datetime` should help you with that)"
    ]
    agent.InvokeAsync(query, thread = chatThread, options = Agent.openRouterAgentOptions)
    |> TaskSeq.toList

let agentResult3 =
    let query = "So?"
    agent.InvokeAsync(query, thread = chatThread, options = Agent.openRouterAgentOptions)
    |> TaskSeq.toList

//for r in agentResult2 do
//    Chat.ChatMessageContent.toPrettyString r.Message
//    |> printfn "%s"

// Chat History:

history |> Chat.ChatHistory.toStringList
history.Clear()

agent.Arguments.ExecutionSettings

agent.Arguments.ExecutionSettings.Count

agent.Kernel.Plugins
