# Citation


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**link** | **string** |  | [optional] [default to undefined]
**index** | **string** |  | [optional] [default to undefined]
**documentId** | **string** |  | [optional] [default to undefined]
**fileId** | **string** |  | [optional] [default to undefined]
**sourceContentType** | **string** |  | [optional] [default to undefined]
**sourceName** | **string** |  | [optional] [default to undefined]
**sourceUrl** | **string** |  | [optional] [default to undefined]
**partitions** | [**Array&lt;Partition&gt;**](Partition.md) |  | [optional] [default to undefined]

## Example

```typescript
import { Citation } from './api';

const instance: Citation = {
    link,
    index,
    documentId,
    fileId,
    sourceContentType,
    sourceName,
    sourceUrl,
    partitions,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
