import React, { useState } from "react";
import {
  IconButton,
  <PERSON>u,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
} from "@mui/material";
import {
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
  Computer as ComputerIcon,
} from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { type ThemeMode } from "../../contexts/ThemeModeContext";


// Component props interface
interface ThemeSwitcherProps {
  mode: ThemeMode;
  setMode: (mode: ThemeMode) => void;
}

// Get icon based on current mode
const ThemeModeIcon: React.FC<{ mode: ThemeMode }> = ({ mode }) => {
  switch (mode) {
    case "light":
      return <LightModeIcon />;
    case "dark":
      return <DarkModeIcon />;
    case "system":
    default:
      return <ComputerIcon />;
  }
};

export const ThemeSwitcher: React.FC<ThemeSwitcherProps> = ({
  mode,
  setMode,
}) => {
  const { t } = useTranslation("common");
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  // <PERSON>le opening the menu
  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // Handle closing the menu
  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  // Handle theme mode selection
  const handleModeSelect = (selectedMode: ThemeMode) => {
    setMode(selectedMode);
    handleCloseMenu();
  };

  return (
    <>
      <Tooltip title={t("choose_color_theme")}>
        <IconButton
          onClick={handleOpenMenu}
          aria-label="theme switcher"
          color="inherit"
        >
          <ThemeModeIcon mode={mode} />
        </IconButton>
      </Tooltip>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleCloseMenu}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem
          onClick={() => handleModeSelect("light")}
          selected={mode === "light"}
        >
          <ListItemIcon>
            <LightModeIcon />
          </ListItemIcon>
          <ListItemText primary={t("color_theme.light")} />
        </MenuItem>
        <MenuItem
          onClick={() => handleModeSelect("dark")}
          selected={mode === "dark"}
        >
          <ListItemIcon>
            <DarkModeIcon />
          </ListItemIcon>
          <ListItemText primary={t("color_theme.dark")} />
        </MenuItem>
        <MenuItem
          onClick={() => handleModeSelect("system")}
          selected={mode === "system"}
        >
          <ListItemIcon>
            <ComputerIcon />
          </ListItemIcon>
          <ListItemText primary={t("color_theme.system")} />
        </MenuItem>
      </Menu>
    </>
  );
};
