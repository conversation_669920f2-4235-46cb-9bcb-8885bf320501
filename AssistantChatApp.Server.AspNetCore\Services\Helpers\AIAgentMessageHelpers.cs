using AssistantChatApp.AIAgents.Shared;
using AssistantChatApp.Server.AspNetCore.DTOs;
using AssistantChatApp.Server.AspNetCore.Models;

using AIAgentMessage = AssistantChatApp.AIAgents.Shared.ChatMessage;
using AIAgentMessageContent = AssistantChatApp.AIAgents.Shared.MessageContent;
using AIAgentFunctionCall = AssistantChatApp.AIAgents.Shared.FunctionCall;
using AIAgentFunctionResult = AssistantChatApp.AIAgents.Shared.FunctionCallResult;

namespace AssistantChatApp.Server.AspNetCore.Services.Helpers
{
    public static class AIAgentMessageHelpers
    {
        public static AIAgentFunctionCall CreateFunctionCall(
            string callId,
            string? pluginName,
            string functionName,
            string? arguments
        ) =>
            new(
                callId,
                pluginName ?? "",
                functionName,
                JsonText.NewJsonText(arguments ?? "{}")
            );

        public static AIAgentFunctionResult CreateFunctionResult(
            string? callId,
            string? pluginName,
            string functionName,
            string result
        ) =>
            new(
                callId ?? "",
                pluginName ?? "",
                functionName,
                JsonText.NewJsonText(result)
            );

        public static AIAgentFunctionCall ToAgentFunctionCall(this FunctionCallContentDto callDto) =>
            CreateFunctionCall(
                callDto.CallId,
                callDto.PluginName,
                callDto.FunctionName,
                callDto.Arguments
            );

        public static AIAgentFunctionResult ToAgentFunctionResult(this FunctionResultContentDto resultDto) =>
            CreateFunctionResult(
                resultDto.CallId,
                resultDto.FunctionCall?.PluginName,
                // TODO: Handle null FunctionCall better (throw exception, maybe?)
                resultDto.FunctionCall?.FunctionName ?? "",
                resultDto.Result
            );

        public static AIAgentFunctionCall ToAgentFunctionCall(this FunctionCallContent callContent) =>
            CreateFunctionCall(
                callContent.CallId,
                callContent.PluginName,
                callContent.FunctionName,
                callContent.Arguments
            );

        public static AIAgentFunctionResult ToAgentFunctionResult(this FunctionResultContent resultContent) =>
            CreateFunctionResult(
                resultContent.CallId,
                resultContent.FunctionCall?.PluginName,
                // TODO: Handle null FunctionCall better (throw exception, maybe?)
                resultContent.FunctionCall?.FunctionName ?? "",
                resultContent.Result
            );

        public static AIAgentMessageContent ToAgentMessageContent(this ContentDto contentDto) =>
            contentDto switch
            {
                FunctionCallContentDto callDto => AIAgentMessageContent.NewFunctionCall(
                    callDto.ToAgentFunctionCall()
                ),
                FunctionResultContentDto resultDto => AIAgentMessageContent.NewFunctionResult(
                    resultDto.ToAgentFunctionResult()
                ),
                _ => throw new ArgumentOutOfRangeException(
                    paramName: nameof(contentDto),
                    message: $"Unknown Content DTO type: {contentDto.GetType().Name}"
                )
            };

        public static AIAgentMessageContent ToAgentMessageContent(this Content content) =>
            content switch
            {
                FunctionCallContent call => AIAgentMessageContent.NewFunctionCall(
                    call.ToAgentFunctionCall()
                ),
                FunctionResultContent result => AIAgentMessageContent.NewFunctionResult(
                    result.ToAgentFunctionResult()
                ),
                _ => throw new ArgumentOutOfRangeException(
                    paramName: nameof(content),
                    message: $"Unknown Content type: {content.GetType().Name}"
                )
            };

        public static IEnumerable<AIAgentMessageContent> ToAgentMessageNonTextContentItems(
            this MessageContents contents
        )
        {
            foreach (var call in contents.FunctionCalls)
            {
                yield return call.ToAgentMessageContent();
            }
            foreach (var result in contents.FunctionResults)
            {
                yield return result.ToAgentMessageContent();
            }
        }

        public static MessageContents ExtractMessageContents(this AIAgentMessage agentMessage)
        {
            var textSb = new System.Text.StringBuilder();
            var functionCalls = new List<FunctionCallContentDto>();
            var functionResults = new List<FunctionResultContentDto>();

            foreach (var contentItem in agentMessage.Content)
            {
                switch (contentItem)
                {
                    case AIAgentMessageContent.TextContent textContent:
                        textSb.AppendLine(textContent.text);
                        break;
                    case AIAgentMessageContent.FunctionCall callContent:
                        {
                            var call = callContent.call;
                            functionCalls.Add(new FunctionCallContentDto
                            {
                                CallId = call.CallId,
                                PluginName = call.PluginName,
                                FunctionName = call.FunctionName,
                                Arguments = call.Arguments.Value,
                            });
                            break;
                        }

                    case AIAgentMessageContent.FunctionResult resultContent:
                        {
                            var result = resultContent.result;
                            functionResults.Add(new FunctionResultContentDto
                            {
                                CallId = result.CallId,
                                Result = result.Result.Value,
                            });
                            break;
                        }
                    default:
                        throw new ArgumentOutOfRangeException(paramName: nameof(contentItem));
                }
            }
            return new MessageContents(textSb.ToString(), functionCalls, functionResults);
        }

        public static CreateMessageDto ToCreateMessageDto(this AIAgentMessage agentMessage)
        {
            var toolTagDto = MessageTags.GetByKey(TagKeys.Tool);
            List<TagDto> tags = agentMessage.AuthorRole.IsTool ? [toolTagDto] : [];
            var contents = agentMessage.ExtractMessageContents();
            List<ContentDto> otherContents = [
                .. contents.FunctionCalls,
                .. contents.FunctionResults
            ];
            // TODO: Parse mentions from text content
            return new CreateMessageDto
            {
                TextContent = contents.TextContent,
                OtherContents = otherContents,
                Tags = tags,
                Mentions = [],
            };
        }

        private static AuthorRole DetermineMessageRole(MessageDto m, string toolTagId)
        {
            if (m.Author.IsAI)
            {
                return AuthorRole.Assistant;
            }
            else if (m.IsTaggedWith(toolTagId))
            {
                return AuthorRole.Tool;
            }
            else
            {
                return AuthorRole.User;
            }
        }

        public static AIAgentMessage ToAgentMessage(this MessageForAiDto message)
        {
            var role = DetermineMessageRole(message.Message, MessageTags.GetByKey(TagKeys.Tool).Id);
            var textContent = AIAgentMessageContent.NewTextContent(message.Message.Content);
            return new AIAgentMessage(
                message.Message.Author.DisplayName,
                role,
                [
                    textContent,
                    ..message.Contents.ToAgentMessageNonTextContentItems()
                ]
            );
        }
    }
}
