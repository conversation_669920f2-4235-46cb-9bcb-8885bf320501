# DeleteAccepted


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**index** | **string** |  | [optional] [default to undefined]
**documentId** | **string** |  | [optional] [default to undefined]
**message** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { DeleteAccepted } from './api';

const instance: DeleteAccepted = {
    index,
    documentId,
    message,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
