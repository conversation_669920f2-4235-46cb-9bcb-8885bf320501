import React, { useState, useCallback } from 'react';
import {
  MDXEditor,
  MDXEditorMethods,
  toolbarPlugin,
  directivesPlugin,
  DirectiveDescriptor,
  usePublisher,
  insertDirective$,
  DirectiveEditorProps,
} from '@mdxeditor/editor';
import {
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Avatar,
  Chip,
  Box,
  TextField,
  Paper,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import { useGetUser } from '../../../../queries/user';
import { useGetChat } from '../../../../queries/chat';

import '@mdxeditor/editor/style.css'

// Types (as provided)
export interface User {
  id: string;
  displayName: string;
  createdAt: string;
}

export interface Message {
  id: string;
  chatId: string;
  author: User;
  content: string;
  mentions?: string[];
}

// // Mock hooks (replace with your actual implementations)
// const useChatParticipants = (): User[] => {
//   return [
//     { id: 'user1', displayName: '<PERSON>', createdAt: '2024-01-01' },
//     { id: 'user2', displayName: 'Bob Smith', createdAt: '2024-01-02' },
//     { id: 'user3', displayName: 'Charlie Brown', createdAt: '2024-01-03' },
//   ];
// };

const useSendMessage = () => {
  return {
    mutate: (data: { content: string; mentions: string[]; chatId: string }) => {
      console.log('Sending message:', data);
    },
    isPending: false,
  };
};

// User mention component for rendering in editor
const UserMentionComponent: React.FC<{
  userId: string;
  displayName: string;
}> = ({ displayName }) => {
  return (
    <Chip
      size="small"
      label={`@${displayName}`}
      variant="outlined"
      color="primary"
      sx={{
        fontSize: '0.875rem',
        height: '24px',
        backgroundColor: 'primary.light',
        color: 'primary.contrastText',
        fontWeight: 'medium',
        '&:hover': {
          backgroundColor: 'primary.main',
        },
      }}
    />
  );
};


// Generic directive editor for mentions
const MentionDirectiveEditor: React.FC<DirectiveEditorProps> = ({ mdastNode: node }) => {
  const userId = node.attributes?.id || '';
  // const displayName = node.children?.[0]?.value || '';
  const { data: user } = useGetUser(userId);
  const displayName = user?.displayName ?? userId;

  return (
    <UserMentionComponent userId={userId} displayName={displayName} />
  );
};

// Mention directive descriptor
const mentionDirective: DirectiveDescriptor = {
  name: 'mention',
  type: 'leafDirective',
  testNode: (node) => {
    return node.name === 'mention';
  },
  attributes: ['id'],
  hasChildren: true,
  Editor: MentionDirectiveEditor,
};

// User selection dialog
const UserSelectionDialog: React.FC<{
  open: boolean;
  onClose: () => void;
  onSelectUser: (user: User) => void;
  users: User[];
}> = ({ open, onClose, onSelectUser, users }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredUsers = users.filter(user =>
    user.displayName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectUser = (user: User) => {
    onSelectUser(user);
    onClose();
    setSearchTerm('');
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Select User to Mention</DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          placeholder="Search users..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ mb: 2 }}
          autoFocus
        />
        <Paper sx={{ maxHeight: 300, overflow: 'auto' }}>
          <List>
            {filteredUsers.map((user) => (
              <ListItem key={user.id} disablePadding>
                <ListItemButton onClick={() => handleSelectUser(user)}>
                  <Avatar sx={{ mr: 2, width: 32, height: 32 }}>
                    {user.displayName.charAt(0).toUpperCase()}
                  </Avatar>
                  <ListItemText
                    primary={user.displayName}
                    secondary={`ID: ${user.id}`}
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </Paper>
      </DialogContent>
    </Dialog>
  );
};

// Toolbar button for adding mentions
const MentionToolbarButton: React.FC<{ chatId: string }> = ({ chatId }) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const { data: chat } = useGetChat(chatId);
  const users = chat?.participants ?? [];
  const insertDirective = usePublisher(insertDirective$);

  const handleSelectUser = useCallback((user: User) => {
    insertDirective({
      type: 'leafDirective',
      name: 'mention',
      attributes: { id: user.id },
      // children: [{ type: 'text', value: user.displayName }],
    });
  }, [insertDirective]);

  return (
    <>
      <Tooltip title="Mention User">
        <IconButton
          onClick={() => setDialogOpen(true)}
          size="small"
          sx={{ mr: 1 }}
        >
          <PersonAddIcon />
        </IconButton>
      </Tooltip>
      <UserSelectionDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onSelectUser={handleSelectUser}
        users={users}
      />
    </>
  );
};

// Function to parse mentions from markdown content
const parseMentionsFromContent = (content: string): string[] => {
  const mentionRegex = /:mention\[([^\]]+)\]\{#([^}]+)\}/g;
  const mentions: string[] = [];
  let match;

  while ((match = mentionRegex.exec(content)) !== null) {
    const userId = match[2];
    if (!mentions.includes(userId)) {
      mentions.push(userId);
    }
  }

  return mentions;
};

// Main message editor component
export const MessageEditor: React.FC<{
  chatId: string
}> = ({ chatId }) => {
  const [content, setContent] = useState('');
  const sendMessage = useSendMessage();
  const editorRef = React.useRef<MDXEditorMethods>(null);

  const handleSubmit = useCallback(() => {
    if (!content.trim()) return;

    const mentions = parseMentionsFromContent(content);

    sendMessage.mutate({
      content: content.trim(),
      mentions,
      chatId,
    });

    // Clear editor after sending
    setContent('');
    if (editorRef.current) {
      editorRef.current.setMarkdown('');
    }
  }, [content, chatId, sendMessage]);

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleSubmit();
    }
  }, [handleSubmit]);

  return (
    <Box sx={{ border: 1, borderColor: 'divider', borderRadius: 1 }} onKeyDown={handleKeyDown}>
      <MDXEditor
        ref={editorRef}
        markdown={content}
        onChange={setContent}
        plugins={[
          toolbarPlugin({
            toolbarContents: () => (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <MentionToolbarButton chatId={chatId} />
                <Box sx={{ flexGrow: 1 }} />
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<SendIcon />}
                  onClick={handleSubmit}
                  disabled={!content.trim() || sendMessage.isPending}
                >
                  Send
                </Button>
              </Box>
            ),
          }),
          directivesPlugin({
            directiveDescriptors: [mentionDirective],
          }),
        ]}
        contentEditableClassName="prose"
      />
    </Box>
  );
};

// Example usage component
export const MessageEditorExample: React.FC = () => {
  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Box sx={{ mb: 2 }}>
        <h2>Message Editor with User Mentions</h2>
        <p>
          Click the person icon to mention users, or use Ctrl+Enter to send.
        </p>
      </Box>

      <MessageEditor
        chatId="test-chat-1"
      />

      <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
        <h4>Features demonstrated:</h4>
        <ul>
          <li>Click the person icon to open user selection dialog</li>
          <li>Search and select users to mention</li>
          <li>Mentions appear as highlighted chips in the editor</li>
          <li>Raw markdown shows the directive format</li>
          <li>Mentions are automatically parsed from content on submit</li>
          <li>Use Ctrl+Enter or click Send to submit</li>
        </ul>
      </Box>
    </Box>
  );
};
