import {
  Autocomplete,
  TextField,
  Avatar,
  Box,
  Button,
  Typography,
} from "@mui/material";
import { UserAvatar } from "../user/UserAvatar";
import { useForm } from "@tanstack/react-form";
import { useTranslation } from "react-i18next";
import { User } from "../../types";

interface UserSelectionFormProps {
  onSubmit: (selectedUser: User) => Promise<void>;
  users: User[];
  submitLabel: string;
  showSubmitButton?: boolean;
}

export const UserSelectionForm: React.FC<UserSelectionFormProps> = ({
  onSubmit,
  users,
  submitLabel,
  showSubmitButton,
}) => {
  const { t } = useTranslation(["common", "users"]);

  const form = useForm({
    defaultValues: {
      selectedUser: users.length > 0 ? users[0] : (null as User | null),
    },
    onSubmit: async ({ value }) => {
      if (value.selectedUser) {
        await onSubmit(value.selectedUser);
      }
    },
    validators: {
      onChange: (state) => {
        if (!state.value.selectedUser) {
          return t("common:form.required")
        }
        return undefined;
      },
    }
  });

  const getAvatarContent = (user: User) => {
    if (user.avatar) {
      return <Avatar src={user.avatar} sx={{ width: 32, height: 32 }} />;
    }
    return (
      <Avatar sx={{ width: 32, height: 32, bgcolor: "primary.main" }}>
        {user.displayName.charAt(0).toUpperCase()}
      </Avatar>
    );
  };

  const filterOptions = (
    options: User[],
    { inputValue }: { inputValue: string }
  ) => {
    return options.filter((option) =>
      option.displayName.toLowerCase().includes(inputValue.toLowerCase())
    );
  };

  return (
    <Box
      component="form"
      onSubmit={(e) => {
        e.preventDefault();
        form.handleSubmit();
      }}
      sx={{ p: 2 }}
    >
      <form.Field
        name="selectedUser"
        children={(field) => (
          <Autocomplete
            options={users}
            value={field.state.value}
            onChange={(_, newValue) => field.handleChange(newValue)}
            getOptionLabel={(option) => option.displayName}
            isOptionEqualToValue={(option, value) =>
              option.displayName === value?.displayName
            }
            filterOptions={filterOptions}
            renderOption={(props, option) => (
              <Box
                component="li"
                {...props}
                key={option.displayName}
                sx={{ display: "flex", alignItems: "center", gap: 2 }}
              >
                <UserAvatar
                  displayName={option.displayName}
                  avatar={option.avatar}
                  isAI={option.isAI}
                />
                <Typography variant="body1">{option.displayName}</Typography>
              </Box>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                label={t("users:user")}
                placeholder={t("users:search_user")}
                variant="outlined"
                fullWidth
                slotProps={{
                  input: {
                    ...params.InputProps,
                    startAdornment: field.state.value ? (
                      <Box
                        sx={{ display: "flex", alignItems: "center", mr: 1 }}
                      >
                        {getAvatarContent(field.state.value)}
                      </Box>
                    ) : null,
                  },
                }}
              />
            )}
            sx={{ mb: 2 }}
          />
        )}
      />
      {showSubmitButton && (
        <form.Subscribe
          selector={(state) => ({ canSubmit: state.canSubmit, isSubmitting: state.isSubmitting })}
          children={({ canSubmit, isSubmitting }) =>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={!canSubmit}
              loading={isSubmitting}
            >
              {submitLabel}
            </Button>
          }
        />
      )}
    </Box>
  );
};
