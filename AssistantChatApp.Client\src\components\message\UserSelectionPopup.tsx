import React, { useEffect, useMemo, useState } from "react";
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Paper,
  Popover,
  TextField,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useGetChat } from "../../queries/chat";
import { User } from "../../types";

interface UserSelectionPopupProps {
  chatId: string;
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  onSelectUser: (user: User) => void;
  searchQuery?: string;
}

export const UserSelectionPopup: React.FC<UserSelectionPopupProps> = ({
  chatId,
  anchorEl,
  open,
  onClose,
  onSelectUser,
  searchQuery = "",
}) => {
  const { t } = useTranslation(["users", "messages"]);
  const { data: chat } = useGetChat(chatId);
  const [internalSearchQuery, setInternalSearchQuery] = useState(searchQuery);

  const filteredUsers = useMemo(() => {
    const query = internalSearchQuery.toLowerCase();
    return (
      chat?.participants.filter(
        (user) =>
          user.displayName.toLowerCase().includes(query) ||
          user.id.toLowerCase().includes(query),
      ) ?? []
    );
  }, [chat?.participants, internalSearchQuery]);

  useEffect(() => {
    setInternalSearchQuery(searchQuery);
  }, [searchQuery]);

  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
    >
      <Paper sx={{ minWidth: 250, maxWidth: 400, maxHeight: 300 }}>
        <Box sx={{ p: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder={t("users:search_user")}
            value={internalSearchQuery}
            onChange={(e) => setInternalSearchQuery(e.target.value)}
            autoFocus
          />
        </Box>
        <List dense sx={{ maxHeight: 200, overflow: "auto" }}>
          {filteredUsers.length === 0 ? (
            <ListItem>
              <ListItemText primary={t("users:no_users_found")} />
            </ListItem>
          ) : (
            filteredUsers.map((user) => (
              <ListItem key={user.id}>
                <ListItemButton
                  onClick={() => {
                    onSelectUser(user);
                    onClose();
                  }}
                >
                  <ListItemText
                    primary={user.displayName}
                    secondary={`ID: ${user.id}`}
                  />
                </ListItemButton>
              </ListItem>
            ))
          )}
        </List>
      </Paper>
    </Popover>
  );
};
