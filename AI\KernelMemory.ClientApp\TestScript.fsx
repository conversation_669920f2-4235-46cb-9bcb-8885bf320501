#r "nuget: Microsoft.KernelMemory.WebClient, 0.98.250508.3"

open System
open Microsoft.KernelMemory

let memory = MemoryWebClient("http://localhost:5272")

let run t =
    t
    |> Async.AwaitTask
    |> Async.RunSynchronously

memory.DeleteDocumentAsync("labelling_ukaz_243", "default").Wait()

let meanOfIdentificationSearchResult =
    memory.SearchAsync(
        "Что такое средство идентификации?",
        index = "nomic-embed-text",
        limit = 10,
        minRelevance = 0.5
    )
    |> run

meanOfIdentificationSearchResult.Results
|> Seq.tryHead
|> Option.map (fun d ->
    d.DocumentId,
    [for p in d.Partitions -> p.Relevance, p.Text]
)
