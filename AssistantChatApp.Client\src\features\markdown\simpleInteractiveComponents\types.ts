export interface SubmissionData {
  componentType: string;
  data: unknown;
  messageId: string;
  componentProps?: unknown; // Original props from directive
}

export interface Submitter {
  (data: SubmissionData): void;
}

export interface InteractiveComponentProps {
  children?: React.ReactNode;
  submitter: Submitter;
  messageId: string;
  [key: string]: unknown; // Props from directive attributes
}
