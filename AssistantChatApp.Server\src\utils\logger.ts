import config from '../config';

// Simple logger with different log levels
const logger = {
  info: (message: string, ...args: any[]) => {
    if (config.nodeEnv !== 'test') {
      console.log(`[INFO] ${message}`, ...args);
    }
  },
  
  warn: (message: string, ...args: any[]) => {
    if (config.nodeEnv !== 'test') {
      console.warn(`[WARN] ${message}`, ...args);
    }
  },
  
  error: (message: string, ...args: any[]) => {
    if (config.nodeEnv !== 'test') {
      console.error(`[ERROR] ${message}`, ...args);
    }
  },
  
  debug: (message: string, ...args: any[]) => {
    if (config.nodeEnv === 'development') {
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  },
};

export default logger;
