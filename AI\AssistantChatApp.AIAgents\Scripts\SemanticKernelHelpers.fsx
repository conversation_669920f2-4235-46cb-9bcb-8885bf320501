#r "nuget: Microsoft.SemanticKernel, 1.52.1"
#r "nuget: Microsoft.SemanticKernel.Agents.Core, 1.52.1"

#load "Config.fsx"

open System
open Microsoft.SemanticKernel
open Config

module KernelBuilder =

    let addTogetherOpenAIChatCompletion
        apiKey modelId
        (httpClient)
        (builder: IKernelBuilder)
        =
        builder.AddOpenAIChatCompletion(
            modelId = modelId,
            endpoint = Uri TogetherAI.ApiEndpoint,
            apiKey = apiKey,
            ?httpClient = httpClient
        )

    let addOpenRouterOpenAIChatCompletion
        apiKey modelId
        httpClient
        (builder: IKernelBuilder)
        =
        builder.AddOpenAIChatCompletion(
            modelId,
            Uri OpenRouter.ApiEndpoint,
            apiKey,
            ?httpClient = httpClient
        )

    let configurePlugins
        (configure: IKernelBuilderPlugins -> IKernelBuilderPlugins)
        (builder: IKernelBuilder)
        =
        configure builder.Plugins
        |> fun _ -> builder

module PromptExecutionSettings =

    let withFunctionChoice
        (behavior: FunctionChoiceBehavior)
        (settings: #PromptExecutionSettings)
        =
        settings.FunctionChoiceBehavior <- behavior
        settings

    let withFunctionChoiceAuto
        (autoInvoke: bool)
        (functions: seq<KernelFunction> option)
        (options: FunctionChoiceBehaviorOptions option)
        (settings: #PromptExecutionSettings)
        =
        settings
        |> withFunctionChoice (
            FunctionChoiceBehavior.Auto(
                ?functions = functions,
                autoInvoke = autoInvoke,
                ?options = options
            ))

    let withFunctionChoiceRequired
        (functions: seq<KernelFunction> option)
        (autoInvoke: bool option)
        (options: FunctionChoiceBehaviorOptions option)
        (settings: #PromptExecutionSettings)
        =
        settings
        |> withFunctionChoice (
            FunctionChoiceBehavior.Required(
                ?functions = functions,
                ?autoInvoke = autoInvoke,
                ?options = options
            ))

    let withFunctionChoiceNone
        (functions: seq<KernelFunction> option)
        (options: FunctionChoiceBehaviorOptions option)
        (settings: #PromptExecutionSettings)
        =
        settings
        |> withFunctionChoice (
            FunctionChoiceBehavior.None(
                ?functions = functions,
                ?options = options
            ))

    let withServiceId (serviceId: string) (settings: #PromptExecutionSettings) =
        settings.ServiceId <- serviceId
        settings

    let withModelId (modelId: string) (settings: #PromptExecutionSettings) =
        settings.ModelId <- modelId
        settings

type IKernelBuilder with

    member builder.AddTogetherOpenAIChatCompletion(apiKey, modelId, ?httpClient) =
        builder
        |> KernelBuilder.addTogetherOpenAIChatCompletion apiKey modelId httpClient

    member builder.AddOpenRouterOpenAIChatCompletion(apiKey, modelId, ?httpClient) =
        builder
        |> KernelBuilder.addOpenRouterOpenAIChatCompletion apiKey modelId httpClient


module ChatHistory =

    open Microsoft.SemanticKernel.ChatCompletion

    let tryGetLastAssistantMessage (history: ChatHistory) =
        let rec loop (i: int) =
            if i > 0 then
                let msg = history[i]
                match msg.Role with
                | r when r = AuthorRole.Assistant -> Some msg
                | _ -> loop (i - 1)
            else None

        loop (history.Count - 1)

    let addAssistantMessage (modelId: string) (message: string) (history: ChatHistory) =
        let aiMessage = ChatMessageContent(
            AuthorRole.Assistant,
            message,
            modelId = modelId
        )
        history.Add(aiMessage)
