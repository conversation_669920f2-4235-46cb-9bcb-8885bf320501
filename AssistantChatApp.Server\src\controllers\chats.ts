import { Response } from 'express';
import { getChats, getChatById, createChat, updateChat, deleteChat } from '../services/chats';
import { AuthRequest, ChatFilters } from '../types';
import { parsePaginationParams } from '../utils/pagination';

/**
 * Get chats controller
 * @route GET /chats
 */
export async function getChatsController(req: AuthRequest, res: Response) {
  if (!req.userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }
  
  const { page, limit } = parsePaginationParams(req.query);
  
  const filters: ChatFilters = {
    page,
    limit,
    search: req.query.search as string | undefined,
  };
  
  // Handle array parameters
  if (req.query.tags) {
    filters.tags = Array.isArray(req.query.tags)
      ? (req.query.tags as string[])
      : [req.query.tags as string];
  }
  
  if (req.query.participants) {
    filters.participants = Array.isArray(req.query.participants)
      ? (req.query.participants as string[])
      : [req.query.participants as string];
  }
  
  if (req.query.startDate) {
    filters.startDate = req.query.startDate as string;
  }
  
  if (req.query.endDate) {
    filters.endDate = req.query.endDate as string;
  }
  
  const result = await getChats(req.userId, filters);
  
  res.status(200).json(result);
}

/**
 * Get chat by ID controller
 * @route GET /chats/:chatId
 */
export async function getChatByIdController(req: AuthRequest, res: Response) {
  if (!req.userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }
  
  const { chatId } = req.params;
  
  const chat = await getChatById(chatId, req.userId);
  
  res.status(200).json(chat);
}

/**
 * Create chat controller
 * @route POST /chats
 */
export async function createChatController(req: AuthRequest, res: Response) {
  if (!req.userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }
  
  const { title, description, participants, tags } = req.body;
  
  const chat = await createChat(
    {
      title,
      description,
      participants: participants || [],
      tags: tags || [],
    },
    req.userId
  );
  
  res.status(201).json(chat);
}

/**
 * Update chat controller
 * @route PATCH /chats/:chatId
 */
export async function updateChatController(req: AuthRequest, res: Response) {
  if (!req.userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }
  
  const { chatId } = req.params;
  const { title, description, participants, tags } = req.body;
  
  const chat = await updateChat(
    chatId,
    {
      title,
      description,
      participants,
      tags,
    },
    req.userId
  );
  
  res.status(200).json(chat);
}

/**
 * Delete chat controller
 * @route DELETE /chats/:chatId
 */
export async function deleteChatController(req: AuthRequest, res: Response) {
  if (!req.userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }
  
  const { chatId } = req.params;
  
  await deleteChat(chatId, req.userId);
  
  res.status(204).send();
}
