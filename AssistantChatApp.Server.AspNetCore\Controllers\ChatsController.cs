using AssistantChatApp.Server.Contracts.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AssistantChatApp.Server.Services;

namespace AssistantChatApp.Server.AspNetCore.Controllers
{
    [ApiController]
    [Route("/api/chats")]
    [Authorize]
    public class ChatsController : ControllerBase
    {
        private readonly ChatService _chatService;

        public ChatsController(ChatService chatService)
        {
            _chatService = chatService;
        }

        [HttpGet]
        public async Task<IActionResult> GetChats([FromQuery] ChatFilterDto filter)
        {
            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new ErrorResponseDto { Message = "Invalid token" });
            }

            var chats = await _chatService.GetChatsAsync(userId, filter);
            return Ok(chats);
        }

        [HttpGet("{chatId}")]
        public async Task<IActionResult> GetChat(string chatId)
        {
            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new ErrorResponseDto { Message = "Invalid token" });
            }

            var chat = await _chatService.GetChatByIdAsync(chatId, userId);

            if (chat == null)
            {
                return NotFound(new ErrorResponseDto { Message = "Chat not found" });
            }

            return Ok(chat);
        }

        [HttpPost]
        public async Task<IActionResult> CreateChat([FromBody] CreateChatDto createChatDto)
        {
            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new ErrorResponseDto { Message = "Invalid token" });
            }

            var chat = await _chatService.CreateChatAsync(userId, createChatDto);
            return CreatedAtAction(nameof(GetChat), new { chatId = chat.Id }, chat);
        }

        [HttpPatch("{chatId}")]
        public async Task<IActionResult> UpdateChat(string chatId, [FromBody] UpdateChatDto updateChatDto)
        {
            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new ErrorResponseDto { Message = "Invalid token" });
            }

            var chat = await _chatService.UpdateChatAsync(chatId, userId, updateChatDto);

            if (chat == null)
            {
                return NotFound(new ErrorResponseDto { Message = "Chat not found or you don't have permission to update it" });
            }

            return Ok(chat);
        }

        [HttpDelete("{chatId}")]
        public async Task<IActionResult> DeleteChat(string chatId)
        {
            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new ErrorResponseDto { Message = "Invalid token" });
            }

            var result = await _chatService.DeleteChatAsync(chatId, userId);

            if (!result)
            {
                return NotFound(new ErrorResponseDto { Message = "Chat not found or you don't have permission to delete it" });
            }

            return NoContent();
        }
    }
}
