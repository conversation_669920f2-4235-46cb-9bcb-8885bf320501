```bash
# Navigate to the desired directory
cd FullStackFSharpApp.Client/

# Initialize a new pnpm project
pnpm init

# Add React and ReactDOM as dependencies
pnpm i react react-dom

# Add development dependencies for TypeScript and type definitions for React and ReactDOM
pnpm i -D typescript @types/react @types/react-dom

pnpm i -D  @types/node

# Add Vite as a development dependency
pnpm add -D vite

# Add the Vite React plugin as a development dependency
pnpm add -D @vitejs/plugin-react

pnpm add -D concurrently

# Add development dependencies for TanStack Router and DevTools
pnpm add -D @tanstack/router-plugin @tanstack/router-devtools

# Add TanStack React Router as a dependency
pnpm add @tanstack/react-router

pnpm add @tanstack/zod-adapter zod

pnpm add @tanstack/react-query

# Add material-react-table and its peer dependencies
pnpm add material-react-table @mui/material @mui/x-date-pickers @mui/icons-material @mui/lab @emotion/react @emotion/styled

pnpm add @fontsource/roboto

# Add i18next and its dependencies
pnpm add i18next react-i18next i18next-browser-languagedetector
```
