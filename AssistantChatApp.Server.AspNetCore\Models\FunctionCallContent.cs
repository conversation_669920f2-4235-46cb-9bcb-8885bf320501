namespace AssistantChatApp.Server.AspNetCore.Models
{
    public class FunctionCallContent : Content
    {
        public required string CallId { get; set; }
        public required string FunctionName { get; set; }
        public string? PluginName { get; set; } = string.Empty;
        public string? Arguments { get; set; } = string.Empty;
        public FunctionResultContent? FunctionResult { get; set; }
    }
}
