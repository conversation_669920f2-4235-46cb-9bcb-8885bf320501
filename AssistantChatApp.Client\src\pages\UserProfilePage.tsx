import React from "react";
import { Skeleton } from "@mui/material";
import { UserProfileView } from "../components/user/UserProfile";
import { useTranslation } from "react-i18next";
import { useAuth } from "../contexts/AuthContext";

export const UserProfilePage: React.FC = () => {
  const { t } = useTranslation(["common", "users"]);
  const { user, isLoading } = useAuth();
  // If `isLoading`, return a Skeleton that simulates a User Profile Card.
  if (isLoading) {
    return (
      <Skeleton
        variant="rectangular"
        width="100%"
        height={400}
        sx={{ borderRadius: 4 }}
      />
    );
  }
  if (!user) {
    return <div>{t("users:user_not_found")}</div>;
  }
  return <UserProfileView user={user} />;
};
