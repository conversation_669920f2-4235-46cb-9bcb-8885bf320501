# MemoryAnswer


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**streamState** | [**StreamStates**](StreamStates.md) |  | [optional] [default to undefined]
**question** | **string** |  | [optional] [default to undefined]
**noResult** | **boolean** |  | [optional] [default to undefined]
**noResultReason** | **string** |  | [optional] [default to undefined]
**text** | **string** |  | [optional] [default to undefined]
**tokenUsage** | [**Array&lt;TokenUsage&gt;**](TokenUsage.md) |  | [optional] [default to undefined]
**relevantSources** | [**Array&lt;Citation&gt;**](Citation.md) |  | [optional] [default to undefined]

## Example

```typescript
import { MemoryAnswer } from './api';

const instance: MemoryAnswer = {
    streamState,
    question,
    noResult,
    noResultReason,
    text,
    tokenUsage,
    relevantSources,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
