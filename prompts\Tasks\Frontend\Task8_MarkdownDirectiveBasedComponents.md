I have an AI chat app with a frontend implemented via React in TypeScript.
Currently, the messages support only formatted text (via Markdown). Markdown is rendered via `react-markdown` library.
Now I want to extend this by embedding interactive components that users can interact with (instead of typing message text). And it should be an extensible system, that allows adding, "registering" new components in a rapid and unified way.
To implement this, I've decided to leverage the existing Markdown ecosystem by using `react-markdown` that is based on `remark` and `rehype` which support plugins.
As I've discovered, there is already a feature suggested to CommonMark standard called "Markdown directives". The feature can allow me to specify metadata that can be parsed to render custom components. Also, there are already suitable `remark` plugins implemented that simplify and speed up the feature implementation:
- `remark-directive`. The plugin parses custom directives (e.g., :my-component[content]{key="value"}) in Markdown into specific Markdown AST nodes.
- `remark-directive-rehype`. The plugin automates the conversion of Markdown directive AST nodes into HTML AST nodes that are finally rendered to actual HTML by `rehype`. I.e., it automatically maps a directive name into a tag name, directive attributes into HTML attributes, and directive content into HTML tag content.

All this allows to make a first implementation in a simple and rapid manner. Here is a minimal demo example:

```typescript
import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkDirective from 'remark-directive';
import remarkDirectiveRehype from 'remark-directive-rehype';

// Your custom React component
const MyCustomComponent = ({ children, someProp }) => {
  return (
    <div style={{ border: '1px solid blue', padding: '10px' }}>
      <h3>Custom Component!</h3>
      <p>Prop value: {someProp}</p>
      {children}
    </div>
  );
};

const markdownContentSample = `
This is some markdown.

:::my-custom-tag{someProp="hello"}
This content is inside my custom component.
:::
`;

const Demo = () => {
  return (
    <ReactMarkdown
      remarkPlugins={[
        remarkGfm,
        remarkDirective,
        remarkDirectiveRehype
      ]}
      components={{
        // Map your custom HTML tag name (lowercase) to your React component
        'my-custom-tag': MyCustomComponent,
      }}
    >
      {markdownContentSample}
    </ReactMarkdown>
  );
};
```

Now, the key part left is to design a unified approach to register new interactive components for messages. To make the components actually interactive, a way of communicating the user input to the app logic is required. Such behavior could be either "hard-coded" into component logic (which couples strongly the component to the app logic), or it could be passed via props (e.g., callbacks that the component uses inside its internal logic). For the later, some kind of unified and generic mechanism need to be designed. I understand that this approach limits the flexibility of components capability, but it facilitates the process of adding new components and maintaining the existing ones.

Could you suggest an implementation of such mechanism?

---

The thing is, not all the props actually required for a component to be initialized, can be parsed from Markdown. For example, to make the components interactive (allow feedback from the user), some props should be passed by the app logic. Example of such props are event handlers. To generalize, unify the components definition, there should be designed a certain mechanism to represent a generic feedback handler.



To embed the components into the Markdown messages in a structured way, I've decided to use Markdown directives. This will allow to specify a desired component identifier and necessary props. By matching the identifier and parsing the props, the component can be rendered inside a message. For that, also, a corresponding Markdown directive should be registered in the app. Then, when the message Markdown content is rendered, the directives are recognized and are replaced with the matching components. However, not all the props actually needed for a component to initialize, can be parsed from Markdown. For example, to make the components interactive (allow feedback from the user), some props should be passed by the app logic. Example of such props are event handlers. To generalize, unify the components definition, there should be designed a certain mechanism to represent a generic feedback handler.

Could you suggest such extensible component definition interface? I suppose, it should contain a set of input props parsed from Markdown, and some kind of generic "submitter". The idea is to unify, restrict the process of registering new components.

## Generic component

```typescript
type Submitter<T> = {
    submit: (data: T) => void;
}
```

## Survey component

A survey component that outputs a question and a set of available answers. The user can select one (if `muiltiple` is `false`) or multiple (if `muiltiple` is `true`) answers by clicking on them. If `multiple` is `false`, the answers should be represented as radio buttons. The answers are passed as a Map. The keys are used by the submitter. Also, if `showKeys` is `true`, the keys should be shown as part of the answer view. If `multiple` is `true`, the answers should be represented as checkboxes. The component should also provide a submit button.

```typescript
interface SurveyComponentProps {
    question: string;
    answers: Map<string, string>;
    multiple: boolean;
    showKeys: boolean;
    onSubmit: (answers: number[]) => void; // Submits keys of selected answers
}
```


## AnswerSelector

### Props

```typescript
interface AnswerSelectorProps {
    options: string[];
    multiple: boolean;
    onSubmit: (answers: string[]) => void;
}
```
