import { alpha } from '@mui/material/styles';
import createTheme from "@mui/material/styles/createTheme";
import "@mui/lab/themeAugmentation";

export const baseTheme = createTheme({
    typography: {
        fontSize: 12
    },
    components: {
        MuiTextField: {
            defaultProps: {
                size: "small",
                variant: "filled"
            }
        },
        MuiButton: {
            defaultProps: {
                size: "small",
                variant: "contained"
            }
        },
        MuiLoadingButton: {
            defaultProps: {
                size: "small",
                variant: "contained"
            }
        }
    }
});

export const lightTheme = createTheme({
    ...baseTheme,
    palette: {
        mode: 'light',
    },
});

export const darkTheme = createTheme({
    ...baseTheme,
    palette: {
        mode: 'dark',
    },
})
