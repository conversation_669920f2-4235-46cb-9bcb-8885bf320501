{"ApiProviders": {"TogetherAI": {"Id": "TogetherAI", "ApiEndpoint": "https://api.together.xyz/v1/", "ApiKey": "<API-key>"}, "OpenRouter": {"Id": "OpenRouter", "ApiEndpoint": "https://openrouter.ai/api/v1/", "ApiKey": "<API-key>"}}, "KernelMemoryInstances": {"KernelMemory1": {"KernelMemory": {"DocumentStorageType": "SimpleFileStorage", "Retrieval": {"EmbeddingGeneratorType": "Ollama", "MemoryDbType": "SimpleVectorDb"}, "Services": {"Ollama": {"Endpoint": "http://localhost:11434", "EmbeddingModel": "nomic-embed-text:latest"}, "SimpleFileStorage": {"Directory": "./agent1_docs", "StorageType": "Disk"}, "SimpleVectorDb": {"Directory": "./agent1_vectors", "StorageType": "Disk"}}}}}, "Agents": {"AgentUser1": {"Id": "AgentUser1", "Name": "Agent User #1", "ProviderId": "TogetherAI", "ModelId": "meta-llama/Llama-3.2-3B-Instruct-Turbo", "KernelMemoryInstanceId": "KernelMemory1", "MainSystemPromptFilePath": "path/to/kb/main-system-prompt.txt", "KbSettings": {"RootDirPath": "path/to/kb/directory"}}}}