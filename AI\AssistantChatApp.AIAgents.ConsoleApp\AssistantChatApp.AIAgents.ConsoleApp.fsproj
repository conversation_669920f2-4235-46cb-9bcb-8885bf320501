﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>5bd94e0e-cd08-4d5f-988e-a5fcb86817d0</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="ConsoleUI.fs" />
    <Compile Include="Program.fs" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="KB\**\*.txt" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="KB\**\*.md" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Argu" Version="6.2.5" />
    <PackageReference Include="Fable.Remoting.AspNetCore" Version="2.42.0" />
    <PackageReference Include="Fable.Remoting.DotnetClient" Version="3.36.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
    <PackageReference Include="Serilog.Sinks.OpenTelemetry" Version="4.2.0" />
    <PackageReference Include="Serilog.Sinks.Trace" Version="4.0.0" />
    <PackageReference Include="Spectre.Console.Cli" Version="0.50.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\AssistantChatApp.Aspire.ServiceDefaults\AssistantChatApp.Aspire.ServiceDefaults.csproj" />
    <ProjectReference Include="..\AssistantChatApp.AIAgents\AssistantChatApp.AIAgents.fsproj" />
  </ItemGroup>

</Project>
