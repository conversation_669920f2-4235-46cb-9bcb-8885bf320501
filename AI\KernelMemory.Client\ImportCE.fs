module Enixar.KernelMemory.Client.Import.ImportCE

type State = {
    ImportEntries: DocumentImportEntry list
    DefaultIndex: string option
    /// Tags applied to all documents
    SharedTags: (string * string) list
    /// Tags applied to the documents without explicit tags (i.e. when `tags` is `None`)
    DefaultTags: (string * string) list
} with
    static member Empty = {
        ImportEntries = []
        DefaultIndex = None
        SharedTags = []
        DefaultTags = []
    }

module State =

    let addImportEntries (importEntries) (state: State) =
        { state with ImportEntries = importEntries @ state.ImportEntries }

    let addImportEntry (importEntry) (state: State) =
        { state with ImportEntries = importEntry :: state.ImportEntries }

    let setDefaultIndex (index: string) (state: State) =
        { state with DefaultIndex = Some index }

    let setSharedTags (tags: (string * string) list) (state: State) =
        { state with SharedTags = tags }

    let getFinalIndex (index: string option) (state: State) =
        index |> Option.orElse state.DefaultIndex

    let mergeTags
        (tags: (string * string) list option)
        (state: State)
        =
        tags
        |> Option.defaultValue state.DefaultTags
        |> List.append state.SharedTags
        |> List.distinct
        |> function
            | [] -> None
            | tags -> Some tags

    let applyToDocumentImportEntry (entry: DocumentImportEntry) (state: State) =
        { entry with
            Index = state |> getFinalIndex entry.Index
            Tags = state |> mergeTags entry.Tags
        }


type ImportBuilder() =

    member _.Zero() = State.Empty

    member _.Yield(_) = State.Empty

    member _.Combine(a: State, b: State) =
        { b with ImportEntries = a.ImportEntries @ b.ImportEntries }

    //member _.Delay(f) = f

    // Finalize and return the list of import entries
    member _.Run(state: State): DocumentImportEntry list =
        ([], state.ImportEntries)
        ||> List.fold (fun acc entry ->
            let newEntry =
                state
                |> State.applyToDocumentImportEntry entry
            newEntry :: acc
        )

    [<CustomOperation("document")>]
    member this.Document(state: State, entry: DocumentImportEntry) =
        state |> State.addImportEntry entry

    [<CustomOperation("document")>]
    member this.Document(state, id: string, filePath: string, ?index: string): State =
        DocumentImportEntry.Create(id, filePath, ?index = index)
        |> fun entry -> state |> State.addImportEntry entry

    [<CustomOperation("document")>]
    member this.Document(state, id: string, filePath: string, tags: (string * string) list): State =
        DocumentImportEntry.Create(id, filePath, tags = tags)
        |> fun entry -> state |> State.addImportEntry entry

    [<CustomOperation("document")>]
    member this.Document(state, id: string, filePath: string, index: string, tags: (string * string) list): State =
        DocumentImportEntry.Create(id, filePath, index, tags)
        |> fun entry -> state |> State.addImportEntry entry

    [<CustomOperation("documents")>]
    member this.Documents(state: State, documents: Map<string, string>, ?index: string, ?tags: (string * string) list) =
        let entries =
            (state.ImportEntries, documents)
            ||> Map.fold (fun acc docId docPath ->
                DocumentImportEntry.Create(docId, docPath, ?index = index, ?tags = tags)
                :: acc
            )
        { state with ImportEntries = entries }

    [<CustomOperation("defaultIndex")>]
    member this.DefaultIndex(state, index: string) =
        { state with DefaultIndex = Some index }

    // Default tags custom operation
    [<CustomOperation("sharedTags")>]
    member this.SharedTags(state, tags: (string * string) list) =
        { state with SharedTags = tags }

    [<CustomOperation("defaultTags")>]
    member this.DefaultTags(state, tags: (string * string) list) =
        { state with DefaultTags = tags }



// Create a global instance for easier use
let import = ImportBuilder()


// let example1 =
//     import {
//         defaultIndex "default"
//         document "id1" "path/to/file1.txt" "index1" [ "tag1", "value1" ]
//         defaultTags [ "year", "2021" ]
//         document "id2" "path/to/file2.txt" "index2" [ "tag2", "value2" ]
//         document "id3" "path/to/file3.txt"
//         document "id4" "path/to/file4.txt" [ "tag3", "value3" ]
//         document (DocumentImportEntry.Create("id5", "path/to/file5.txt", "index5", [ "tag4", "value4" ]))
//     }
