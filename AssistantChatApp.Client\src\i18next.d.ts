import "18next";
import type { resources } from "./i18n";

declare module "i18next" {
  // the type of your `resources` constant
  type DefaultResources = (typeof resources)["en"];

  interface CustomTypeOptions {
    // Resources type must match one language slice: `resources[LANG]`
    resources: DefaultResources;
    // (optional) if you use defaultNS + fallbackNS heavily, you can type it here:
    defaultNS: "common";
    fallbackNS: "common";
  }
}
