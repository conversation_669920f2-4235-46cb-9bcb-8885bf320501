﻿<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.4.0" />
    
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>7720eda8-6bbf-4aa0-a9b2-4ec145eb35f2</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.4.0" />
    <PackageReference Include="CommunityToolkit.Aspire.Hosting.NodeJS.Extensions" Version="9.6.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AI\AssistantChatApp.AIAgents.ConsoleApp\AssistantChatApp.AIAgents.ConsoleApp.fsproj" />
    <ProjectReference Include="..\AI\KernelMemory.WebService\KernelMemory.WebService.csproj" />
    <ProjectReference Include="..\AssistantChatApp.Server.AspNetCore\AssistantChatApp.Server.AspNetCore.csproj" />
    <ProjectReference Include="..\AssistantChatApp.Shared\AssistantChatApp.Shared.csproj">
        <IsAspireProjectResource>false</IsAspireProjectResource>
    </ProjectReference>
  </ItemGroup>

</Project>
