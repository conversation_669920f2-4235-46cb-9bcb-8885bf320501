#!meta

{"kernelInfo":{"defaultKernelName":"csharp","items":[{"name":"csharp","languageName":"C#","aliases":["c#","cs"]},{"name":"fsharp","languageName":"fsharp"},{"name":"html","languageName":"HTML"},{"name":"http","languageName":"HTTP"},{"name":"javascript","languageName":"JavaScript","aliases":["js"]},{"name":"mermaid","languageName":"Mermaid"},{"name":"pwsh","languageName":"PowerShell","aliases":["powershell"]},{"name":"value"}]}}

#!markdown

## Install and Load Dependencies

#!fsharp

#r "nuget: FSharp.Control.TaskSeq, 0.4.0"
#r "nuget: FSharp.Control.AsyncSeq, 3.2.1"
#r "nuget: Microsoft.SemanticKernel, 1.54"
#r "nuget: Microsoft.SemanticKernel.Agents.Core, 1.54"
#r "nuget: Microsoft.Extensions.Logging.Console, 9.0.5"
#r "nuget: Microsoft.Extensions.Http, 9.0.5"
#r "nuget: Microsoft.Extensions.Hosting, 9.0.5"

#!fsharp

#load "Env.fsx"
#load "Config.fsx"
#load "Serialization.fsx"
#load "Chat.fsx"
#load "Plugins.fsx"
#load "Http.fsx"
#load "Helpers.fsx"

#!fsharp

#r "../bin/Debug/net9.0/AssistantChatApp.AIAgents.Shared.dll"
#r "../bin/Debug/net9.0/AssistantChatApp.AIAgents.dll"

#!fsharp

open System
open System.Net.Http
open System.Collections.Generic
open Microsoft.Extensions.DependencyInjection
open Microsoft.Extensions.Logging
open Microsoft.Extensions.Options
open Microsoft.Extensions.Http
open Microsoft.Extensions.Hosting
open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.ChatCompletion
open Microsoft.SemanticKernel.Agents
//open AssistantChatApp.AIAgents
open Microsoft.Extensions.AI
open FSharp.Control
open AssistantChatApp.AIAgents
open AssistantChatApp.AIAgents.SemanticKernelHelpers
open Config

#!markdown

## Core Services and Kernel Setup

#!fsharp

let kbSettings: KnowledgeBase.KnowledgeBaseSettings = {
    RootDirPath = Path.Combine(__SOURCE_DIRECTORY__, @"..\..\AssistantChatApp.AIAgents.ConsoleApp\KB")
    MainSystemPromptFileName = "MainSystemPrompt.txt"
}

let agentSettings: Core.AgentSettings = {
    Id = "Agent1"
    ModelId = OpenRouter.Models.``Meta-Llama-Scout-Free``
    ProviderId = "OpenRouter"
    KbSettings = kbSettings
}

let hostEnv: IHostEnvironment =
    let s = Microsoft.Extensions.Hosting.HostApplicationBuilderSettings()
    s.ContentRootPath <- __SOURCE_DIRECTORY__
    s.EnvironmentName <- "Development"
    s.ApplicationName <- "AiAgentTestNotebook"
    let b = Microsoft.Extensions.Hosting.Host.CreateEmptyApplicationBuilder(s)
    b.Environment

let kbPlugin = new KnowledgeBase.KnowledgeBasePlugin(OptionsWrapper kbSettings, hostEnv)

let rootServices =
    ServiceCollection()
    |> Http.addHttpLoggingHandler
    |> Http.addNamedHttpClient Config.OpenRouter.HttpClientName
    |> Http.addNamedHttpClient Config.TogetherAI.HttpClientName
    |> fun s -> s.AddLogging(fun builder ->
        builder.AddSimpleConsole()
            .SetMinimumLevel(LogLevel.Information)
        |> ignore
    )
    |> fun s -> s.AddSingleton(hostEnv)
    |> fun s -> s.AddSingleton(kbPlugin)
    |> fun s -> s.BuildServiceProvider()

let loggerFactory = rootServices.GetRequiredService<ILoggerFactory>()
let togetherHttpClient = Http.getHttpClientByName Config.OpenRouter.HttpClientName rootServices
let openRouterHttpClient = Http.getHttpClientByName Config.TogetherAI.HttpClientName rootServices

let addOpenRouterChatCompletion = Helpers.addOpenRouterChatCompletion openRouterHttpClient
// let addTogetherOpenAIChatCompletion = Helpers.addTogetherOpenAIChatCompletion togetherHttpClient

let builder = Kernel.CreateBuilder()

do Helpers.ignoreMany [
    builder
    |> addOpenRouterChatCompletion OpenRouter.Models.``Meta-Llama-Scout-Free``
    // |> addOpenRouterChatCompletion OpenRouter.Models.``Llama-3.3-8B-Free``
    // |> addOpenRouterChatCompletion OpenRouter.Models.``Qwen3-14B-Free``
    // |> addOpenRouterChatCompletion OpenRouter.Models.``DeepHermes-3-Mistral-24B-Free``
    // |> addOpenRouterChatCompletion "meta-llama/llama-3.3-70b-instruct:free"
    // |> addOpenRouterChatCompletion "meta-llama/llama-4-maverick:free"
    //|> addOpenRouterChatCompletion OpenRouter.Models.``Llama-3.3-8B-Free``
    //|> addTogetherAIChatCompletion TogetherAI.Models.``Llama3.3_Free``
    // |> addTogetherOpenAIChatCompletion TogetherAI.Models.``Llama3.2-3B-Instruct-Turbo``

    builder.Plugins
        //.AddFromType<Plugins.MathPlugin>()
        // .AddFromType<Plugins.TimePlugin>()
        .AddFromObject(kbPlugin)

    builder.Services.AddLogging(fun builder ->
        builder.AddSimpleConsole()
            .SetMinimumLevel(LogLevel.Debug)
        |> ignore
    )
]

let kernel = builder.Build()

let completionService = kernel.GetRequiredService<IChatCompletionService>()

let agent =
    Agents.AIAssistantAgent(
        loggerFactory,
        completionService,
        kbPlugin,
        agentSettings
    )

let chatHistory = ChatHistory()
let chatThread = ChatHistoryAgentThread(chatHistory)
let agentInvokeOptions =
    let kargs = KernelArguments(
        PromptExecutionSettings(
            FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(),
            ModelId = OpenRouter.Models.``Meta-Llama-Scout-Free``
        )
    )
    AgentInvokeOptions(KernelArguments = kargs)

//let askAgent inputs = Kernel.askAgent inputs "Alan" Threading.CancellationToken.None agent.Agent
let askAgent options newInputs =
    chatHistory.AddRange([
        for m in newInputs -> Core.ChatMessage.toChatMessageContent m
    ])
    agent.Agent.InvokeAsync(chatThread, ?options = options)
    |> TaskSeq.toArray

#!markdown

## Agent Tests

#!fsharp

let userMessage content : AssistantChatApp.AIAgents.Shared.ChatMessage ={
    AuthorName = "Alan"
    AuthorRole = AssistantChatApp.AIAgents.Shared.AuthorRole.User
    Content = content
}

let noAutoInvokeOptions =
    let kargs = KernelArguments(
        PromptExecutionSettings(
            FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(autoInvoke = false)
        )
    )
    AgentInvokeOptions(KernelArguments = kargs)

let agentResponse1 =
    [
        userMessage "What electronics products are available?"
    ]
    |> askAgent (Some noAutoInvokeOptions)

agentResponse1.Display()

#!fsharp

let agentResponse1ToolCalls =
    agentResponse1[0].Message.Items
    |> Seq.choose (function
        | :? Microsoft.SemanticKernel.FunctionCallContent as funCall ->
            match funCall.InnerContent with
            | :? OpenAI.Chat.ChatToolCall as toolCall ->
                let callJson =
                    {|
                        Id = toolCall.Id
                        FunctionName = toolCall.FunctionName
                        Arguments = toolCall.FunctionArguments.ToObjectFromJson<System.Text.Json.JsonElement>()
                    |}
                    |> System.Text.Json.JsonSerializer.Serialize
                let callResult =
                    funCall.InvokeAsync(agent.Agent.Kernel)
                    |> Async.AwaitTask |> Async.RunSynchronously
                Some {|
                    Call = callJson
                    Result = callResult
                |}
            | _ -> None
        | _ -> None
    )
    |> Seq.toArray
agentResponse1ToolCalls.Display()

#!fsharp

for x in agentResponse1ToolCalls do
    let c = ChatMessageContent(Role = AuthorRole.Tool)
    c.Items.Add(x.Result)
    chatHistory.Add(c)

chatHistory.Display()

#!fsharp

let noFunctionOptions =
    let kargs = KernelArguments(
        PromptExecutionSettings(
            FunctionChoiceBehavior = FunctionChoiceBehavior.None(functions = [])
        )
    )
    AgentInvokeOptions(KernelArguments = kargs)

let agentResponse2 = [] |> askAgent (Some noFunctionOptions)
agentResponse2.Display()

#!fsharp

let agentInvokeOptionsWithModelId modelId =
    let kargs = KernelArguments(
        PromptExecutionSettings(
            FunctionChoiceBehavior = FunctionChoiceBehavior.Required(),
            ModelId = modelId
        )
    )
    AgentInvokeOptions(KernelArguments = kargs)

let agentInvokeOptions =
    agentInvokeOptionsWithModelId "meta-llama/llama-4-maverick:free"

#!fsharp

let userMessage2 = userMessage "So, what is the result of the analysis? Use the tools provided to you to access the file."
let agentResponse2 =
    [userMessage2]
    |> askAgent (Some agentInvokeOptions)
printfn "Response #2:"
agentResponse2[0].Message.Content.Display()

#!fsharp

chatHistory.Clear()
chatHistory.Display()

#!fsharp

chatHistory.Display()

#!fsharp

chatHistory.RemoveRange(2, chatHistory.Count - 2)
chatHistory.Display()

#!fsharp

chatHistory.Clear()
chatHistory.Display()

#!markdown

## Chat Completion Service Tests

#!fsharp

let completionResponse1 =
    chatHistory.Clear()
    // chatHistory.AddSystemMessage(kbPlugin.GetMainSystemPrompt())
    chatHistory.AddUserMessage("What electronics products are available?")
    chatHistory.AddAssistantMessage("To answer, I must fetch the `products.json` file via one of the provided tools/functions")
    let execSettings =
        PromptExecutionSettings(
            FunctionChoiceBehavior = FunctionChoiceBehavior.None()
        )

    completionService.GetChatMessageContentAsync(chatHistory, execSettings, kernel)
    |> Async.AwaitTask
    |> Async.RunSynchronously

completionResponse1.Display()

#!fsharp

let funcCall = completionResponse1.Items[1] :?> Microsoft.SemanticKernel.FunctionCallContent
let funcResult =
    funcCall.InvokeAsync(kernel) |> Async.AwaitTask |> Async.RunSynchronously
let funcResultMsg = ChatMessageContent(Role = AuthorRole.Tool)
funcResultMsg.Items.Add(funcResult)
chatHistory.Add(funcResultMsg)

let completionResponse1_2 =
    let execSettings =
        PromptExecutionSettings(
            FunctionChoiceBehavior = FunctionChoiceBehavior.None()
        )

    completionService.GetChatMessageContentAsync(chatHistory, execSettings, kernel)
    |> Async.AwaitTask
    |> Async.RunSynchronously

completionResponse1_2.Display()

#!fsharp

funcResultMsg.Display()

#!fsharp

chatHistory.Display()

#!fsharp

kernel.Services.GetServices<IChatCompletionService>().Display()
