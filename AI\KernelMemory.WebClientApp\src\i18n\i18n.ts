import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import enCommon from "./locales/en/common.json";
import ruCommon from "./locales/ru/common.json";

const isDev = import.meta.env.DEV;

export const resources = {
  en: {
    common: enCommon,
  },
  ru: {
    common: ruCommon,
  }
} as const;

i18n
  .use(LanguageDetector)           // auto-detect browser language
  .use(initReactI18next)           // bind react-i18next
  .init({
    resources,
    fallbackLng: 'en',
    supportedLngs: ['en', 'ru'],
    ns: ["common"],
    defaultNS: "common",
    fallbackNS: "common",
    debug: isDev,
    interpolation: {
      escapeValue: false          // React already does escaping
    },
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json'
    },
  });

export default i18n;
