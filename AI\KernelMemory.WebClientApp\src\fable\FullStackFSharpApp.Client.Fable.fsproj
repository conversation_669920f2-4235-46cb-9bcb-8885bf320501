﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <PropertyGroup>
    <PackageTags>fable-javascript</PackageTags>
    <FablePackageType>library</FablePackageType>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="Server.fs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Fable.Core" Version="4.5.0" />
    <PackageReference Include="Fable.Package.SDK" Version="1.3.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Fable.React.Types" Version="18.4.0" />
    <PackageReference Include="Fable.Remoting.Client" Version="7.34.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\FullStackFSharpApp.Shared\FullStackFSharpApp.Shared.fsproj" />
  </ItemGroup>

</Project>
