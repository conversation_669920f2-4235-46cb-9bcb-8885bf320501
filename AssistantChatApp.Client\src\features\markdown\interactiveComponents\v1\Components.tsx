import React from "react";
// import {  } from "./componentRegistry";
import type { ComponentRegistration, InteractiveComponentProps } from "./types";
import {
  type SurveyComponentProps as OriginSurveyComponentProps,
  SurveyComponent as OriginSurveyComponent,
} from "../../../../components/common/Survey";
import {
  type ComponentRegistry,
  useComponentRegistryStore,
} from "./componentRegistryStore";

type SurveyComponentProps = Omit<OriginSurveyComponentProps, "onSubmit">;

type SurveyComponentData = {
  selectedAnswers: string[];
};

type SurveyComponentActionName = "submit";

export const SurveyComponent: React.FC<
  InteractiveComponentProps<
    SurveyComponentActionName,
    SurveyComponentData,
    SurveyComponentProps
  >
> = ({ context, answers, multiple, question, showKeys }) => {
  return (
    <OriginSurveyComponent
      answers={answers}
      multiple={multiple}
      question={question}
      showKeys={showKeys}
      onSubmit={(answers) => {
        context.onAction({
          type: "submit",
          payload: { selectedAnswers: answers },
        });
      }}
    />
  );
};

type InteractiveComponentTypeDefs = {
  survey: {
    actionName: SurveyComponentActionName;
    data: SurveyComponentData;
    props: SurveyComponentProps;
  };
};

type InteractiveComponentName = keyof InteractiveComponentTypeDefs;

type InteractiveComponentRegistrations = {
  [K in keyof InteractiveComponentTypeDefs]: ComponentRegistration<
    K,
    InteractiveComponentTypeDefs[K]["actionName"],
    InteractiveComponentTypeDefs[K]["data"],
    InteractiveComponentTypeDefs[K]["props"]
  >;
};

const getSurveyResultAsMdText = (answers: string[]) => {
  return answers.map((x) => `* ${x}`).join("\n");
};

export const getMessageComponentRegistrations = (
  sendMessageToChat: (text: string) => void
): InteractiveComponentRegistrations => ({
  survey: {
    component: SurveyComponent,
    initialData: { selectedAnswers: [] },
    actionHandlers: {
      submit: (action) => {
        if (action.payload) {
          const mdText = getSurveyResultAsMdText(
            action.payload?.selectedAnswers
          );
          sendMessageToChat(mdText);
        }
      },
    },
  },
});

export const registerMessageComponents = (
  registry: ComponentRegistry,
  sendMessageToChat: (text: string) => void
) => {
  const registrations = getMessageComponentRegistrations(sendMessageToChat);
  console.debug("Registering 'survey' component...", registry);
  registry.register("survey", registrations.survey);
  console.debug("`survey` component registered.", registry);
};

export const useRegisterMessageComponents = (
  sendMessageText: (text: string) => void
) => {
  const registerComponent = useComponentRegistryStore((s) => s.register);

  React.useEffect(() => {
    console.debug("Registering Interactive components...");
    const registrations = getMessageComponentRegistrations(sendMessageText);

    Object.entries(registrations).forEach(([name, registration]) => {
      registerComponent(name as InteractiveComponentName, registration);
    });
  }, [sendMessageText, registerComponent]);
};
