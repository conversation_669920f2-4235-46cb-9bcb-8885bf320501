import React from "react";
import { useMutation } from "@tanstack/react-query";
import { userApi } from "../api/userApi";
import { User } from "../types";

export const useGetUser = (
  setUser: React.Dispatch<React.SetStateAction<User | undefined>>,
) => {
  return useMutation({
    mutationFn: (userId: string) => userApi.getUser(userId),
    onSuccess: (user) => {
      setUser(user);
    },
  });
};
