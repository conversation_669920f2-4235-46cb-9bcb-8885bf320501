#r "nuget: YamlDotNet, 16.3.0"

module Json =

    open System.Text.Json
    open System.Text.Json.Serialization

    let serializerOptions =
        let o = JsonSerializerOptions()
        o.PropertyNamingPolicy <- JsonNamingPolicy.CamelCase
        o

    let serialize (x: 't) = JsonSerializer.Serialize(x, serializerOptions)
    let deserialize (json: string): 't = JsonSerializer.Deserialize<'t>(json, serializerOptions)


module Yaml =

    open YamlDotNet.Serialization

    let serializer =
        SerializerBuilder()
            .ConfigureDefaultValuesHandling(DefaultValuesHandling.OmitNull)
            .WithNamingConvention(NamingConventions.CamelCaseNamingConvention.Instance)
            .Build()

    let deserializer =
        DeserializerBuilder()
            .WithNamingConvention(NamingConventions.CamelCaseNamingConvention.Instance)
            .Build()

    let serialize (x: 't) =
        serializer.Serialize(x)

    let deserialize (yaml: string): 't =
        deserializer.Deserialize<'t>(yaml)
