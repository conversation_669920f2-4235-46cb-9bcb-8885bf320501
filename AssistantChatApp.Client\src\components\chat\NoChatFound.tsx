import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { MessageSquarePlus } from 'lucide-react';
import { useTranslation } from "react-i18next";

const NoChatFound: React.FC = () => {
  const { t } = useTranslation(["chats", "common"]);
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 8,
        textAlign: 'center',
      }}
    >
      <MessageSquarePlus size={80} color="#9ca3af" strokeWidth={1} />

      <Typography variant="h5" sx={{ mt: 3, fontWeight: 'medium' }}>
        {t("chats:no_chats_found")}
      </Typography>

      <Typography variant="body1" color="text.secondary" sx={{ mt: 1, mb: 4, maxWidth: 450 }}>
        {t("chats:no_chats_found_message")}
      </Typography>

      <Button
        variant="contained"
        size="large"
        startIcon={<MessageSquarePlus size={20} />}
      >
        {t("chats:create_new_chat")}
      </Button>
    </Box>
  );
};

export default NoChatFound;
