<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <VersionPrefix>1.0.0</VersionPrefix>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Program.fs" />
    <None Include="TestScript.fsx" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="appsettings.json" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="appsettings.example.json" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="documentImports.example.json" />
  </ItemGroup>

  <ItemGroup>
    <None Include="docs\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Scripts\Dependencies.fsx" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="EluciusFTW.SpectreCoff" Version="0.50.6" />
    <PackageReference Include="FSharp.Data" Version="6.6.0" />
    <PackageReference Include="FSharp.SystemTextJson" Version="1.4.36" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.7" />
    <PackageReference Include="Microsoft.KernelMemory.WebClient" Version="0.98.250508.3" />
    <PackageReference Include="Spectre.Console" Version="0.50.0" />
    <PackageReference Include="Spectre.Console.Cli" Version="0.50.0" />
    <PackageReference Include="Thoth.Json.System.Text.Json" Version="0.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\KernelMemory.Client\Enixar.KernelMemory.Client.fsproj" />
  </ItemGroup>
</Project>
