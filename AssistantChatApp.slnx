<Solution>
  <Configurations>
    <Platform Name="Any CPU" />
    <Platform Name="x64" />
    <Platform Name="x86" />
  </Configurations>
  <Folder Name="/AI/">
    <Project Path="AI/AssistantChatApp.AIAgents.Client/AssistantChatApp.AIAgents.Client.fsproj" />
    <Project Path="AI/AssistantChatApp.AIAgents.ConsoleApp/AssistantChatApp.AIAgents.ConsoleApp.fsproj" />
    <Project Path="AI/AssistantChatApp.AIAgents.Shared.Dotnet/AssistantChatApp.AIAgents.Shared.Dotnet.fsproj" />
    <Project Path="AI/AssistantChatApp.AIAgents.Shared/AssistantChatApp.AIAgents.Shared.fsproj" />
    <Project Path="AI/AssistantChatApp.AIAgents/AssistantChatApp.AIAgents.fsproj" />
    <Project Path="AI/KernelMemory.Client/Enixar.KernelMemory.Client.fsproj" Id="0c083b4f-2adc-4636-8453-83fefc04a85e" />
    <Project Path="AI/KernelMemory.ClientApp/Enixar.KernelMemory.ClientApp.fsproj" Id="a66b9afa-96ce-47e9-b6fc-683367729f36" />
    <Project Path="AI/KernelMemory.WebService/KernelMemory.WebService.csproj" />
  </Folder>
  <Folder Name="/AI/Connectors/" />
  <Folder Name="/AI/Connectors/TogetherAI/">
    <Project Path="../../../Prog/AI/together-dotnet/Together.SemanticKernel/Together.SemanticKernel.csproj" />
    <Project Path="../../../Prog/AI/together-dotnet/Together/Together.csproj" />
  </Folder>
  <Folder Name="/Aspire/">
    <Project Path="AssistantChatApp.Aspire.AppHost/AssistantChatApp.Aspire.AppHost.csproj" />
    <Project Path="AssistantChatApp.Aspire.ServiceDefaults/AssistantChatApp.Aspire.ServiceDefaults.csproj" />
  </Folder>
  <Folder Name="/ChatApp/">
    <Project Path="AssistantChatApp.Client/AssistantChatApp.Client.esproj">
      <Platform Project="AnyCPU" />
      <Build />
      <Deploy />
    </Project>
    <Project Path="AssistantChatApp.DataStore.AdminApp/AssistantChatApp.DataStore.AdminApp.csproj" Id="c4c3b240-fc04-4865-8572-9e811a6c2987" />
    <Project Path="AssistantChatApp.DataStore.Infrastructure/AssistantChatApp.DataStore.Infrastructure.csproj" Id="c4b89239-82b5-47d9-837e-5bab263bafde" />
    <Project Path="AssistantChatApp.DataStore.Migrations.PostgreSQL/AssistantChatApp.DataStore.Migrations.PostgreSQL.csproj" Id="f0c5338b-1114-4bdc-a31a-8fdb03a6e3a6" />
    <Project Path="AssistantChatApp.DataStore/AssistantChatApp.DataStore.csproj" Id="b92c3235-7ddc-4b57-b511-1485820bd0dc" />
    <Project Path="AssistantChatApp.Server.AspNetCore.Tests/AssistantChatApp.Server.AspNetCore.Tests.csproj" />
    <Project Path="AssistantChatApp.Server.AspNetCore/AssistantChatApp.Server.AspNetCore.csproj" />
    <Project Path="AssistantChatApp.Server.Contracts/AssistantChatApp.Server.Contracts.csproj" Id="e132a729-d116-4201-95b4-9678619f1794" />
    <Project Path="AssistantChatApp.Server.Services/AssistantChatApp.Server.Services.csproj" Id="aa4b047b-d946-41b3-a636-cbcb98ee0ddd" />
  </Folder>
  <Project Path="AssistantChatApp.Shared/AssistantChatApp.Shared.csproj" />
  <Properties Name="ExtensibilityGlobals" Scope="PostLoad">
    <Property Name="FileExplorer" Value="|Scripts\" />
  </Properties>
</Solution>
