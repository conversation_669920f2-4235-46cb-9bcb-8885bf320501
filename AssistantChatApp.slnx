<Solution>
  <Configurations>
    <Platform Name="Any CPU" />
    <Platform Name="x64" />
    <Platform Name="x86" />
  </Configurations>
  <Folder Name="/AI/">
    <Project Path="AI/AssistantChatApp.AIAgents.Client/AssistantChatApp.AIAgents.Client.fsproj" />
    <Project Path="AI/AssistantChatApp.AIAgents.ConsoleApp/AssistantChatApp.AIAgents.ConsoleApp.fsproj" />
    <Project Path="AI/AssistantChatApp.AIAgents.Shared.Dotnet/AssistantChatApp.AIAgents.Shared.Dotnet.fsproj" />
    <Project Path="AI/AssistantChatApp.AIAgents.Shared/AssistantChatApp.AIAgents.Shared.fsproj" />
    <Project Path="AI/AssistantChatApp.AIAgents/AssistantChatApp.AIAgents.fsproj" />
    <Project Path="AI/KernelMemory.Client/Enixar.KernelMemory.Client.fsproj" Id="0c083b4f-2adc-4636-8453-83fefc04a85e" />
    <Project Path="AI/KernelMemory.ClientApp/Enixar.KernelMemory.ClientApp.fsproj" Id="a66b9afa-96ce-47e9-b6fc-683367729f36" />
    <Project Path="AI/KernelMemory.WebService/KernelMemory.WebService.csproj" />
  </Folder>
  <Folder Name="/AI/Connectors/" />
  <Folder Name="/AI/Connectors/TogetherAI/">
    <Project Path="../../../Prog/AI/together-dotnet/Together.SemanticKernel/Together.SemanticKernel.csproj" />
    <Project Path="../../../Prog/AI/together-dotnet/Together/Together.csproj" />
  </Folder>
  <Folder Name="/Aspire/">
    <Project Path="AssistantChatApp.Aspire.AppHost/AssistantChatApp.Aspire.AppHost.csproj" />
    <Project Path="AssistantChatApp.Aspire.ServiceDefaults/AssistantChatApp.Aspire.ServiceDefaults.csproj" />
  </Folder>
  <Folder Name="/ChatApp/">
    <Project Path="AssistantChatApp.Client/AssistantChatApp.Client.esproj">
      <Platform Project="AnyCPU" />
      <Build />
      <Deploy />
    </Project>
    <Project Path="AssistantChatApp.Server.AspNetCore.Tests/AssistantChatApp.Server.AspNetCore.Tests.csproj" />
    <Project Path="AssistantChatApp.Server.AspNetCore/AssistantChatApp.Server.AspNetCore.csproj" />
  </Folder>
  <Project Path="AssistantChatApp.Shared/AssistantChatApp.Shared.csproj" />
  <Properties Name="ExtensibilityGlobals" Scope="PostLoad">
    <Property Name="FileExplorer" Value="|Scripts\" />
  </Properties>
</Solution>
