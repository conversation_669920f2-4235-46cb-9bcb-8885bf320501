# Artifact Viewer Module Architecture

## Overview

The Artifact Viewer module is a self-contained system for displaying interactive components (artifacts) in draggable, resizable modal windows. It's designed to handle complex content that would otherwise clutter the chat message interface.

## State Structure and Data Flow

### Core State Architecture

The module uses <PERSON><PERSON> for state management with a hierarchical structure:

```typescript
// Individual viewer state
interface ArtifactViewerState {
  id: string;                    // Unique identifier
  isOpen: boolean;              // Visibility state
  isCollapsed: boolean;         // Collapsed/expanded state
  isFullscreen: boolean;        // Fullscreen mode
  isFocused: boolean;           // Focus state for z-index management
  position: Position;           // { x, y } coordinates
  size: Size;                   // { width, height } dimensions
  zIndex: number;               // Layer ordering
  title: React.ReactNode;       // Display title
  description?: string;         // Optional description
  createdAt: number;            // Creation timestamp
  updatedAt: number;            // Last update timestamp
}

// Global multi-modal state
interface MultiModalState {
  viewers: Record<string, ArtifactViewerState>;  // All viewer instances
  focusedViewerId: string | null;                // Currently focused viewer
  nextZIndex: number;                            // Next available z-index
}
```

### Atom Structure

```mermaid
graph TD
    A[artifactViewerAtomFamily] --> B[Individual Viewer Atoms]
    C[multiModalStateAtom] --> D[Global State]
    E[activeViewersAtom] --> F[Derived: Open Viewers]
    G[focusedViewerAtom] --> H[Derived: Focused Viewer]
    
    B --> I[Viewer State Updates]
    D --> I
    I --> J[UI Re-renders]
```

### Data Flow Patterns

#### 1. Viewer Initialization
```typescript
initializeViewerAtom → artifactViewerAtomFamily(id) → multiModalStateAtom
```

#### 2. Opening a Viewer
```typescript
openViewerAtom → {
  Update individual atom (isOpen: true, isFocused: true)
  Update global state (focusedViewerId, nextZIndex++)
  Trigger UI render
}
```

#### 3. Focus Management
```typescript
focusViewerAtom → {
  Unfocus all other viewers
  Focus target viewer
  Update z-index hierarchy
  Update global focused state
}
```

#### 4. State Synchronization
- Individual atoms hold the source of truth for each viewer
- Global atom maintains references and coordination data
- Derived atoms provide computed views (active viewers, focused viewer)
- Updates flow bidirectionally to maintain consistency

## Component Structure and Relations

### Component Hierarchy

```
ArtifactViewer (Main Container)
├── ArtifactViewerHeader
│   ├── DragHandle
│   ├── Title & Description
│   ├── SizeSwitcher
│   └── Control Buttons (Collapse, Fullscreen, Close)
└── ArtifactViewerContent
    └── User-provided Content
```

### Component Relationships

```mermaid
graph LR
    A[ArtifactTriggerButton] --> B[useArtifactViewer Hook]
    B --> C[ArtifactViewer Component]
    C --> D[ArtifactViewerHeader]
    C --> E[ArtifactViewerContent]
    D --> F[SizeSwitcher]
    
    G[Jotai Atoms] --> B
    H[Responsive Utils] --> C
    I[Position/Size Utils] --> C
```

### Key Component Responsibilities

#### ArtifactViewer (Main Component)
- **Purpose**: Root container managing the entire modal lifecycle
- **Responsibilities**:
  - Integrates with react-rnd for drag/resize functionality
  - Manages responsive behavior and constraints
  - Handles keyboard events (Escape, focus management)
  - Coordinates with state management hooks
  - Applies theming and styling

#### ArtifactViewerHeader
- **Purpose**: Modal header with controls and metadata
- **Responsibilities**:
  - Displays title and description
  - Houses control buttons (collapse, fullscreen, close)
  - Integrates size switcher component
  - Provides drag handle for modal movement
  - Adapts to responsive breakpoints

#### SizeSwitcher
- **Purpose**: Utility component for discrete size selection
- **Responsibilities**:
  - Presents predefined size options
  - Handles size change events
  - Provides visual feedback for current size
  - Supports both dropdown and compact layouts

#### ArtifactTriggerButton
- **Purpose**: Entry point components for opening artifacts
- **Responsibilities**:
  - Provides multiple presentation styles (button, card, inline)
  - Initializes viewer state when needed
  - Handles opening logic
  - Renders the associated ArtifactViewer when active

### Hook Architecture

#### useArtifactViewer
- **Purpose**: Primary interface for individual viewer management
- **Provides**:
  - State access and mutation functions
  - Responsive behavior handling
  - Lifecycle management
  - Event handlers

#### useArtifactViewerManager
- **Purpose**: Coordination layer for multiple viewers
- **Provides**:
  - Global operations (close all, focus management)
  - Keyboard shortcut handling
  - Multi-modal coordination

#### useResponsiveBreakpoints
- **Purpose**: Responsive behavior utilities
- **Provides**:
  - Device type detection
  - Breakpoint-aware behavior
  - Viewport size tracking

## Integration Points

### State Management Integration
- Uses existing Jotai setup from the application
- Follows established atom patterns
- Integrates with Jotai DevTools for debugging

### UI Framework Integration
- Built on Material-UI components and theming
- Respects application theme settings
- Uses consistent spacing and typography

### Responsive Integration
- Leverages MUI breakpoint system
- Provides custom responsive utilities
- Adapts behavior based on device capabilities

## Performance Considerations

### Optimization Strategies
1. **Lazy Initialization**: Viewer atoms only created when needed
2. **Selective Re-renders**: Derived atoms prevent unnecessary updates
3. **Debounced Operations**: Resize and position updates are debounced
4. **Memory Management**: Unused viewers can be garbage collected
5. **Conditional Rendering**: Content only rendered when modals are open

### Scalability Features
- Atom family pattern supports unlimited viewer instances
- Z-index management prevents conflicts
- Efficient focus tracking with minimal state updates
- Responsive calculations cached and reused

## Error Handling

### Resilience Patterns
1. **Graceful Degradation**: Falls back to basic functionality on mobile
2. **Boundary Conditions**: Handles viewport constraints and edge cases
3. **State Recovery**: Maintains consistency even with rapid state changes
4. **Error Boundaries**: Content errors don't crash the entire modal system

### Debugging Support
- Comprehensive TypeScript types for development
- Jotai DevTools integration for state inspection
- Console warnings for configuration issues
- Clear error messages for common mistakes
