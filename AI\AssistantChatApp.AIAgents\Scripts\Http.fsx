#r "nuget: Microsoft.Extensions.Http, 9.0.5"

#load "StringBuilder.fsx"

open System
open System.Threading
open System.Threading.Tasks
open Microsoft.Extensions.DependencyInjection
open Microsoft.Extensions.Http
open System.Net.Http
open System.IO
open StringBuilder

(*
type HttpClientLogger(logger: ILogger<HttpClientLogger>) =

    interface IHttpClientLogger with
        member this.LogRequestFailed(
            context: obj,
            request: HttpRequestMessage,
            response: HttpResponseMessage,
            ``exception``: exn,
            elapsed: TimeSpan): unit
            =
            failwith "Not Implemented"

        member this.LogRequestStart(request: HttpRequestMessage): obj =
            failwith "Not Implemented"

        member this.LogRequestStop(context: obj, request: HttpRequestMessage, response: HttpResponseMessage, elapsed: TimeSpan): unit =
            failwith "Not Implemented"
*)


module LogFormatter =
    open System.Net.Http.Headers

    let formatHeaders (headers: HttpHeaders) =
        stringBuilder {
            for header in headers do
                let headerValues = String.Join(";", header.Value)
                $"{header.Key}: {headerValues}"
        }

    /// Create a string representation of the HTTP request.
    let formatRequest (request: HttpRequestMessage) (body: string option) =
        stringBuilder {
            "----- Request -----"
            $"Method: {request.Method.Method}"
            $"RequestUri: {request.RequestUri.ToString()}"
            "Headers:"
            formatHeaders request.Headers
            if request.Content <> null then
                formatHeaders request.Content.Headers
            match body with
            | Some b ->
                "Body:"
                b
            | None ->
                "Body: <empty>"
                "-------------------"
        }

    /// Create a string representation of the HTTP response.
    let formatResponse (response: HttpResponseMessage) (body: string option) =
        stringBuilder {
            "----- Response -----"
            $"StatusCode: %i{int response.StatusCode}"
            "Headers:"
            formatHeaders response.Headers
            if response.Content <> null then
                formatHeaders response.Content.Headers
            match body with
            | Some b ->
                "Body:"
                b
            | None ->
                "Body: <empty>"
            "--------------------"
        }

/// Defines an interface for writing log output. You can implement it as needed.
type ILogOutput =
    abstract member WriteLog : string -> unit

/// A simple console implementation of ILogOutput
type ConsoleLogOutput() =
    interface ILogOutput with
        member _.WriteLog log = Console.WriteLine log

/// Custom DelegatingHandler that logs request and response data.
type LoggingHandler(logOutput: ILogOutput) = //(logOutput: ILogOutput, ?innerHandler: HttpMessageHandler) =
    inherit DelegatingHandler()
    //inherit DelegatingHandler(
    //    match innerHandler with
    //    | Some h -> h
    //    | None -> new HttpClientHandler() :> HttpMessageHandler
    //)

    member _.BaseSendAsync(request, cancellationToken) =
        base.SendAsync(request, cancellationToken)

    override this.SendAsync(request: HttpRequestMessage, cancellationToken: CancellationToken) : Task<HttpResponseMessage> =
        task {
            // Read and log request body if present.
            let! requestBody =
                if request.Content <> null then
                    request.Content.ReadAsStringAsync()
                else
                    Task.FromResult ""

            // Format the request log message.
            let requestLog =
                let body =
                    if String.IsNullOrWhiteSpace requestBody
                    then None
                    else Some requestBody
                LogFormatter.formatRequest request body
            // Output the request log.
            logOutput.WriteLog(requestLog)

            // Send the request down the pipeline and await the response.
            let! response = this.BaseSendAsync(request, cancellationToken)

            // Read and log response body if present.
            let! responseBody =
                if response.Content <> null then
                    response.Content.ReadAsStringAsync()
                else
                    Task.FromResult ""

            // Format the response log message.
            let responseLog =
                let body =
                    if String.IsNullOrWhiteSpace responseBody
                    then None
                    else Some responseBody
                LogFormatter.formatResponse response body
            // Output the response log.
            logOutput.WriteLog(responseLog)

            return response
        }

let addHttpLoggingHandler (services: IServiceCollection) =
    services.AddTransient<LoggingHandler>(fun sp ->
        new LoggingHandler(ConsoleLogOutput())
    )
    |> fun _ -> services

let addNamedHttpClient (name: string) (services: IServiceCollection) =
    services
        .AddHttpClient(name)
        .RemoveAllLoggers()
        .AddHttpMessageHandler(fun () ->
                new LoggingHandler(ConsoleLogOutput())
                :> DelegatingHandler
            )
    |> fun _ -> services

let getHttpClientByName (name: string) (services: IServiceProvider) =
    services.GetRequiredService<IHttpClientFactory>()
        .CreateClient(name)
