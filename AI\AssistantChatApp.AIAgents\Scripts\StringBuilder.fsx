open System.Text

type StringBuilderCE() =
    member _.<PERSON>eld(line: string) =
        fun (sb: StringBuilder) -> sb.AppendLine(line) |> ignore

    member _.Yield(()) =
        fun (_: StringBuilder) -> ()

    member _.For(s: seq<'a>, body: 'a -> StringBuilder -> unit) =
        fun (sb: StringBuilder) ->
            for item in s do
                body item sb

    member _.Combine(f1, f2) =
        fun (sb: StringBuilder) ->
            f1 sb
            f2 sb

    member _.Delay(f: unit -> _ ) = f()

    member _.Zero() =
        fun (_: StringBuilder) -> ()

    member _.Run(f: StringBuilder -> unit) =
        let sb = StringBuilder()
        f sb
        sb.ToString()

let stringBuilder = StringBuilderCE()
