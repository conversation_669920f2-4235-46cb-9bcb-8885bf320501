import { atom } from 'jotai';
import { atomFamily } from 'jotai/utils';
import {
  ArtifactViewerState,
  MultiModalState,
  Position,
  Size,
  BASE_Z_INDEX,
  DEFAULT_INITIAL_POSITION,
  DEFAULT_INITIAL_SIZE
} from './types';

/**
 * Creates initial state for an artifact viewer
 */
const createInitialViewerState = (
  id: string,
  title: React.ReactNode,
  description?: string,
  initialPosition?: Position,
  initialSize?: Size
): ArtifactViewerState => ({
  id,
  isOpen: false,
  isCollapsed: false,
  isFullscreen: false,
  isFocused: false,
  position: initialPosition || DEFAULT_INITIAL_POSITION,
  size: initialSize || DEFAULT_INITIAL_SIZE,
  zIndex: BASE_Z_INDEX,
  title,
  description,
  createdAt: Date.now(),
  updatedAt: Date.now(),
});

/**
 * Atom family for managing individual artifact viewer states
 * Each artifact gets its own atom identified by artifactId
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const artifactViewerAtomFamily = atomFamily((_artifactId: string) =>
  atom<ArtifactViewerState | null>(null)
);

/**
 * Atom for managing the global multi-modal state
 */
export const multiModalStateAtom = atom<MultiModalState>({
  viewers: {},
  focusedViewerId: null,
  nextZIndex: BASE_Z_INDEX + 1,
});

/**
 * Derived atom to get all active (open) viewers
 */
export const activeViewersAtom = atom((get) => {
  const multiModalState = get(multiModalStateAtom);
  return Object.values(multiModalState.viewers).filter(viewer => viewer.isOpen);
});

/**
 * Derived atom to get the currently focused viewer
 */
export const focusedViewerAtom = atom((get) => {
  const multiModalState = get(multiModalStateAtom);
  const { focusedViewerId, viewers } = multiModalState;
  return focusedViewerId ? viewers[focusedViewerId] || null : null;
});

/**
 * Atom to initialize a new artifact viewer
 */
export const initializeViewerAtom = atom(
  null,
  (get, set, params: {
    artifactId: string;
    title: React.ReactNode;
    description?: string;
    initialPosition?: Position;
    initialSize?: Size;
  }) => {
    const { artifactId, title, description, initialPosition, initialSize } = params;
    const viewerAtom = artifactViewerAtomFamily(artifactId);
    const currentState = get(viewerAtom);

    if (!currentState) {
      const newState = createInitialViewerState(
        artifactId,
        title,
        description,
        initialPosition,
        initialSize
      );
      set(viewerAtom, newState);

      // Update multi-modal state
      const multiModalState = get(multiModalStateAtom);
      set(multiModalStateAtom, {
        ...multiModalState,
        viewers: {
          ...multiModalState.viewers,
          [artifactId]: newState,
        },
      });
    }
  }
);

/**
 * Atom to open an artifact viewer
 */
export const openViewerAtom = atom(
  null,
  (get, set, artifactId: string) => {
    const viewerAtom = artifactViewerAtomFamily(artifactId);
    const currentState = get(viewerAtom);

    if (currentState) {
      const multiModalState = get(multiModalStateAtom);
      const updatedState = {
        ...currentState,
        isOpen: true,
        isFocused: true,
        zIndex: multiModalState.nextZIndex,
        updatedAt: Date.now(),
      };

      set(viewerAtom, updatedState);
      set(multiModalStateAtom, {
        viewers: {
          ...multiModalState.viewers,
          [artifactId]: updatedState,
        },
        focusedViewerId: artifactId,
        nextZIndex: multiModalState.nextZIndex + 1,
      });
    }
  }
);

/**
 * Atom to close an artifact viewer
 */
export const closeViewerAtom = atom(
  null,
  (get, set, artifactId: string) => {
    const viewerAtom = artifactViewerAtomFamily(artifactId);
    const currentState = get(viewerAtom);

    if (currentState) {
      const updatedState = {
        ...currentState,
        isOpen: false,
        isFocused: false,
        updatedAt: Date.now(),
      };

      set(viewerAtom, updatedState);

      const multiModalState = get(multiModalStateAtom);
      const updatedViewers = {
        ...multiModalState.viewers,
        [artifactId]: updatedState,
      };

      // If this was the focused viewer, find the next one to focus
      let newFocusedViewerId = null;
      if (multiModalState.focusedViewerId === artifactId) {
        const activeViewers = Object.values(updatedViewers)
          .filter(viewer => viewer.isOpen)
          .sort((a, b) => b.zIndex - a.zIndex);

        if (activeViewers.length > 0) {
          newFocusedViewerId = activeViewers[0].id;
        }
      }

      set(multiModalStateAtom, {
        ...multiModalState,
        viewers: updatedViewers,
        focusedViewerId: newFocusedViewerId,
      });
    }
  }
);

/**
 * Atom to focus an artifact viewer
 */
export const focusViewerAtom = atom(
  null,
  (get, set, artifactId: string) => {
    const multiModalState = get(multiModalStateAtom);
    const currentViewer = multiModalState.viewers[artifactId];

    if (currentViewer && currentViewer.isOpen) {
      // Update the focused viewer's z-index and focus state
      const updatedViewer = {
        ...currentViewer,
        isFocused: true,
        zIndex: multiModalState.nextZIndex,
        updatedAt: Date.now(),
      };

      // Unfocus all other viewers
      const updatedViewers = Object.keys(multiModalState.viewers).reduce((acc, id) => {
        acc[id] = {
          ...multiModalState.viewers[id],
          isFocused: id === artifactId,
        };
        return acc;
      }, {} as Record<string, ArtifactViewerState>);

      updatedViewers[artifactId] = updatedViewer;

      // Update the individual viewer atom
      const viewerAtom = artifactViewerAtomFamily(artifactId);
      set(viewerAtom, updatedViewer);

      // Update multi-modal state
      set(multiModalStateAtom, {
        viewers: updatedViewers,
        focusedViewerId: artifactId,
        nextZIndex: multiModalState.nextZIndex + 1,
      });
    }
  }
);

/**
 * Atom to update viewer state
 */
export const updateViewerStateAtom = atom(
  null,
  (get, set, artifactId: string, updates: Partial<ArtifactViewerState>) => {
    const viewerAtom = artifactViewerAtomFamily(artifactId);
    const currentState = get(viewerAtom);

    if (currentState) {
      const updatedState = {
        ...currentState,
        ...updates,
        updatedAt: Date.now(),
      };

      set(viewerAtom, updatedState);

      const multiModalState = get(multiModalStateAtom);
      set(multiModalStateAtom, {
        ...multiModalState,
        viewers: {
          ...multiModalState.viewers,
          [artifactId]: updatedState,
        },
      });
    }
  }
);

/**
 * Atom to toggle collapse state
 */
export const toggleCollapseAtom = atom(
  null,
  (get, set, artifactId: string) => {
    const viewerAtom = artifactViewerAtomFamily(artifactId);
    const currentState = get(viewerAtom);

    if (currentState) {
      set(updateViewerStateAtom, artifactId, {
        isCollapsed: !currentState.isCollapsed,
      });
    }
  }
);

/**
 * Atom to toggle fullscreen state
 */
export const toggleFullscreenAtom = atom(
  null,
  (get, set, artifactId: string) => {
    const viewerAtom = artifactViewerAtomFamily(artifactId);
    const currentState = get(viewerAtom);

    if (currentState) {
      set(updateViewerStateAtom, artifactId, {
        isFullscreen: !currentState.isFullscreen,
      });
    }
  }
);
