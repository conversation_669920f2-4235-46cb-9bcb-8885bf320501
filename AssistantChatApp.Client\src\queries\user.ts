import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { userApi } from "../api/userApi";

export interface UseGetUsersProps {
  queryParams?: { page: number; limit: number; search: string },
  enabled?: boolean,
}

export const userQueryKeys = {
  users: (params?: { page: number; limit: number; search: string }) =>
    params ? ["users", params.page, params.limit, params.search] : ["users"],
  user: (userId: string) => ["user", userId],
  aiAssistants: ["aiAssistants"],
};

export const useGetUsers = ({ queryParams, enabled }: UseGetUsersProps) => {
  return useQuery({
    queryKey: userQueryKeys.users(queryParams),
    queryFn: () =>
      userApi.getUsers(
        queryParams?.page,
        queryParams?.limit,
        queryParams?.search,
      ),
    enabled: enabled,
  });
};

export const useGetUser = (userId: string) => {
  return useQuery({
    queryKey: userQueryKeys.user(userId),
    queryFn: () => userApi.getUser(userId),
  });
};

export const useTryGetUser = (userId: string | undefined) => {
  return useQuery({
    queryKey: userQueryKeys.user(userId ?? "undefined"),
    queryFn: () => userId ? userApi.getUser(userId) : Promise.resolve(undefined),
  });
};

export const useTryGetUserSuspense = (userId: string | undefined) => {
  return useSuspenseQuery({
    queryKey: userQueryKeys.user(userId ?? "undefined"),
    queryFn: () => userId ? userApi.getUser(userId) : Promise.resolve(undefined),
  });
};

export const useGetAIAssistants = (enabled?: boolean) => {
  return useQuery({
    queryKey: userQueryKeys.aiAssistants,
    queryFn: () => userApi.getAIAssistants(),
    enabled: enabled,
  });
};
