/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * @import {} from 'mdast-util-directive'
 * @import {Root} from 'mdast'
 */
// import { visit } from "unist-util-visit";
// import { Directives } from "mdast-util-directive";
// import { Root } from "mdast";
import React from "react";
import { ErrorBoundary, type FallbackProps } from "react-error-boundary";
import ReactMarkdown, {
  type Components,
  type ExtraProps,
} from "react-markdown";
import remarkDirective from "remark-directive";
import remarkGfm from "remark-gfm";
import remarkMdx from "remark-mdx";
import rehypeRaw from "rehype-raw";
import {
  Link,
  Typography,
  Stack,
  IconButton,
  Snackbar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import {
  ExpandMore as ExpandMoreIcon,
  Replay as ReplayIcon,
  ContentCopy as ContentCopyIcon,
} from "@mui/icons-material";
import { UserMention } from "./UserMention";
import type { Message } from "../../types";
import { MessageComponents } from "./interactiveComponents";
import { mdDirectiveToHtmlPlugin } from "../../features/markdown/interactiveComponents/mdDirectiveToHtmlRemarkPlugin";
import {
  remarkThinkBlock,
  thinkBlockHandler,
} from "../../features/markdown/thinkBlock";
import { useSendMessage } from "../../mutations/message";
import { ReasoningBlock } from "./ReasoningBlock";

interface MessageReactMarkdownViewProps {
  message: Message;
}

type MessagePreviewRemarkMarkdownComponents = Components & {
  mention: React.ComponentType<object & ExtraProps>;
  think: React.ComponentType<React.PropsWithChildren<ExtraProps>>;
};

/** Replace `@`-based mentions with `mention` tags */
const replaceMentionsWithTags = (message: Message) => {
  if (!message.mentions || message.mentions.length === 0) {
    return message.content;
  }

  // This is a simple implementation - in a real app, you'd want to use a proper parser
  let processedContent = message.content;
  message.mentions.forEach((userId) => {
    const mentionRegex = new RegExp(`@${userId}`, "g");
    processedContent = processedContent.replace(
      mentionRegex,
      `<Mention userId="${userId}" />`
    );
  });

  return processedContent;
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const replaceMentionsWithMarkdownLinks = (
  messageText: string,
  mentions?: string[]
) => {
  if (!mentions || mentions.length === 0) {
    return messageText;
  }

  let processedContent = messageText;
  mentions.forEach((userId) => {
    const mentionRegex = new RegExp(`@${userId}`, "g");
    processedContent = processedContent.replace(
      mentionRegex,
      `[mention:${userId}](user-mention)`
    );
  });

  return processedContent;
};

const replaceThinkBlockWithMarkdownQuote = (text: string) => {
  const result = text.replace(
    /<think>([\s\S]+?)<\/think>/g,
    (_, inner: string) =>
      inner
        .trim()
        .split(/\r?\n/)
        .map((line) => `> ${line}`)
        .join("\n")
  );
  return result;
};

// const userMentionPlugin = () => {
//   /*
//    * @param {Root} tree
//    *   Tree.
//    * @param {VFile} file
//    *   File.
//    * @returns {undefined}
//    *   Nothing.
//    */
//   return (tree: any, file: unknown) => {
//     visit(tree, function (node) {
//       const n = node as Directives;
//       if (n.type === "textDirective" && n.name === "mention") {
//         const userId = n.attributes?.userId;
//         return {
//           type: "link",
//           url: "user-mention",
//           children: [{ type: "text", value: `mention:${userId}` }],
//         };
//       }
//     });
//   };
// };

export const replaceMentionDirectivesWithMarkdownLinks = (
  messageText: string
) => {
  const mentionRegex = /:mention\[([^\]]+)\]/g;
  return messageText.replace(mentionRegex, (match, userId) => {
    return `[mention:${userId}](user-mention)`;
  });
};

const messagePreviewComponents: MessagePreviewRemarkMarkdownComponents = {
  p: ({ node, ref, ...props }) => (
    <Typography component="p" variant="body1" {...props} />
  ),
  a: ({ node, ref, href, children, ...props }) => {
    if (href === "user-mention") {
      const userId = String(children).replace("mention:", "");
      return <UserMention userId={userId} />;
    }
    return (
      <Link href={href} target="_blank" rel="noopener noreferrer" {...props}>
        {children}
      </Link>
    );
  },
  mention: ({ node }) => {
    if (!node) return undefined;
    const firstChild = node.children[0];
    if (firstChild.type === "text") {
      const userId = firstChild.value;
      return <UserMention userId={userId} />;
    }
  },
  think: ({ children }) => {
    return <ReasoningBlock>{children}</ReasoningBlock>;
  },
};

const useMessageInteractiveComponents = (message: Message) => {
  const { chatId, author: messageAuthor } = message;
  const { isPending: isSendingMessage, mutate: sendMessage } = useSendMessage();

  const sendMessageText = React.useCallback(
    (text: string | undefined) => {
      // console.debug("Submit message text from interactive component:", text);
      if (!isSendingMessage && text !== undefined) {
        sendMessage({
          chatId,
          textContent: text,
          mentions: [messageAuthor.id],
        });
      }
    },
    [chatId, messageAuthor.id, isSendingMessage, sendMessage]
  );

  const interactiveComponents =
    MessageComponents.useMessageComponents(sendMessageText);

  return interactiveComponents;
};

const useMessageComponents = (message: Message) => {
  const interactiveComponents = useMessageInteractiveComponents(message);
  return React.useMemo(
    () => ({
      ...messagePreviewComponents,
      ...interactiveComponents,
    }),
    [interactiveComponents]
  );
};

const useAdditionallyProcessedMessage = (message: Message) => {
  const processedMessageText = React.useMemo(
    () => replaceThinkBlockWithMarkdownQuote(message.content),
    [message.content]
  );

  return React.useMemo(() => {
    return {
      ...message,
      content: processedMessageText,
    };
  }, [message, processedMessageText]);
};

const MarkdownFallback: React.FC<FallbackProps & { raw: string }> = ({
  error,
  resetErrorBoundary,
  raw,
}) => {
  const [copied, setCopied] = React.useState(false);

  const copy = async () => {
    try {
      await navigator.clipboard.writeText(raw);
      setCopied(true);
      setTimeout(() => setCopied(false), 1500);
    } catch {
      // ignore
    }
  };

  return (
    <Stack spacing={1} sx={{ p: 1 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Typography color="error">Failed to render message</Typography>
        <Stack direction="row" spacing={1}>
          <IconButton onClick={resetErrorBoundary} aria-label="retry">
            <ReplayIcon />
          </IconButton>
          <IconButton onClick={copy} aria-label="copy">
            <ContentCopyIcon />
          </IconButton>
        </Stack>
      </Stack>

      <Typography variant="caption" color="text.secondary">
        {error?.message}
      </Typography>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="body2">Show raw markdown</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography
            component="pre"
            sx={{
              whiteSpace: "pre-wrap",
              fontFamily: "monospace",
              maxHeight: 240,
              overflow: "auto",
            }}
          >
            {raw}
          </Typography>
        </AccordionDetails>
      </Accordion>

      <Snackbar open={copied} message="Copied" autoHideDuration={1500} />
    </Stack>
  );
};

export const ReactMarkdownMessageView: React.FC<
  MessageReactMarkdownViewProps
> = ({ message }) => {
  //const processedMessage = useAdditionallyProcessedMessage(message);
  const messageContent = message.content;
  const components = useMessageComponents(message);
  return (
    <ErrorBoundary
      FallbackComponent={(props) => (
        <MarkdownFallback {...props} raw={messageContent} />
      )}
      onError={(error, info) =>
        console.error("Message rendering error:", { error, info })
      }
    >
      <ReactMarkdown
        remarkPlugins={[
          remarkGfm,
          // remarkMdx,
          remarkDirective,
          mdDirectiveToHtmlPlugin,
          // remarkThinkBlock,
        ]}
        rehypePlugins={[rehypeRaw]} // A way to detect `<think>` blocks as HTML rather than text. It's a workaround until remark plugin-based solution is fixed.
        // remarkRehypeOptions={{ handlers: { thinkBlock: thinkBlockHandler } }}
        components={components}
      >
        {messageContent}
      </ReactMarkdown>
    </ErrorBoundary>
  );
};
