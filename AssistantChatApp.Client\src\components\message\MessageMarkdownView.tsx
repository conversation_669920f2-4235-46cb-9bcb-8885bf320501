/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * @import {} from 'mdast-util-directive'
 * @import {Root} from 'mdast'
 */
// import { visit } from "unist-util-visit";
// import { Directives } from "mdast-util-directive";
// import { Root } from "mdast";
import React from "react";
import ReactMarkdown, {
  type Components,
  type ExtraProps,
} from "react-markdown";
import remarkDirective from "remark-directive";
import remarkGfm from "remark-gfm";
import remarkMdx from "remark-mdx";
import rehypeRaw from "rehype-raw";
import { Link, Typography } from "@mui/material";
import { UserMention } from "./UserMention";
import type { Message } from "../../types";
import { MessageComponents } from "./interactiveComponents";
import { mdDirectiveToHtmlPlugin } from "../../features/markdown/interactiveComponents/mdDirectiveToHtmlRemarkPlugin";
import { useSendMessage } from "../../mutations/message";
import { ReasoningContainer } from "./ReasoningTextView";

interface MessageReactMarkdownViewProps {
  message: Message;
}

type MessagePreviewRemarkMarkdownComponents = Components & {
  mention: React.ComponentType<object & ExtraProps>;
  think: React.ComponentType<object & ExtraProps>;
};

/** Replace `@`-based mentions with `mention` tags */
const replaceMentionsWithTags = (message: Message) => {
  if (!message.mentions || message.mentions.length === 0) {
    return message.content;
  }

  // This is a simple implementation - in a real app, you'd want to use a proper parser
  let processedContent = message.content;
  message.mentions.forEach((userId) => {
    const mentionRegex = new RegExp(`@${userId}`, "g");
    processedContent = processedContent.replace(
      mentionRegex,
      `<Mention userId="${userId}" />`
    );
  });

  return processedContent;
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const replaceMentionsWithMarkdownLinks = (
  messageText: string,
  mentions?: string[]
) => {
  if (!mentions || mentions.length === 0) {
    return messageText;
  }

  let processedContent = messageText;
  mentions.forEach((userId) => {
    const mentionRegex = new RegExp(`@${userId}`, "g");
    processedContent = processedContent.replace(
      mentionRegex,
      `[mention:${userId}](user-mention)`
    );
  });

  return processedContent;
};

const replaceThinkBlockWithMarkdownQuote = (text: string) => {
  const result = text.replace(
    /<think>([\s\S]+?)<\/think>/g,
    (_, inner: string) =>
      inner
        .trim()
        .split(/\r?\n/)
        .map(line => `> ${line}`)
        .join("\n")
  );
  return result;
}

// const userMentionPlugin = () => {
//   /*
//    * @param {Root} tree
//    *   Tree.
//    * @param {VFile} file
//    *   File.
//    * @returns {undefined}
//    *   Nothing.
//    */
//   return (tree: any, file: unknown) => {
//     visit(tree, function (node) {
//       const n = node as Directives;
//       if (n.type === "textDirective" && n.name === "mention") {
//         const userId = n.attributes?.userId;
//         return {
//           type: "link",
//           url: "user-mention",
//           children: [{ type: "text", value: `mention:${userId}` }],
//         };
//       }
//     });
//   };
// };

export const replaceMentionDirectivesWithMarkdownLinks = (
  messageText: string
) => {
  const mentionRegex = /:mention\[([^\]]+)\]/g;
  return messageText.replace(mentionRegex, (match, userId) => {
    return `[mention:${userId}](user-mention)`;
  });
};

const messagePreviewComponents: MessagePreviewRemarkMarkdownComponents = {
  // a: ({ node, ...props }) => (
  //   <a
  //     {...props}
  //     target="_blank"
  //     rel="noopener noreferrer"
  //     style={{ color: '#3a7bd5', textDecoration: 'none' }}
  //   />
  // ),
  // mention: ({ userId }) => <UserMention userId={userId as string} />,
  p: ({ node, ref, ...props }) => (
    <Typography component="p" variant="body1" {...props} />
  ),
  a: ({ node, ref, href, children, ...props }) => {
    if (href === "user-mention") {
      const userId = String(children).replace("mention:", "");
      return <UserMention userId={userId} />;
    }
    return (
      <Link href={href} target="_blank" rel="noopener noreferrer" {...props}>
        {children}
      </Link>
    );
  },
  //mention: ({ userId }) => <UserMention userId={userId} />,
  mention: ({ node }) => {
    if (!node) return undefined;
    const firstChild = node.children[0];
    if (firstChild.type === "text") {
      const userId = firstChild.value;
      return <UserMention userId={userId} />;
    }
  },
  blockquote: ({ node, ref, children, ...props }) => {
    return (
      // <details style={{ marginTop: 8, marginBottom: 8 }}>
      //   <summary style={{ cursor: "pointer", userSelect: "none", color: "#555" }}>
      //     Рассуждение
      //   </summary>
      //   <div>
      //     <Typography component="blockquote" variant="body2" sx={{ text }} {...props}>
      //       {children}
      //     </Typography>
      //   </div>
      // </details>
      <ReasoningContainer>
        {children}
      </ReasoningContainer>
    );
  },
  // think: ({ node }) => {
  //   if (!node || !node.children || node.children.length === 0) return undefined;
  //   const reasoningText =
  //     node.children.reduce((acc, child) => {
  //       if (child.type === "text") {
  //         acc += child.value;
  //       }
  //       return acc;
  //     }, "");
  //   if (reasoningText.trim().length === 0) return undefined;

  //   return (
  //     <details style={{ marginTop: 8 }}>
  //       <summary style={{ cursor: 'pointer', userSelect: 'none', color: '#555' }}>
  //         Show reasoning
  //       </summary>
  //       <div style={{ marginTop: 4, paddingLeft: 16, color: '#666' }}>
  //         {reasoningText}
  //       </div>
  //     </details>
  //   );
  // }
};

const useMessageInteractiveComponents = (message: Message) => {
  const { chatId, author: messageAuthor } = message;
  const { isPending: isSendingMessage, mutate: sendMessage } = useSendMessage();

  const sendMessageText = React.useCallback(
    (text: string) => {
      // console.debug("Submit message text from interactive component:", text);
      if (!isSendingMessage) {
        sendMessage({
          chatId,
          textContent: text,
          mentions: [messageAuthor.id],
        });
      }
    },
    [chatId, messageAuthor.id, isSendingMessage, sendMessage]
  );

  const interactiveComponents = MessageComponents.useMessageComponents(sendMessageText);

  return interactiveComponents;
};

const useMessageComponents = (message: Message) => {
  const interactiveComponents = useMessageInteractiveComponents(message);
  return React.useMemo(
    () => ({
      ...messagePreviewComponents,
      ...interactiveComponents,
    }),
    [interactiveComponents]
  );
};

export const ReactMarkdownMessageView: React.FC<
  MessageReactMarkdownViewProps
> = ({ message }) => {
  const processedMessageText = replaceThinkBlockWithMarkdownQuote(
    message.content
  );
  const processedMessage = {
    ...message,
    content: processedMessageText,
  };
  const components = useMessageComponents(processedMessage);
  return (
    <ReactMarkdown
      remarkPlugins={[
        remarkGfm,
        // remarkMdx,
        remarkDirective,
        mdDirectiveToHtmlPlugin,
      ]}
      rehypePlugins={[rehypeRaw]}
      components={components}
    >
      {processedMessageText}
    </ReactMarkdown>
  );
};
