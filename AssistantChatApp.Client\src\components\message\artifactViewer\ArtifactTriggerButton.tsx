import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardActions,
  Typography,
  Box,
  Chip,
  Stack,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Launch as LaunchIcon,
  Visibility as ViewIcon,
  OpenInNew as OpenIcon,
  Article as ArticleIcon,
} from '@mui/icons-material';
import { ArtifactTriggerButtonProps } from './types';
import { useArtifactViewer } from './hooks';
import ArtifactViewer from './ArtifactViewer';

/**
 * Button component that triggers opening an artifact viewer
 */
export const ArtifactTriggerButton: React.FC<ArtifactTriggerButtonProps> = ({
  artifactId,
  title,
  description,
  children,
  variant = 'outlined',
  size = 'medium',
  icon = <LaunchIcon />,
  disabled = false,
  className,
  onOpen,
}) => {
  const { open, isOpen } = useArtifactViewer(artifactId, title, description);

  const handleOpen = () => {
    open();
    onOpen?.();
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={handleOpen}
        disabled={disabled}
        startIcon={icon}
        className={className}
        sx={{
          textTransform: 'none',
          '&:hover': {
            backgroundColor: 'action.hover',
          },
        }}
      >
        {title}
      </Button>

      {/* Render the artifact viewer when open */}
      {isOpen && (
        <ArtifactViewer
          artifactId={artifactId}
          title={title}
          description={description}
          autoFocus
        >
          {children}
        </ArtifactViewer>
      )}
    </>
  );
};

/**
 * Card-style trigger button for more prominent display
 */
export const ArtifactTriggerCard: React.FC<ArtifactTriggerButtonProps> = ({
  artifactId,
  title,
  description,
  children,
  variant = 'outlined',
  icon = <ArticleIcon />,
  disabled = false,
  className,
  onOpen,
}) => {
  const { open, isOpen } = useArtifactViewer(artifactId, title, description);

  const handleOpen = () => {
    open();
    onOpen?.();
  };

  return (
    <>
      <Card
        variant={variant === 'contained' ? 'elevation' : 'outlined'}
        className={className}
        sx={{
          maxWidth: 400,
          cursor: disabled ? 'default' : 'pointer',
          transition: 'all 0.2s ease-in-out',
          '&:hover': disabled ? {} : {
            boxShadow: 2,
            transform: 'translateY(-1px)',
          },
          opacity: disabled ? 0.6 : 1,
        }}
        onClick={disabled ? undefined : handleOpen}
      >
        <CardContent sx={{ pb: 1 }}>
          <Stack direction="row" alignItems="flex-start" spacing={2}>
            <Box
              sx={{
                color: 'primary.main',
                display: 'flex',
                alignItems: 'center',
                mt: 0.5,
              }}
            >
              {icon}
            </Box>

            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography
                variant="subtitle1"
                component="h3"
                sx={{
                  fontWeight: 600,
                  mb: description ? 0.5 : 0,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {title}
              </Typography>

              {description && (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                  }}
                >
                  {description}
                </Typography>
              )}
            </Box>
          </Stack>
        </CardContent>

        <CardActions sx={{ pt: 0, justifyContent: 'space-between' }}>
          <Chip
            label="Interactive"
            size="small"
            variant="outlined"
            color="primary"
            sx={{ fontSize: '0.75rem' }}
          />

          <Tooltip title="Open in modal">
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                if (!disabled) handleOpen();
              }}
              disabled={disabled}
              sx={{ color: 'primary.main' }}
            >
              <OpenIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </CardActions>
      </Card>

      {/* Render the artifact viewer when open */}
      {isOpen && (
        <ArtifactViewer
          artifactId={artifactId}
          title={title}
          description={description}
          autoFocus
        >
          {children}
        </ArtifactViewer>
      )}
    </>
  );
};

/**
 * Compact inline trigger button
 */
export const ArtifactTriggerInline: React.FC<ArtifactTriggerButtonProps> = ({
  artifactId,
  title,
  description,
  children,
  icon = <ViewIcon />,
  disabled = false,
  className,
  onOpen,
}) => {
  const { open, isOpen } = useArtifactViewer(artifactId, title, description);

  const handleOpen = () => {
    open();
    onOpen?.();
  };

  return (
    <>
      <Box
        component="span"
        sx={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: 0.5,
          px: 1,
          py: 0.5,
          borderRadius: 1,
          backgroundColor: 'action.hover',
          cursor: disabled ? 'default' : 'pointer',
          transition: 'all 0.2s ease-in-out',
          '&:hover': disabled ? {} : {
            backgroundColor: 'primary.50',
            color: 'primary.main',
          },
          opacity: disabled ? 0.6 : 1,
        }}
        className={className}
        onClick={disabled ? undefined : handleOpen}
      >
        <Box sx={{ fontSize: 16, display: 'flex', alignItems: 'center' }}>
          {icon}
        </Box>
        <Typography
          variant="caption"
          sx={{
            fontWeight: 500,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: 120,
          }}
        >
          {title}
        </Typography>
      </Box>

      {/* Render the artifact viewer when open */}
      {isOpen && (
        <ArtifactViewer
          artifactId={artifactId}
          title={title}
          description={description}
          autoFocus
        >
          {children}
        </ArtifactViewer>
      )}
    </>
  );
};

export default ArtifactTriggerButton;
