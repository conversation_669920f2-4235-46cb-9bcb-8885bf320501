import React from 'react';
import { MDXProvider as BaseMDXProvider } from '@mdx-js/react';
import { UserMention } from '../message/UserMention';
import { AnchorHTMLAttributes, HTMLAttributes } from 'react';
import { Typo<PERSON>, Link } from '@mui/material';

export const useMdxComponents = () => {
  return React.useMemo(() => ({
    Mention: UserMention,
    // Add any other custom components or HTML element overrides here
    a: (props: AnchorHTMLAttributes<HTMLAnchorElement>) => (
      <Link href={props.href} target="_blank" rel="noopener noreferrer" {...props} />
    ),
    p: (props: HTMLAttributes<HTMLParagraphElement>) => (
      <Typography component="p" variant="body1" {...props} />
    ),
  }), []);
};

export const MDXProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const components = useMdxComponents();

  return (
    <BaseMDXProvider components={components}>
      {children}
    </BaseMDXProvider>
  );
};
