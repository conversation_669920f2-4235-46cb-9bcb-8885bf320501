import React, { useState } from 'react';
import { Container, Typo<PERSON>, Box, TextField, InputAdornment, Chip, Divider, Stack } from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import { Chat, ChatFilters } from '../types';
import { ChatListItem } from '../components/chat/ChatListItem';
import ChatListSkeleton from '../components/chat/ChatListSkeleton';
import NoChatFound from '../components/chat/NoChatFound';
import NewChatButton from '../components/chat/NewChatButton';
import { useGetChats } from '../queries/chat';
import { useTranslation } from "react-i18next";

const ChatListPage: React.FC = () => {
  const { t } = useTranslation("chats");
  const [filters, setFilters] = useState<ChatFilters>({
    page: 1,
    limit: 20,
    search: '',
  });

  const [activeTags, setActiveTags] = useState<string[]>([]);

  // Fetch chats with current filters
  const { data, isLoading, error } = useGetChats(filters);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters({ ...filters, search: e.target.value, page: 1 });
  };

  // Handle tag selection
  const handleTagClick = (tag: string) => {
    if (activeTags.includes(tag)) {
      // Remove tag
      const updatedTags = activeTags.filter(t => t !== tag);
      setActiveTags(updatedTags);
      setFilters({ ...filters, tags: updatedTags, page: 1 });
    } else {
      // Add tag
      const updatedTags = [...activeTags, tag];
      setActiveTags(updatedTags);
      setFilters({ ...filters, tags: updatedTags, page: 1 });
    }
  };

  // Extract all unique tags from chats
  const allTags = React.useMemo(() => {
    const tags = new Set<string>();
    if (data?.data) {
      data.data.forEach((chat: Chat) => {
        chat.tags?.forEach(tag => tags.add(tag));
      });
    }
    return Array.from(tags);
  }, [data?.data]);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          {t('chats')}
        </Typography>
        <NewChatButton />
      </Box>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder={t('searchPlaceholder')}
          value={filters.search}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          variant="outlined"
          sx={{ mb: 2 }}
        />

        {allTags.length > 0 && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
            {allTags.map(tag => (
              <Chip
                key={tag}
                label={tag}
                clickable
                color={activeTags.includes(tag) ? 'primary' : 'default'}
                onClick={() => handleTagClick(tag)}
              />
            ))}
          </Box>
        )}
      </Box>

      <Divider sx={{ mb: 3 }} />

      {isLoading ? (
        <ChatListSkeleton count={5} />
      ) : error ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography color="error">
            {t('chatsLoadingError')}
          </Typography>
        </Box>
      ) : data?.data.length === 0 ? (
        <NoChatFound />
      ) : (
        <Stack spacing={0.5}>
          {data?.data.map((chat: Chat) => (
            <ChatListItem key={chat.id} chat={chat} />
          ))}
        </Stack>
      )}
    </Container>
  );
};

export default ChatListPage;
