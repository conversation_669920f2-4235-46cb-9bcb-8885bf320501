{
    "ApiProviders": {
        "TogetherAI": {
            "Id": "TogetherAI",
            "ApiEndpoint": "https://api.together.xyz/v1/",
            "ApiKey": "<API-key>"
        },
        "OpenRouter": {
            "Id": "OpenRouter",
            "ApiEndpoint": "https://openrouter.ai/api/v1/",
            "ApiKey": "<API-key>"
        }
    },
    "KernelMemory": {
        "DocumentStorageType": "SimpleFileStorage",
        "Retrieval": {
            "EmbeddingGeneratorType": "Ollama",
            "MemoryDbType": "SimpleVectorDb"
        },
        "Services": {
            "Ollama": {
                "Endpoint": "http://localhost:11434",
                "EmbeddingModel": "nomic-embed-text:latest"
            },
            "SimpleFileStorage": {
                "Directory": "./agent1_docs",
                "StorageType": "Disk"
            },
            "SimpleVectorDb": {
                "Directory": "./agent1_vectors",
                "StorageType": "Disk"
            }
        }
    },
    "Agents": {
        "AgentUser1": {
            "Id": "AgentUser1",
            "Name": "Agent User #1",
            "ProviderId": "TogetherAI",
            "ModelId": "meta-llama/Llama-3.2-3B-Instruct-Turbo",
            "MainSystemPromptFilePath": "path/to/kb/main-system-prompt.txt",
            "KernelMemoryPluginSettings": {
                "Enabled": true,
                "SearchSettings": {
                    "Limit": 10,
                    "MinRelevance": 0.5
                }
            },
            "KbSettings": {
                "Enabled": true,
                "RootDirPath": "path/to/kb/directory"
            }
        }
    },
    "KernelMemoryAccessSettings": {
        "KernelMemoryServiceUrl": "http://kernel-memory-service"
    },
    "Serilog": {
        "HttpClientLogging": {
            "LogMode": "LogFailures", // Default to less verbose
            "RequestHeaderLogMode": "LogNone",
            "RequestBodyLogMode": "LogNone",
            "ResponseHeaderLogMode": "LogNone",
            "ResponseBodyLogMode": "LogFailures",
            "RequestBodyLogTextLengthLimit": 2000,
            "ResponseBodyLogTextLengthLimit": 2000,
            "MaskFormat": "*****",
            "MaskedProperties": [ "*password*", "*token*", "*api_key*", "*secret*" ]
        }
    }
}
