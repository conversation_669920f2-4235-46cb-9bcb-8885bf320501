# Partition


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**text** | **string** |  | [optional] [default to undefined]
**relevance** | **number** |  | [optional] [default to undefined]
**partitionNumber** | **number** |  | [optional] [default to undefined]
**sectionNumber** | **number** |  | [optional] [default to undefined]
**lastUpdate** | **string** |  | [optional] [default to undefined]
**tags** | **{ [key: string]: Array&lt;string&gt;; }** |  | [optional] [default to undefined]

## Example

```typescript
import { Partition } from './api';

const instance: Partition = {
    text,
    relevance,
    partitionNumber,
    sectionNumber,
    lastUpdate,
    tags,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
