# MemoryQuery


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**index** | **string** |  | [optional] [default to undefined]
**question** | **string** |  | [optional] [default to undefined]
**filters** | **Array&lt;{ [key: string]: Array&lt;string&gt;; }&gt;** |  | [optional] [default to undefined]
**minRelevance** | **number** |  | [optional] [default to undefined]
**stream** | **boolean** |  | [optional] [default to undefined]
**args** | **{ [key: string]: any | null; }** |  | [optional] [default to undefined]

## Example

```typescript
import { MemoryQuery } from './api';

const instance: MemoryQuery = {
    index,
    question,
    filters,
    minRelevance,
    stream,
    args,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
