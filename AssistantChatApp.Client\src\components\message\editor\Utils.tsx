import React from "react";
import {
  <PERSON>,
  But<PERSON>,
  CircularP<PERSON>ress,
  Icon<PERSON>utton,
  Tooltip,
  Fab,
} from "@mui/material";
import {
  Bold as BoldIcon,
  Italic as ItalicIcon,
  Link as LinkIcon,
  List as ListIcon,
  Code as CodeIcon,
  AtSign as AtSignIcon,
  Send as SendIcon,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { SendHorizonalIcon } from "lucide-react";

export const MessageFormattingToolbar: React.FC = () => {
  return (
    <Box sx={{ display: "flex", gap: 0.5 }}>
      <Tooltip title="Bold">
        <IconButton size="small">
          <BoldIcon size={18} />
        </IconButton>
      </Tooltip>
      <Tooltip title="Italic">
        <IconButton size="small">
          <ItalicIcon size={18} />
        </IconButton>
      </Tooltip>
      <Tooltip title="Link">
        <IconButton size="small">
          <LinkIcon size={18} />
        </IconButton>
      </Tooltip>
      <Tooltip title="List">
        <IconButton size="small">
          <ListIcon size={18} />
        </IconButton>
      </Tooltip>
      <Tooltip title="Code">
        <IconButton size="small">
          <CodeIcon size={18} />
        </IconButton>
      </Tooltip>
      <Tooltip title="Mention">
        <IconButton size="small">
          <AtSignIcon size={18} />
        </IconButton>
      </Tooltip>
    </Box>
  );
};

export const SendMessageButton: React.FC<{
  onClick: () => void;
  isPending: boolean;
  isDisabled: boolean;
}> = ({ onClick, isPending, isDisabled }) => {
  const { t } = useTranslation("messages");
  return (
    <Button
      variant="contained"
      endIcon={
        isPending ? <CircularProgress size={16} /> : <SendIcon size={16} />
      }
      onClick={onClick}
      disabled={isDisabled || isPending}
    >
      {isPending ? t("sending_message") : t("send_message")}
    </Button>
  );
};

export const SendMessageFab: React.FC<{
  onClick: () => void;
  isPending: boolean;
  isDisabled: boolean;
}> = ({ onClick, isPending, isDisabled }) => {
  return (
    <Fab
      size="small"
      color="primary"
      onClick={onClick}
      disabled={isDisabled || isPending}
    >
      {isPending ? <CircularProgress size={24} /> : <SendHorizonalIcon />}
    </Fab>
  );
};
