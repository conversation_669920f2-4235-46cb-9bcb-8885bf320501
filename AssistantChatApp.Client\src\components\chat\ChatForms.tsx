import React from "react";
import {
  Box,
  TextField,
  Autocomplete,
  CircularProgress,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useChatFormState, useUsers } from "./hooks";

export type ChatFormState = ReturnType<typeof useChatFormState>;
export type ChatFormStateValues = ChatFormState["formValues"];

/** Generic Chat form component props */
export interface ChatFormProps {
  formState: ChatFormState;
  onSubmit: (values: ChatFormStateValues) => void;
  isSubmitting: boolean;
  withSubmitButton: boolean;
  /** Specifies whether the form is active, visible to control whether the component's queries are enabled or not. */
  isActive: boolean;
}

export interface ChatFormDialogProps
  extends Omit<ChatFormProps, "withSubmitButton" | "isActive"> {
  isOpened: boolean;
  onClose: () => void;
  dialogTitle: string;
}

export const ChatForm: React.FC<ChatFormProps> = ({
  formState,
  onSubmit,
  isSubmitting,
  withSubmitButton,
}) => {
  const { t } = useTranslation(["chats", "common", "users"]);
  const formValues = formState.formValues;
  const { users, usersLoading } = useUsers();

  const handleAddTag = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && formValues.inputTag.trim() !== "") {
      if (!formValues.tags.includes(formValues.inputTag.trim())) {
        formState.setTags([...formValues.tags, formValues.inputTag.trim()]);
      }
      formState.setInputTag("");
      event.preventDefault();
    }
  };

  const handleDeleteTag = (tagToDelete: string) => {
    formState.setTags(formValues.tags.filter((tag) => tag !== tagToDelete));
  };

  const handleSubmit = () => onSubmit(formState.formValues);

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
      <TextField
        autoFocus
        margin="dense"
        label={t("chatForm.title")}
        fullWidth
        required
        value={formValues.title}
        onChange={(e) => formState.setTitle(e.target.value)}
      />
      <TextField
        margin="dense"
        label={t("chatForm.description")}
        fullWidth
        multiline
        rows={2}
        value={formValues.description}
        onChange={(e) => formState.setDescription(e.target.value)}
      />

      <Autocomplete
        multiple
        id="participants"
        options={users || []}
        loading={usersLoading}
        getOptionLabel={(option) => option.displayName}
        value={formValues.participants}
        onChange={(_, newValue) => {
          formState.setSelectedParticipants(newValue);
        }}
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        renderOption={({ key, ...props }, option) => (
          <li key={option.id} {...props}>
            {option.displayName} {option.isAI ? `(${t("users:ai")})` : ""}
          </li>
        )}
        renderInput={(params) => (
          <TextField
            {...params}
            margin="dense"
            label={t("chatForm.participants")}
            fullWidth
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <>
                  {usersLoading ? (
                    <CircularProgress color="inherit" size={20} />
                  ) : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            }}
          />
        )}
        sx={{ mt: 1 }}
      />

      <TextField
        margin="dense"
        label={t("chatForm.tags")}
        fullWidth
        value={formValues.inputTag}
        onChange={(e) => formState.setInputTag(e.target.value)}
        onKeyDown={handleAddTag}
        helperText={t("chatForm.tagsHelper")}
        sx={{ mt: 1 }}
      />

      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, mt: 1 }}>
        {formValues.tags.map((tag) => (
          <Chip key={tag} label={tag} onDelete={() => handleDeleteTag(tag)} />
        ))}
      </Box>

      {withSubmitButton && (
        <Button type="submit" variant="contained" disabled={isSubmitting}>
          {isSubmitting ? t("common:submitting") : t("common:submit")}
        </Button>
      )}
    </Box>
  );
};

export const ChatFormDialog: React.FC<ChatFormDialogProps> = ({
  formState,
  onSubmit,
  isSubmitting,
  isOpened,
  onClose,
  dialogTitle,
}) => {
  const { t } = useTranslation(["chats", "common"]);
  const handleSubmit = () => onSubmit(formState.formValues);
  return (
    <Dialog open={isOpened} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>{dialogTitle}</DialogTitle>
      <DialogContent>
        <ChatForm
          formState={formState}
          onSubmit={onSubmit}
          isSubmitting={isSubmitting}
          withSubmitButton={false}
          isActive={isOpened}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>{t("common:cancel")}</Button>
        <Button
          type="submit"
          onClick={handleSubmit}
          variant="contained"
          disabled={!formState.formValues.title.trim() || isSubmitting}
        >
          {isSubmitting ? t("common:submitting") : t("common:submit")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
