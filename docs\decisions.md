## AI Agents Service + Chat App Backend (ASP.NET Core) Interaction

- Store agent-related message data (function calls and function call results) in the Chat App's DB. For that, introduce several new entities, and update existing `Message` entity by introducing `Contents` attribute to store multiple entities of base `Content` type:
```mermaid
erDiagram
    Message
    Content
    TextContent
    FunctionCallContent
    FunctionResultContent

    Content ||--|| TextContent : subtype
    Content ||--|| FunctionCallContent : subtype
    Content ||--|| FunctionResultContent : subtype
    Message ||--o{ Content : contains
```

## Ask AI Assistant: Complete interaction flow

Components involved:
- **Chat App FE**: Chat App Frontend (client, UI)
- **Chat App BE**: Chat App Backend (server, ASP.NET Core app)
  - **AI Agents service**: special service inside Chat App BE responsible for interaction with AI Assistant Agent app
  - **Message Service**: special service inside Chat App BE responsible for managing chat messages
- **AI Assistant Agent App**: standalone .NET app, implements AI agents functionality, exposes HTTP API.

1. User sends a message to the chat, mentioning the AI assistant user.
2. Chat App BE detects that the message is addressed to the AI assistant and sends a request to the AI Agents service to generate a response.
3. AI Agents service receives the request, fetches the chat history from the Chat App's DB, and sends it to the AI agent.
4. AI agent processes the chat history and generates a response.
5. AI Agents service receives the response from the AI agent and sends it back to the Chat App backend.
6. Chat App backend receives the response from the AI Agents service. The response can consist of multiple messages, e.g.: intermediate messages such as function calls and function results, and final message with the answer to the user's question.
   1. Chat App BE saves all the messages to the DB. Each response message is marked as authored by AI assistant.
   2. All the intermediate messages are tagged with special `Tool` tag.
   3. Chat App BE responds to the Chat App FE with the list of messages to display to the user. The list includes the original user message and all the messages generated by the AI assistant. In production environment, intermediate messages are filtered out from the response (based on the `Tool` tag presence).
