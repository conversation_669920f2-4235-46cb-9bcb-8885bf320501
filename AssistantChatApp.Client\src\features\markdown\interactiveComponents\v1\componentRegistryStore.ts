import { create } from "zustand";
import { Map } from "immutable";
import type {
  ComponentRegistration,
  ComponentAction,
  MessageId,
  ComponentId,
  ComponentName,
} from "./types";

export interface ComponentRegistryData {
  components: Map<ComponentName, ComponentRegistration>;
  componentStates: Map<ComponentId, unknown>;
  actionHandlers: Map<MessageId, (action: ComponentAction) => void>;
}

export interface ComponentRegistryCommands {
  register<
    TComponentName extends string = ComponentName,
    TComponentActionName extends string = string,
    TData = unknown,
    TProps = unknown
  >(
    name: TComponentName,
    registration: ComponentRegistration<
      TComponentName,
      TComponentActionName,
      TData,
      TProps
    >
  ): void;

  setActionHandler<
    TComponentName extends string = ComponentName,
    TData = unknown
  >(
    messageId: MessageId,
    handler: (action: ComponentAction<TComponentName, TData>) => void
  ): void;

  removeActionHandler(messageId: MessageId): void;

  handleAction<TComponentName extends string = ComponentName, TData = unknown>(
    action: ComponentAction<TComponentName, TData>
  ): void;

  setComponentState<TData = unknown>(
    messageId: MessageId,
    componentId: ComponentId,
    data: TData
  ): void;

  initializeComponent(
    messageId: MessageId,
    componentId: ComponentId,
    componentName: ComponentName
  ): void;

  cleanup(messageId: MessageId): void;
}

export interface ComponentRegistrySelectors {
  getComponentState<TData = unknown>(
    messageId: MessageId,
    componentId: ComponentId
  ): TData;

  getComponent<
    TComponentName extends string = ComponentName,
    TComponentActionName extends string = string,
    TData = unknown,
    TProps = unknown
  >(
    name: TComponentName
  ): ComponentRegistration<TComponentName, TComponentActionName, TData, TProps>;
}

export interface ComponentRegistry
  extends ComponentRegistryCommands,
    ComponentRegistrySelectors {
  state: ComponentRegistryData;
}

const initializeComponentRegistryData: () => ComponentRegistryData = () => ({
  components: Map<ComponentName, ComponentRegistration>(),
  componentStates: Map<ComponentId, unknown>(),
  actionHandlers: Map<MessageId, (action: ComponentAction) => void>(),
});

export const useComponentRegistryStore = create<ComponentRegistry>((set, get) => ({
  state: initializeComponentRegistryData(),

  register<
    TComponentName extends string = ComponentName,
    TComponentActionName extends string = string,
    TData = unknown,
    TProps = unknown
  >(
    name: TComponentName,
    registration: ComponentRegistration<
      TComponentName,
      TComponentActionName,
      TData,
      TProps
    >
  ): void {
    set(({ state: { components, ...rest } }) => ({
      state: {
        ...rest,
        components: components.set(name, registration as ComponentRegistration),
      },
    }));
  },

  setActionHandler<
    TComponentName extends string = ComponentName,
    TData = unknown
  >(
    messageId: MessageId,
    handler: (action: ComponentAction<TComponentName, TData>) => void
  ) {
    set(({ state: { actionHandlers, ...rest } }) => ({
      state: {
        ...rest,
        actionHandlers: actionHandlers.set(
          messageId,
          handler as (action: ComponentAction) => void
        ),
      },
    }));
  },

  removeActionHandler(messageId: MessageId) {
    set(({ state: { actionHandlers, ...rest } }) => ({
      state: {
        ...rest,
        actionHandlers: actionHandlers.delete(messageId),
      },
    }));
  },

  handleAction<TComponentName extends string = ComponentName, TData = unknown>(
    action: ComponentAction<TComponentName, TData>
  ) {
    const {
      state: { actionHandlers, components },
      getComponentState,
      setComponentState,
    } = get();
    const handler = actionHandlers.get(action.messageId);
    if (handler) {
      handler(action);
    }

    // Handle component-specific actions
    const registration = components.get(action.type);
    if (registration?.actionHandlers) {
      const actionHandler = registration.actionHandlers[action.type];
      if (actionHandler) {
        const currentData = getComponentState(
          action.messageId,
          action.componentId
        );
        const newData = actionHandler(action, currentData);
        setComponentState(action.messageId, action.componentId, newData);
      }
    }
  },

  setComponentState<TData = unknown>(
    messageId: MessageId,
    componentId: ComponentId,
    data: TData
  ) {
    set(({ state: { componentStates, ...rest } }) => {
      const key = `${messageId}:${componentId}`;
      return {
        state: {
          ...rest,
          componentStates: componentStates.set(key, data),
        },
      };
    });
  },

  initializeComponent(
    messageId: MessageId,
    componentId: ComponentId,
    componentName: ComponentName
  ) {
    const {
      state: { components },
      setComponentState,
    } = get();
    const registration = components.get(componentName);
    if (registration?.initialData) {
      setComponentState(messageId, componentId, registration.initialData);
    }
  },

  cleanup(messageId: MessageId) {
    // Clean up component states for a specific message
    set(({ state: { componentStates, ...rest } }) => {
      const keysToDelete = Array.from(componentStates.keys()).filter((key) =>
        key.startsWith(`${messageId}:`)
      );
      return {
        state: {
          ...rest,
          componentStates: componentStates.deleteAll(keysToDelete),
        },
      };
    });
    const { removeActionHandler } = get();
    removeActionHandler(messageId);
  },

  getComponentState<TData = unknown>(
    messageId: MessageId,
    componentId: ComponentId
  ): TData {
    const { state } = get();
    const key = `${messageId}:${componentId}`;
    return state.componentStates.get(key) as TData;
  },

  getComponent<
    TComponentName extends string = ComponentName,
    TComponentActionName extends string = string,
    TData = unknown,
    TProps = unknown
  >(name: TComponentName) {
    const { state } = get();
    return state.components.get(name) as ComponentRegistration<
      TComponentName,
      TComponentActionName,
      TData,
      TProps
    >;
  },
}));
