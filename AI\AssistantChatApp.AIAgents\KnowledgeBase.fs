module AssistantChatApp.AIAgents.KnowledgeBase

open System
open System.IO
open System.ComponentModel
open System.ComponentModel.DataAnnotations
open Microsoft.SemanticKernel
open Microsoft.Extensions.FileProviders
open Microsoft.Extensions.Options
open Microsoft.Extensions.Hosting


module FunctionNames =

    let [<Literal>] GetPromptByName = "get_prompt_by_name"
    let [<Literal>] GetFile = "get_file"

module ErrorMessages =

    let PromptOfGivenNameNotFound (promptName: string) =
        $"Error: Prompt named %A{promptName} doesn't exist."

    let DataFileOfGivenNameNotFound (dataFileName: string) =
        $"Error: Data file named %A{dataFileName} doesn't exist."

    let FileOfGivenNameNotFound (fileName: string) =
        $"Error: File named %A{fileName} doesn't exist."

let getKbFullDirPath
    (contentRootDirPath: string)
    (settings: FileAccessPluginSettings)
    =
    Utils.getFullDirPath contentRootDirPath settings.RootDirPath

let createKnowledgeBaseFileProvider
    (contentRootDirPath: string)
    (settings: FileAccessPluginSettings)
    =
    let kbDirPath = getKbFullDirPath contentRootDirPath settings
    new PhysicalFileProvider(kbDirPath)


/// Previously named "KnowledgeBasePlugin".
type FileAccessPlugin(
    settings: FileAccessPluginSettings,
    env: IHostEnvironment
) =
    let kbFileProvider = createKnowledgeBaseFileProvider env.ContentRootPath settings

    let readFile (name: string) =
        let info = kbFileProvider.GetFileInfo(name)
        if info.Exists then
            use stream = info.CreateReadStream()
            use reader = new StreamReader(stream)
            let content = reader.ReadToEnd()
            Some content
        else
            None

    (*
    [<KernelFunction(FunctionNames.GetPromptByName)>]
    [<Description "Fetch prompt by its name/identifier">]
    *)
    member this.GetPromptByName(name: string) =
        readFile name
        |> Option.defaultWith (fun () -> ErrorMessages.PromptOfGivenNameNotFound name)

    [<KernelFunction(FunctionNames.GetFile)>]
    [<Description "Fetch a file by its name (with extension).">]
    member this.GetFile(fileName: string) =
        readFile fileName
        |> Option.defaultWith (fun () -> ErrorMessages.FileOfGivenNameNotFound fileName)

    member this.FileProvider = kbFileProvider

    member this.Dispose() =
        kbFileProvider.Dispose()

    interface IDisposable with
        member this.Dispose () =
            this.Dispose()
