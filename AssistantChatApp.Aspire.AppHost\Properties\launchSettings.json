{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "https://localhost:17232;http://localhost:15199", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DOTNET_ENVIRONMENT": "Development", "DOTNET_DASHBOARD_OTLP_ENDPOINT_URL": "https://localhost:21185", "DOTNET_RESOURCE_SERVICE_ENDPOINT_URL": "https://localhost:22156"}}, "http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:15199", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DOTNET_ENVIRONMENT": "Development", "DOTNET_DASHBOARD_OTLP_ENDPOINT_URL": "http://localhost:19177", "DOTNET_RESOURCE_SERVICE_ENDPOINT_URL": "http://localhost:20008", "ASPIRE_ALLOW_UNSECURED_TRANSPORT": "true"}}}}