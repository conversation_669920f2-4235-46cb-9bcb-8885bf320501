The task is to add internationalization (i18n) support to the frontend of the chat app (`./AssistantChatApp.Client`).
I've already started the work, now it's need to be finished.
What I've done:
1. Added `i18next`, `react-i18next`, `i18next-browser-languagedetector`, `date-fns`, `date-fns-tz` packages.
2. Added `src/i18n.ts` file with `i18next` configuration. Then imported it in `src/main.tsx`.
3. Added `src/i18next.d.ts` file with `i18next` module augmentation.
4. Added `src/locales/en` and `src/locales/ru` folders for JSON translation files. Each folder contains following files:
   - `common.json` for common translations
   - `auth.json` for authentication-related translations
   - `chats.json` for chat-related translations
   - `messages.json` for message-related translations
   - `users.json` for user-related translations
5. Applied i18n to the following modules/files:
   - `src/routes/index.tsx`
   - `src/pages/LoginPage.tsx`
   - `src/pages/ChatListPage.tsx`
   - `src/pages/ChatPage.tsx`
   - `src/pages/NotFoundPage.tsx`

Now your task is to apply the i18n in the similar manner to the components in `src/components` folder.

You should do this file by file. The process is approximately as follows:
1. Add import to `useTranslation` hook from `react-i18next` to the module/file.
2. For each component in the file:
   1. Look whether it contains any hard-coded strings that are intended to be visible in the UI to end-users. If there are such strings, to the component body, get the `t` function, add `useTranslation` hook call with an array of needed namespaces passed to it. You should estimate the namespaces needed based on the component context and the strings used in it.
   2. Then, for each of the hard-coded strings:
      1. Decide on the translation key and the translation namespace.
      2. Replace the hard-coded string with the proper `t` function call from `react-i18next`, with the translation key and namespace (if `useTranslation` is called only with one namespace, the namespace argument in `t` function can be omitted).
      3. Remember the key and namespace used (for later steps).
3. Finally, given all the translation keys and namespaces used in the file, update corresponding translation files for every of the supported locales (i.e., JSON files in `src/locales/en` and `src/locales/ru` directories).

<!-- Besides the translations, you should handle DateTime usages (i.e. the timestamps displayed in the UI). You should replace them with the `format` function calls from `date-fns` library based on the locale used. The locale can be obtained from the `i18n` value returned alongside the `t` function from the `useTranslation` hook). -->

---

The process is:
1. To the module imports section, add import of `useTranslation` hook from `react-i18next`.
2. Look at every React component in the file. For each component:
   1. Look at the file and find all the hard-coded strings that are intended to be visible in the UI to end-users.
   2. If there are such strings, in the component body, get `t` function from `useTranslation` hook with a proper translation namespace passed to it. Pick one of the defined namespace (`common`, `auth`, `chats`, `messages`, `users`) based on the component context.
   3. Then, for each of the hard-coded strings:
      1. Decide on the translation key. It should be unique within the translation namespace, and should reflect the meaning of the string.
      2. Decide on the translation namespace. It should reflect the module or the component where the string is used. Pick one of the defined namespace (`common`, `auth`, `chats`, `messages`, `users`).
      3. As each namespace corresponds to a JSON file, add the translation key to the appropriate JSON file. You should do this for all the supported locales (for now it's only `en` and `ru`).
- Replace the hard-coded strings with with `t` function calls from `react-i18next`. For that you should introduce the appropriate translation key.
- Add the corresponding translations to `src/locales/en` and `src/locales/ru` folders.
