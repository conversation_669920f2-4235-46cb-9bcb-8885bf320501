Please, design me a React component that represents message writing progress. I want to add such component to my chat app to show in the UI when some other user types a message, so that the current user could know about that. The component provide a name of the user who is writing a message. For example, it could be a text message like: "<User Name> is responding...". Although, some animation is desirable. Here the ideas for animation I see it (although, you're free to choose something else if you have a better vision):
- The animation of the final three dots, i.e., the dots are added one by one with a small time interval in a loop
- Some kind of "running highlighting" animation of the whole message (or combined with the previous dots animation)
- Some kind of pencil/pen/etc. animation after the progress message

Tech stack requirements:
- Language: TypeScript
- UI kit: MUI
- Icons: `@mui/icons-material` or `lucide-react`.
