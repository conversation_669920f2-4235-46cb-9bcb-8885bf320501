import { Request, Response } from 'express';
import { login, getUserById } from '../services/auth';
import { AuthRequest } from '../types';

/**
 * Login controller
 * @route POST /auth/login
 */
export async function loginController(req: Request, res: Response) {
  const { email, password } = req.body;
  const result = await login({ email, password });
  
  res.status(200).json(result);
}

/**
 * Get current user controller
 * @route GET /auth/me
 */
export async function getMeController(req: AuthRequest, res: Response) {
  if (!req.userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }
  
  const user = await getUserById(req.userId);
  
  res.status(200).json(user);
}
