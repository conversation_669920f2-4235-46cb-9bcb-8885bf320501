import type {
  ComponentRegistration,
  ComponentAction,
  MessageId,
  ComponentId,
  ComponentName,
} from "./types";

export class ComponentRegistry {
  private components = new Map<ComponentName, ComponentRegistration>();
  private componentStates = new Map<ComponentId, unknown>();
  private actionHandlers = new Map<
    MessageId,
    (action: ComponentAction) => void
  >();

  register<TComponentName extends string = ComponentName, TComponentActionName extends string = string, TData = unknown, TProps = unknown>(
    name: TComponentName,
    registration: ComponentRegistration<TComponentName, TComponentActionName, TData, TProps>
  ) {
    this.components.set(
      name,
      registration as ComponentRegistration
    );
  }

  getComponent<TComponentName extends string = ComponentName, TComponentActionName extends string = string, TData = unknown, TProps = unknown>(name: TComponentName) {
    return this.components.get(name) as ComponentRegistration<TComponentName, TComponentActionName, TData, TProps>;
  }

  setActionHandler<TComponentName extends string = ComponentName, TData = unknown>(
    messageId: MessageId,
    handler: (action: ComponentAction<TComponentName, TData>) => void
  ) {
    this.actionHandlers.set(
      messageId,
      handler as (action: ComponentAction) => void
    );
  }

  removeActionHandler(messageId: MessageId) {
    this.actionHandlers.delete(messageId);
  }

  handleAction<TComponentName extends string = ComponentName, TData = unknown>(action: ComponentAction<TComponentName, TData>) {
    const handler = this.actionHandlers.get(action.messageId);
    if (handler) {
      handler(action);
    }

    // Handle component-specific actions
    const registration = this.components.get(action.type);
    if (registration?.actionHandlers) {
      const actionHandler = registration.actionHandlers[action.type];
      if (actionHandler) {
        const currentData = this.getComponentState(
          action.messageId,
          action.componentId
        );
        const newData = actionHandler(action, currentData);
        this.setComponentState(action.messageId, action.componentId, newData);
      }
    }
  }

  getComponentState<TData = unknown>(
    messageId: MessageId,
    componentId: ComponentId
  ): TData {
    const key = `${messageId}:${componentId}`;
    return this.componentStates.get(key) as TData;
  }

  setComponentState<TData = unknown>(
    messageId: MessageId,
    componentId: ComponentId,
    data: TData
  ) {
    const key = `${messageId}:${componentId}`;
    this.componentStates.set(key, data);
  }

  initializeComponent(
    messageId: MessageId,
    componentId: ComponentId,
    componentName: ComponentName
  ) {
    const registration = this.components.get(componentName);
    if (registration?.initialData) {
      this.setComponentState(messageId, componentId, registration.initialData);
    }
  }

  cleanup(messageId: MessageId) {
    // Clean up component states for a specific message
    const keysToDelete = Array.from(this.componentStates.keys()).filter((key) =>
      key.startsWith(`${messageId}:`)
    );
    keysToDelete.forEach((key) => this.componentStates.delete(key));

    this.removeActionHandler(messageId);
  }
}
