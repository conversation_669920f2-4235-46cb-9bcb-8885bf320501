# Assistant Chat App Backend - Technical Overview

This document provides a technical overview of the Assistant Chat App backend, including its module structure and database schema.

## Application Architecture

The backend follows a layered architecture pattern with clear separation of concerns:

```
AssistantChatApp.Server/
├── src/
│   ├── auth/           # Authentication utilities
│   ├── config/         # Configuration management
│   ├── controllers/    # Request handlers
│   ├── db/             # Database connection and schema
│   ├── middleware/     # Express middleware
│   ├── routes/         # API route definitions
│   ├── services/       # Business logic
│   ├── tests/          # Test files
│   ├── types/          # TypeScript type definitions
│   ├── utils/          # Utility functions
│   ├── validation/     # Request validation schemas
│   ├── app.ts          # Express application setup
│   └── index.ts        # Application entry point
```

## Module Hierarchy and Functionality

### 1. Entry Point and Configuration

- **index.ts**: Application entry point that starts the Express server
- **app.ts**: Express application setup with middleware and route registration
- **config/**: Environment-specific configuration management

### 2. API Layer

#### Routes (`routes/`)

The routes directory contains Express routers that define API endpoints:

- **index.ts**: Main router that combines all route modules
- **auth.ts**: Authentication routes (`/auth/login`, `/auth/me`)
- **users.ts**: User management routes (`/users`, `/users/:userId`, `/users/ai-assistants`)
- **chats.ts**: Chat management routes (`/chats`, `/chats/:chatId`)
- **messages.ts**: Message routes (`/chats/:chatId/messages`, `/chats/:chatId/messages/:messageId`)

#### Controllers (`controllers/`)

Controllers handle HTTP requests and responses:

- **auth.ts**: Authentication controllers (login, get current user)
- **users.ts**: User controllers (get users, get user by ID, get AI assistants)
- **chats.ts**: Chat controllers (CRUD operations for chats)
- **messages.ts**: Message controllers (CRUD operations for messages)

### 3. Business Logic Layer

#### Services (`services/`)

Services contain the core business logic:

- **auth.ts**: Authentication logic (login, token generation)
- **users.ts**: User management logic (get users, search users)
- **chats.ts**: Chat management logic (create, update, delete chats, manage participants and tags)
- **messages.ts**: Message handling logic (send, update, delete messages, handle mentions)

### 4. Data Access Layer

#### Database (`db/`)

Database connection and schema definitions:

- **index.ts**: Database connection setup using Drizzle ORM
- **schema/**: Database schema definitions
  - **users.ts**: Users table schema
  - **chats.ts**: Chats table schema
  - **messages.ts**: Messages table schema
  - **chatParticipants.ts**: Chat participants table schema (many-to-many)
  - **tags.ts**: Tags and chat_tags tables schema (many-to-many)
  - **mentions.ts**: Mentions table schema (many-to-many)
- **migrations/**: SQL migration files
- **seed.ts**: Database seeding script for development

### 5. Authentication System

#### Auth (`auth/`)

Authentication utilities:

- **jwt.ts**: JWT token generation and verification
- **password.ts**: Password hashing and comparison
- **middleware.ts**: Authentication middleware for protected routes

### 6. Cross-Cutting Concerns

#### Middleware (`middleware/`)

Express middleware:

- **error.ts**: Global error handling middleware
- **validation.ts**: Request validation middleware using Zod

#### Validation (`validation/`)

Request validation schemas using Zod:

- **auth.ts**: Authentication request validation
- **users.ts**: User request validation
- **chats.ts**: Chat request validation
- **messages.ts**: Message request validation

#### Utils (`utils/`)

Utility functions:

- **errors.ts**: Custom error classes
- **pagination.ts**: Pagination utilities
- **logger.ts**: Logging utilities

#### Types (`types/`)

TypeScript type definitions:

- **index.ts**: Shared type definitions for the application

## Database Schema

The database uses PostgreSQL with the following schema:

### Users Table

Stores user information including authentication details:

```sql
CREATE TABLE "users" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "email" VARCHAR(255) NOT NULL UNIQUE,
  "password" VARCHAR(255) NOT NULL,
  "display_name" VARCHAR(100) NOT NULL,
  "avatar" VARCHAR(255),
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "is_ai" BOOLEAN NOT NULL DEFAULT FALSE
);
```

- `id`: Unique identifier for the user
- `email`: User's email address (used for authentication)
- `password`: Hashed password
- `display_name`: User's display name
- `avatar`: URL to user's avatar image
- `created_at`: Timestamp when the user was created
- `updated_at`: Timestamp when the user was last updated
- `is_ai`: Boolean flag indicating if the user is an AI assistant

### Chats Table

Stores chat information:

```sql
CREATE TABLE "chats" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "title" VARCHAR(100) NOT NULL,
  "description" VARCHAR(500),
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW()
);
```

- `id`: Unique identifier for the chat
- `title`: Chat title
- `description`: Optional chat description
- `created_at`: Timestamp when the chat was created
- `updated_at`: Timestamp when the chat was last updated

### Messages Table

Stores messages sent in chats:

```sql
CREATE TABLE "messages" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "chat_id" UUID NOT NULL REFERENCES "chats"("id") ON DELETE CASCADE,
  "author_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "content" TEXT NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW()
);
```

- `id`: Unique identifier for the message
- `chat_id`: Foreign key reference to the chat
- `author_id`: Foreign key reference to the user who sent the message
- `content`: Message content
- `created_at`: Timestamp when the message was created
- `updated_at`: Timestamp when the message was last updated

### Chat Participants Table (Many-to-Many)

Links users to chats they participate in:

```sql
CREATE TABLE "chat_participants" (
  "chat_id" UUID NOT NULL REFERENCES "chats"("id") ON DELETE CASCADE,
  "user_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "joined_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  PRIMARY KEY ("chat_id", "user_id")
);
```

- `chat_id`: Foreign key reference to the chat
- `user_id`: Foreign key reference to the user
- `joined_at`: Timestamp when the user joined the chat
- Composite primary key of `chat_id` and `user_id`

### Tags Table

Stores tags that can be applied to chats:

```sql
CREATE TABLE "tags" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" VARCHAR(50) NOT NULL UNIQUE
);
```

- `id`: Unique identifier for the tag
- `name`: Tag name (unique)

### Chat Tags Table (Many-to-Many)

Links tags to chats:

```sql
CREATE TABLE "chat_tags" (
  "chat_id" UUID NOT NULL REFERENCES "chats"("id") ON DELETE CASCADE,
  "tag_id" UUID NOT NULL REFERENCES "tags"("id") ON DELETE CASCADE,
  PRIMARY KEY ("chat_id", "tag_id")
);
```

- `chat_id`: Foreign key reference to the chat
- `tag_id`: Foreign key reference to the tag
- Composite primary key of `chat_id` and `tag_id`

### Mentions Table (Many-to-Many)

Tracks user mentions in messages:

```sql
CREATE TABLE "mentions" (
  "message_id" UUID NOT NULL REFERENCES "messages"("id") ON DELETE CASCADE,
  "user_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  PRIMARY KEY ("message_id", "user_id")
);
```

- `message_id`: Foreign key reference to the message
- `user_id`: Foreign key reference to the mentioned user
- Composite primary key of `message_id` and `user_id`

## Database Relationships

The schema establishes the following relationships:

1. **One-to-Many**:
   - A user can author many messages
   - A chat can contain many messages

2. **Many-to-Many**:
   - Users participate in many chats (via `chat_participants`)
   - Chats have many participants (via `chat_participants`)
   - Chats have many tags (via `chat_tags`)
   - Tags can be applied to many chats (via `chat_tags`)
   - Messages can mention many users (via `mentions`)
   - Users can be mentioned in many messages (via `mentions`)

## Data Flow

1. Client sends a request to an API endpoint
2. Express router directs the request to the appropriate controller
3. Controller validates the request and calls the appropriate service
4. Service implements business logic and interacts with the database via Drizzle ORM
5. Service returns data to the controller
6. Controller formats the response and sends it back to the client

This architecture ensures separation of concerns, making the codebase maintainable and testable.
