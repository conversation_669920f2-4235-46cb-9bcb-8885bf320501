import React from "react";
import { Avatar, AvatarGroup, SxProps } from "@mui/material";
import { Bot as BotIcon } from "lucide-react";
import { User } from "../../types/index";

interface UserAvatarProps extends Pick<User, "displayName" | "avatar" | "isAI"> {
  sx?: SxProps;
}

interface UserAvatarGroupProps {
  users: User[];
  max?: number;
  sx?: SxProps;
}

export const UserAvatar: React.FC<UserAvatarProps> = ({
  avatar,
  displayName,
  isAI,
  sx,
}) => {
  return (
    <Avatar src={avatar} alt={displayName} sx={sx ?? { width: 32, height: 32 }}>
      {isAI ? (
        <BotIcon />
      ) : (
        displayName.charAt(0).toUpperCase()
      )}
    </Avatar>
  );
};

export const UserAvatarGroup: React.FC<UserAvatarGroupProps> = ({
  users,
  max,
  sx,
}) => {
  return (
    <AvatarGroup max={max} sx={sx}>
      {users.map(({ id, displayName, avatar, isAI }) => (
        <UserAvatar key={id} displayName={displayName} avatar={avatar} isAI={isAI} />
      ))}
    </AvatarGroup>
  );
};
