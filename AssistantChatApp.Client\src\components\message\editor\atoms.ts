import { atom } from "jotai";
import { atomWithMutation, queryClientAtom } from "jotai-tanstack-query";
import { createSendMessageMutationOptions } from "@/mutations/message";
import { isNonEmptyString } from "@/utils/utils";
// import {
//   selectedAIUserAtom,
//   autoMentionSelectedAIUserAtom,
//   messageSendKeyModeAtom,
// } from "@/components/message/interactionSettings/atoms";
import { extractMentions } from "./messageEditorUtils";
import type { MDXEditorRef } from "./types";
import { InteractionSettingsValues } from "../interactionSettings";

export const messageTextContentAtom = atom("");

export const mentionPopupAnchorAtom = atom<HTMLElement | null>(null);
export const mentionSearchQueryAtom = atom("");

export const sendMessageMutationAtom = atomWithMutation((get) => {
  const queryClient = get(queryClientAtom);
  return createSendMessageMutationOptions(queryClient);
});

export const isMessageSubmittableAtom = atom((get) => {
  const messageTextContent = get(messageTextContentAtom);
  const { isPending } = get(sendMessageMutationAtom);
  return isNonEmptyString(messageTextContent) && !isPending;
});

export const resetTextContentAtom = atom(
  null,
  (get, set, editorRef: MDXEditorRef) => {
    set(messageTextContentAtom, "");
    editorRef.current?.setMarkdown("");
  }
);

export const handleMessageSubmitAtom = atom(
  null,
  async (get, set, chatId: string, editorRef: MDXEditorRef, interactionSettings: InteractionSettingsValues) => {
    const isSubmittable = get(isMessageSubmittableAtom);
    if (!isSubmittable) return;

    // const autoMentionSelectedAIUser = get(autoMentionSelectedAIUserAtom);
    // const selectedAIUser = get(selectedAIUserAtom);
    const { autoMentionSelectedAIUser, selectedAIUser } = interactionSettings;
    const messageTextContent = get(messageTextContentAtom);
    const sendMessageMutation = get(sendMessageMutationAtom);
    const mentions = extractMentions(messageTextContent);
    // const resetTextContent = get(resetTextContentAtom);

    // Add the selected AI user mention
    if (autoMentionSelectedAIUser && selectedAIUser) {
      mentions.push(selectedAIUser.id);
    }
    try {
      // console.log("Sending message:", textContent, mentions, chatId);
      await sendMessageMutation.mutateAsync({
        textContent: messageTextContent,
        mentions,
        chatId,
      });
      // Clear the editor after successful send
      set(resetTextContentAtom, editorRef);
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  }
);

export const handleMentionButtonClickAtom = atom(
  null,
  (get, set, event: React.MouseEvent<HTMLElement>) => {
    set(mentionPopupAnchorAtom, event.currentTarget);
  }
);

export const handleMentionPopupCloseAtom = atom(null, (get, set) => {
  set(mentionPopupAnchorAtom, null);
  set(mentionSearchQueryAtom, "");
});

export const handleEditorKeyDownAtom = atom(
  null,
  (
    get,
    set,
    chatId: string,
    editorRef: MDXEditorRef,
    interactionSettings: InteractionSettingsValues,
    event: React.KeyboardEvent
  ) => {
    // const messageSendKeyMode = get(messageSendKeyModeAtom);
    const messageSendKeyMode = interactionSettings.messageSendKeyMode;
    const hanldeSubmitViaKeyboard = () => {
      event.preventDefault();
      event.stopPropagation();
      set(handleMessageSubmitAtom, chatId, editorRef, interactionSettings);
    };
    switch (messageSendKeyMode) {
      case "Ctrl+Enter": {
        if (event.key === "Enter" && (event.ctrlKey || event.metaKey)) {
          hanldeSubmitViaKeyboard();
        }
        return;
      }
      case "Enter": {
        if (event.key === "Enter" && !event.shiftKey) {
          hanldeSubmitViaKeyboard();
        }
        return;
      }
    }
  }
);
