For one of the web services I have, I need a way to more-or-less easily and comfortably use the service. This is for internal needs, thus developing a complete web UI app seems an unreasonable amount of work. I just need a way to view request results in a more user-friendly way than in a console app.
So I have an idea to develop a .NET Interactive Notebook (to be used via VS Code Polyglot Notebook) that will be structured properly and use its formatting capabilities for a more good-looking result output.
I already have an F# library that defines necessary client logic (sets up HTTP client and defines necessary DTOs and handlers).
