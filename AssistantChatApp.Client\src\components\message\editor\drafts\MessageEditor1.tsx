import React, { useState, useCallback } from "react";
import {
  MDXEditor,
  // toolbarPlugin,
  headingsPlugin,
  listsPlugin,
  quotePlugin,
  thematicBreakPlugin,
  markdownShortcutPlugin,
  directivesPlugin,
  usePublisher,
  // useMdastNodeSubscription,
  insertDirective$,
  DirectiveDescriptor,
  // DirectiveNode,
} from "@mdxeditor/editor";
import "@mdxeditor/editor/style.css";
// import { useSendMessage } from "../../mutations/message";
import { useGetChat } from "../../../../queries/chat";
import { useGetUser } from "../../../../queries/user";

import '@mdxeditor/editor/style.css'


// Types
export interface User {
  id: string;
  displayName: string;
  createdAt: string;
}

export interface Message {
  id: string;
  chatId: string;
  author: User;
  content: string;
  mentions?: string[];
}

// User Mention Component - renders the mention in the editor
const UserMentionComponent: React.FC<{
  userId: string;
}> = ({ userId }) => {
  const { data: user, isLoading } = useGetUser(userId);
  const displayName = isLoading ? userId : user?.displayName || "Unknown";

  return (
    <span
      style={{
        backgroundColor: "#e3f2fd",
        color: "#1976d2",
        padding: "2px 6px",
        borderRadius: "12px",
        fontSize: "0.875em",
        fontWeight: 500,
        border: "1px solid #bbdefb",
        display: "inline-block",
        margin: "0 1px",
      }}
      data-user-id={userId}
    >
      @{displayName}
    </span>
  );
};

// User Selection Dropdown Component
const UserSelectionDropdown: React.FC<{
  users: User[];
  onUserSelect: (user: User) => void;
  onClose: () => void;
  isOpen: boolean;
}> = ({ users, onUserSelect, onClose, isOpen }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "absolute",
        top: "100%",
        left: 0,
        right: 0,
        backgroundColor: "white",
        border: "1px solid #ccc",
        borderRadius: "4px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        maxHeight: "200px",
        overflowY: "auto",
        zIndex: 1000,
      }}
    >
      {users.map((user) => (
        <div
          key={user.id}
          style={{
            padding: "8px 12px",
            cursor: "pointer",
            borderBottom: "1px solid #eee",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = "#f5f5f5";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "white";
          }}
          onClick={() => {
            onUserSelect(user);
            onClose();
          }}
        >
          <div style={{ fontWeight: 500 }}>{user.displayName}</div>
          <div style={{ fontSize: "0.875em", color: "#666" }}>
            ID: {user.id}
          </div>
        </div>
      ))}
    </div>
  );
};

// Mention Button Component for Toolbar
const MentionButton: React.FC<{ users: User[] }> = ({ users }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const insertDirective = usePublisher(insertDirective$);

  const handleUserSelect = useCallback(
    (user: User) => {
      insertDirective({
        type: "leafDirective",
        name: "mention",
        attributes: { userId: user.id },
        // children: [{ type: "text", value: user.displayName }],
      });
      setIsDropdownOpen(false);
    },
    [insertDirective],
  );

  return (
    <div style={{ position: "relative", display: "inline-block" }}>
      <button
        type="button"
        style={{
          padding: "6px 12px",
          border: "1px solid #ccc",
          borderRadius: "4px",
          backgroundColor: "white",
          cursor: "pointer",
          fontSize: "14px",
        }}
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = "#f5f5f5";
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = "white";
        }}
      >
        @ Mention
      </button>
      <UserSelectionDropdown
        users={users}
        onUserSelect={handleUserSelect}
        onClose={() => setIsDropdownOpen(false)}
        isOpen={isDropdownOpen}
      />
    </div>
  );
};

// Directive Descriptor for User Mentions
const mentionDirectiveDescriptor: DirectiveDescriptor = {
  name: "mention",
  type: "leafDirective",
  testNode: (node) => {
    return node.name === "mention";
  },
  attributes: ["userId"],
  hasChildren: true,
  Editor: ({ mdastNode }) => {
    const userId = mdastNode.attributes?.userId || "";
    // const displayName = mdastNode.children?.[0]?.value || "Unknown User";

    return (
      <UserMentionComponent userId={userId} />
    );
  },
};

// Custom Toolbar Component
const CustomToolbar: React.FC<{ users: User[] }> = ({ users }) => {
  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        gap: "8px",
        padding: "8px",
        borderBottom: "1px solid #e0e0e0",
        backgroundColor: "#f9f9f9",
      }}
    >
      <MentionButton users={users} />
    </div>
  );
};

// Utility function to extract user mentions from markdown content
const extractMentions = (content: string): string[] => {
  const mentionRegex = /:mention\[([^\]]*)\]\{userId="([^"]+)"\}/g;
  const mentions: string[] = [];
  let match;

  while ((match = mentionRegex.exec(content)) !== null) {
    const userId = match[2];
    if (userId && !mentions.includes(userId)) {
      mentions.push(userId);
    }
  }

  return mentions;
};

// // Mock hooks (you would replace these with your actual implementations)
// const useChatParticipants = (): User[] => {
//   return [
//     { id: "user1", displayName: "Alice Johnson", createdAt: "2024-01-01" },
//     { id: "user2", displayName: "Bob Smith", createdAt: "2024-01-02" },
//     { id: "user3", displayName: "Charlie Brown", createdAt: "2024-01-03" },
//     { id: "user4", displayName: "Diana Prince", createdAt: "2024-01-04" },
//   ];
// };

const useSendMessage = () => {
  return {
    mutate: (data: { textContent: string; mentions: string[]; chatId: string }) => {
      console.log("Sending message:", data);
      // Your actual implementation would send the message to the server
    },
    isPending: false,
  };
};

// Main Message Editor Component
export const MessageEditor: React.FC<{
  chatId: string;
}> = ({ chatId }) => {
  const [content, setContent] = useState("");
  const { data: chat } = useGetChat(chatId);
  const users = chat?.participants || [];
  const sendMessage = useSendMessage();

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();

      if (!content.trim()) return;

      const mentions = extractMentions(content);

      sendMessage.mutate({
        textContent: content.trim(),
        mentions,
        chatId,
      });

      // Clear the editor after sending
      setContent("");
    },
    [content, chatId, sendMessage],
  );

  return (
    <div
      style={{
        border: "1px solid #e0e0e0",
        borderRadius: "8px",
        overflow: "hidden",
        maxWidth: "800px",
        margin: "20px auto",
      }}
    >
      <CustomToolbar users={users} />

      <form onSubmit={handleSubmit}>
        <div style={{ minHeight: "120px" }}>
          <MDXEditor
            key={content} // Force re-render when content changes
            markdown={content}
            onChange={setContent}
            plugins={[
              headingsPlugin(),
              listsPlugin(),
              quotePlugin(),
              thematicBreakPlugin(),
              markdownShortcutPlugin(),
              directivesPlugin({
                directiveDescriptors: [mentionDirectiveDescriptor],
              }),
            ]}
            contentEditableClassName="prose"
          />
        </div>

        <div
          style={{
            padding: "12px",
            borderTop: "1px solid #e0e0e0",
            backgroundColor: "#f9f9f9",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div style={{ fontSize: "0.875em", color: "#666" }}>
            {content.trim() && <>Mentions: {extractMentions(content).length}</>}
          </div>

          <div style={{ display: "flex", gap: "8px" }}>
            <button
              type="button"
              onClick={() => setContent("")}
              style={{
                padding: "8px 16px",
                border: "1px solid #ccc",
                borderRadius: "4px",
                backgroundColor: "white",
                cursor: "pointer",
              }}
              disabled={!content.trim()}
            >
              Clear
            </button>

            <button
              type="submit"
              style={{
                padding: "8px 16px",
                border: "none",
                borderRadius: "4px",
                backgroundColor: "#1976d2",
                color: "white",
                cursor: "pointer",
                fontWeight: 500,
              }}
              disabled={!content.trim() || sendMessage.isPending}
            >
              {sendMessage.isPending ? "Sending..." : "Send Message"}
            </button>
          </div>
        </div>
      </form>

      {/* Debug info */}
      {content && (
        <div
          style={{
            padding: "12px",
            borderTop: "1px solid #e0e0e0",
            backgroundColor: "#f0f0f0",
            fontSize: "0.875em",
          }}
        >
          <strong>Debug - Raw Content:</strong>
          <pre
            style={{
              marginTop: "8px",
              padding: "8px",
              backgroundColor: "white",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "0.8em",
              overflow: "auto",
            }}
          >
            {content}
          </pre>
          <strong>Extracted Mentions:</strong>{" "}
          {JSON.stringify(extractMentions(content))}
        </div>
      )}
    </div>
  );
};

// Demo App Component
export const Demo: React.FC = () => {
  return (
    <div style={{ padding: "20px" }}>
      <h1>Message Editor with User Mentions</h1>
      <p>Click the "@ Mention" button to add user mentions to your message.</p>

      <MessageEditor chatId="chat-123" />
    </div>
  );
};
