import {
  DirectiveDescriptor,
  MDXEditorMethods,
  insertDirective$,
  usePublisher,
} from "@mdxeditor/editor";
import React from "react";
import { useSendMessage } from "../../mutations/message";
import { User } from "../../types";
import { MentionDirectiveEditor } from "./editor/UserMentionDirectiveEditor";
import { LeafDirective } from "mdast-util-directive";
import { isNonEmptyString } from "../common/utils";

// Mention Directive Descriptor
export const mentionDirectiveDescriptor: DirectiveDescriptor = {
  name: "mention",
  type: "leafDirective",
  testNode: (node) => {
    return node.name === "mention";
  },
  attributes: [],
  hasChildren: true,
  Editor: MentionDirectiveEditor,
};

// Utility function to extract mentions from markdown content
export const extractMentions = (content: string): string[] => {
  const mentionRegex = /:mention\[([^\]]+)\]/g;
  const mentions: string[] = [];
  let match;

  while ((match = mentionRegex.exec(content)) !== null) {
    mentions.push(match[1]);
  }

  return Array.from(new Set(mentions)); // Remove duplicates
};

/** Doesn't work yet 🤷‍♂️ */
//TODO: Investigate why this is not working (it has no effect, i.e. doesn't insert the mention directives)
export const useUserSelectHandler = () => {
  const insertDirective = usePublisher(insertDirective$);
  const onUserSelect = React.useCallback(
    (user: User) => {
      console.log("Selected user:", user);
      insertDirective({
        name: "mention",
        type: "leafDirective",
        attributes: { userId: user.id },
        children: [
          {
            type: "text",
            value: user.id,
          },
        ],
      } as LeafDirective);
    },
    [insertDirective],
  );
  return onUserSelect;
};

/** Naive implementation that leverages MDXEditor instance's `insertMarkdown` method (accessed via `ref`) */
export const useUserSelectHandler1 =
  ({
    editorRef,
    setTextContent,
  }: Pick<MessageEditorState, "editorRef" | "setTextContent">) =>
  (user: User) => {
    const mentionMarkdown = `:mention[${user.id}]`;

    // Insert the mention at cursor position
    if (editorRef.current) {
      editorRef.current.insertMarkdown(mentionMarkdown);
      setTextContent(editorRef.current.getMarkdown());
    }
  };

export const useMessageEditorState = (chatId: string) => {
  const [textContent, setTextContent] = React.useState("");
  const [mentionPopupAnchor, setMentionPopupAnchor] =
    React.useState<HTMLElement | null>(null);
  const [mentionSearchQuery, setMentionSearchQuery] = React.useState("");
  const editorRef = React.useRef<MDXEditorMethods>(null);
  const sendMessageMutation = useSendMessage();

  const reset = () => {
    setTextContent("");
    editorRef.current?.setMarkdown("");
  };

  const checkIfIsSubmittable = () =>
    isNonEmptyString(textContent) && !sendMessageMutation.isPending;

  const handleSubmit = async () => {
    if (!checkIfIsSubmittable()) return;

    const mentions = extractMentions(textContent);

    try {
      // console.log("Sending message:", textContent, mentions, chatId);
      await sendMessageMutation.mutateAsync({
        textContent: textContent,
        mentions,
        chatId,
      });
      // Clear the editor after successful send
      reset();
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const handleMentionButtonClick = (event: React.MouseEvent<HTMLElement>) => {
    setMentionPopupAnchor(event.currentTarget);
  };

  const handleMentionPopupClose = () => {
    setMentionPopupAnchor(null);
    setMentionSearchQuery("");
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleSubmit();
    }
  };

  return {
    textContent,
    setTextContent,
    mentionPopupAnchor,
    setMentionPopupAnchor,
    mentionSearchQuery,
    setMentionSearchQuery,
    editorRef,
    sendMessageMutation,
    checkIfIsSubmittable,
    handleSubmit,
    handleMentionButtonClick,
    handleMentionPopupClose,
    handleKeyDown,
  };
};

export type MessageEditorState = ReturnType<typeof useMessageEditorState>;
