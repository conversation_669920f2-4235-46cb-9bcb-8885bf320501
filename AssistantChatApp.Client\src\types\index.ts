// User type
export interface User {
  id: string;
  displayName: string;
  avatar?: string;
  createdAt: string;
  isAI: boolean;
}

export interface FunctionCallContent {
  callId: string;
  functionName: string;
  pluginName: string;
  arguments: string;
}

export interface FunctionResultContent {
  callId: string;
  result: string;
}

export interface MessageAdditionalContent {
  functionCalls?: FunctionCallContent[];
  functionResults?: FunctionResultContent[];
}

// Message type
export interface Message {
  id: string;
  chatId: string;
  author: User;
  content: string;
  otherContent: MessageAdditionalContent;
  createdAt: string;
  updatedAt: string;
  mentions?: string[]; // User IDs mentioned in the message
  tags?: { id: string; name: string }[];
}

// Chat type
export interface Chat {
  id: string;
  title: string;
  description?: string;
  participants: User[];
  createdAt: string;
  updatedAt: string;
  tags?: string[];
  lastMessage?: Message;
}

export interface CreateOrUpdateChatRequest {
  title: string;
  description?: string;
  participants: string[];
  tags?: string[];
}

// Auth types
export interface LoginResponse {
  token: string;
  user: User;
}

export interface LoginRequest {
  email: string;
  password: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
}

// Common types for requests
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface ChatFilters extends PaginationParams {
  search?: string;
  tags?: string[];
  participants?: string[];
  startDate?: string;
  endDate?: string;
}

export interface SendMessageRequest {
  chatId: string;
  textContent: string;
  mentions: string[];
}

export interface SendMessageResponse {
  originalMessage: Message;
  responseMessages: Message[];
}

export interface AuthApiClient {
  login: (email: string, password: string) => Promise<LoginResponse>;
  getMe: () => Promise<User>;
}

export interface ChatsApiClient {
  getChats: (filters: ChatFilters) => Promise<PaginatedResponse<Chat>>;
  getChat: (chatId: string) => Promise<Chat>;
  createChat: (data: CreateOrUpdateChatRequest) => Promise<Chat>;
  updateChat: (
    chatId: string,
    data: CreateOrUpdateChatRequest
  ) => Promise<Chat>;
  deleteChat: (chatId: string) => Promise<void>;
}

export interface MessagesApiClient {
  getMessages: (
    chatId: string,
    page: number,
    limit: number
  ) => Promise<PaginatedResponse<Message>>;
  getMessagesByIds: (
    chatId: string,
    messageIds: string[]
  ) => Promise<{ data: Message[] }>;
  sendMessage: (
    chatId: string,
    textContent: string,
    mentions: string[]
  ) => Promise<SendMessageResponse>;
  updateMessage: (
    chatId: string,
    messageId: string,
    content: string,
    mentions: string[]
  ) => Promise<Message>;
  deleteMessage: (chatId: string, messageId: string) => Promise<void>;
}

export interface UsersApiClient {
  getUsers: (
    page: number,
    limit: number,
    search: string
  ) => Promise<PaginatedResponse<User>>;
  getUser: (userId: string) => Promise<User>;
  getAIAssistants: () => Promise<User[]>;
}

export interface AIChatCompletionApiClient {
  getChatCompletion: (
    request: AIChatCompletionRequest
  ) => Promise<AIChatCompletionResponse>;
}

export interface ApiClient
  extends AuthApiClient,
    ChatsApiClient,
    MessagesApiClient,
    UsersApiClient,
    AIChatCompletionApiClient {}

export interface ChatApi {
  getChats: (filters: ChatFilters) => Promise<PaginatedResponse<Chat>>;
  getChat: (chatId: string) => Promise<Chat>;
  createChat: (data: CreateOrUpdateChatRequest) => Promise<Chat>;
  updateChat: (
    chatId: string,
    data: CreateOrUpdateChatRequest
  ) => Promise<Chat>;
  deleteChat: (chatId: string) => Promise<void>;
  getMessages: (
    chatId: string,
    page?: number,
    limit?: number
  ) => Promise<PaginatedResponse<Message>>;
  getMessagesByIds: (
    chatId: string,
    messageIds: string[]
  ) => Promise<{ data: Message[] }>;
  sendMessage: (
    chatId: string,
    textContent: string,
    mentions: string[]
  ) => Promise<SendMessageResponse>;
  updateMessage: (
    chatId: string,
    messageId: string,
    content: string,
    mentions: string[]
  ) => Promise<Message>;
  deleteMessage: (chatId: string, messageId: string) => Promise<void>;
}

export interface AIChatCompletionRequest {
  chatId: string;
  aiUserId: string;
}

export interface AIChatCompletionResponse {
  completionMessages: Message[];
}

export interface AIChatApi {
  getChatCompletion: (
    request: AIChatCompletionRequest
  ) => Promise<AIChatCompletionResponse>;
}
