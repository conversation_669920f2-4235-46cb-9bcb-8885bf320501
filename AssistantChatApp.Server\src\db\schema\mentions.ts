import { pgTable, uuid, primaryKey } from 'drizzle-orm/pg-core';
import { messages } from './messages';
import { users } from './users';

export const mentions = pgTable('mentions', {
  messageId: uuid('message_id').notNull().references(() => messages.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
}, (table) => {
  return [
    primaryKey({ columns: [table.messageId, table.userId] }),
  ];
});
