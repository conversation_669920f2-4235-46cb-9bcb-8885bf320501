// Main components
export { default as ArtifactViewer } from './ArtifactViewer';
export { ArtifactViewerHeader, CompactArtifactViewerHeader } from './ArtifactViewerHeader';
export {
  ArtifactViewerContent,
  ArtifactViewerLoadingContent,
  ArtifactViewerErrorContent,
  ArtifactViewerEmptyContent,
  ArtifactViewerPaddedContent,
  ArtifactViewerScrollableContent,
  ArtifactViewerPaperContent,
  ArtifactViewerSectionedContent,
} from './ArtifactViewerContent';

// Trigger components
export {
  ArtifactTriggerButton,
  ArtifactTriggerCard,
  ArtifactTriggerInline,
} from './ArtifactTriggerButton';

// Utility components
export { SizeSwitcher, CompactSizeSwitcher } from './SizeSwitcher';

// Hooks
export {
  useArtifactViewer,
  useArtifactViewerManager,
  useResponsiveBreakpoints,
} from './hooks';

// State management
export {
  artifactViewerAtomFamily,
  multiModalStateAtom,
  activeViewersAtom,
  focusedViewerAtom,
  initializeViewerAtom,
  openViewerAtom,
  closeViewerAtom,
  focusViewerAtom,
  updateViewerStateAtom,
  toggleCollapseAtom,
  toggleFullscreenAtom,
} from './atoms';

// Types
export type {
  ArtifactViewerSize,
  Position,
  Size,
  ArtifactViewerState,
  ArtifactViewerProps,
  ArtifactTriggerButtonProps,
  ArtifactViewerHeaderProps,
  ArtifactViewerContentProps,
  SizeSwitcherProps,
  MultiModalState,
  ResponsiveConfig,
} from './types';

// Constants
export {
  DEFAULT_SIZES,
  DEFAULT_RESPONSIVE_CONFIG,
  DEFAULT_MIN_SIZE,
  DEFAULT_MAX_SIZE,
  DEFAULT_INITIAL_POSITION,
  DEFAULT_INITIAL_SIZE,
  BASE_Z_INDEX,
} from './types';

// Utilities
export {
  getViewportSize,
  constrainPosition,
  constrainSize,
  getCenteredPosition,
  getCascadedPosition,
  getResponsiveSize,
  getResponsivePosition,
  findClosestSize,
  sizesEqual,
  positionsEqual,
  generateViewerId,
  getNextZIndex,
  debounce,
  throttle,
  getFullscreenSize,
  getFullscreenPosition,
  isEffectivelyFullscreen,
} from './utils';
