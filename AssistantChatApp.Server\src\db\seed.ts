import { db } from './index';
import { users, chats, messages, chatParticipants, tags, chatTags } from './schema';
import bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

async function seed() {
  console.log('🌱 Seeding database...');

  try {
    // Create users
    const passwordHash = await bcrypt.hash('password123', 10);
    
    const user1Id = uuidv4();
    const user2Id = uuidv4();
    const aiAssistantId = uuidv4();
    
    await db.insert(users).values([
      {
        id: user1Id,
        email: '<EMAIL>',
        password: passwordHash,
        displayName: '<PERSON>',
        avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg',
        isAI: false,
      },
      {
        id: user2Id,
        email: '<EMAIL>',
        password: passwordHash,
        displayName: '<PERSON>',
        avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg',
        isAI: false,
      },
      {
        id: aiAssistantId,
        email: '<EMAIL>',
        password: passwordHash,
        displayName: 'AI Assistant',
        avatar: 'https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg',
        isAI: true,
      },
    ]);

    console.log('✅ Users created');

    // Create tags
    const generalTagId = uuidv4();
    const teamTagId = uuidv4();
    const aiTagId = uuidv4();
    const helpTagId = uuidv4();
    
    await db.insert(tags).values([
      { id: generalTagId, name: 'general' },
      { id: teamTagId, name: 'team' },
      { id: aiTagId, name: 'ai' },
      { id: helpTagId, name: 'help' },
    ]);

    console.log('✅ Tags created');

    // Create chats
    const chat1Id = uuidv4();
    const chat2Id = uuidv4();
    
    await db.insert(chats).values([
      {
        id: chat1Id,
        title: 'General Discussion',
        description: 'A place for general conversation',
      },
      {
        id: chat2Id,
        title: 'AI Assistant Chat',
        description: 'Get help from our AI assistant',
      },
    ]);

    console.log('✅ Chats created');

    // Add chat participants
    await db.insert(chatParticipants).values([
      { chatId: chat1Id, userId: user1Id },
      { chatId: chat1Id, userId: user2Id },
      { chatId: chat2Id, userId: user1Id },
      { chatId: chat2Id, userId: aiAssistantId },
    ]);

    console.log('✅ Chat participants added');

    // Add chat tags
    await db.insert(chatTags).values([
      { chatId: chat1Id, tagId: generalTagId },
      { chatId: chat1Id, tagId: teamTagId },
      { chatId: chat2Id, tagId: aiTagId },
      { chatId: chat2Id, tagId: helpTagId },
    ]);

    console.log('✅ Chat tags added');

    // Add messages
    const message1Id = uuidv4();
    const message2Id = uuidv4();
    const message3Id = uuidv4();
    
    await db.insert(messages).values([
      {
        id: message1Id,
        chatId: chat1Id,
        authorId: user1Id,
        content: 'Hello everyone! Welcome to the general discussion.',
      },
      {
        id: message2Id,
        chatId: chat1Id,
        authorId: user2Id,
        content: 'Hi John! Thanks for setting up this chat.',
      },
      {
        id: message3Id,
        chatId: chat2Id,
        authorId: aiAssistantId,
        content: 'Hello! I am the AI Assistant. How can I help you today?',
      },
    ]);

    console.log('✅ Messages created');
    console.log('🎉 Seed completed successfully!');
  } catch (error) {
    console.error('❌ Seed failed:', error);
  } finally {
    process.exit(0);
  }
}

seed();
