import React, { useState } from 'react';
import {
  A<PERSON>Bar,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>graphy,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Button,
  Box,
  Drawer,
  List,
  ListItemIcon,
  ListItemText,
  Divider,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  MessageSquare as MessageSquareIcon,
  LogOut as LogOutIcon,
  // Settings as SettingsIcon,
  User as UserIcon,
} from 'lucide-react';
import { Link, useNavigate } from '@tanstack/react-router';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { useThemeMode } from "../../contexts/ThemeModeContext";
import { ThemeSwitcher } from '../common/ThemeSwitcher';
import { ListItemButton } from '@mui/material';

const Header: React.FC = () => {
  const { t } = useTranslation(["chats", "common", "auth"]);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleClose();
    logout();
  };

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  const { mode, setMode } = useThemeMode();

  return (
    <>
      <AppBar position="fixed" color="default" elevation={1}>
        <Toolbar>
          {isMobile && (
            <IconButton
              edge="start"
              color="inherit"
              aria-label="menu"
              onClick={toggleDrawer}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
          )}

          <Typography
            variant="h6"
            component={Link}
            to="/"
            sx={{
              flexGrow: 1,
              textDecoration: 'none',
              color: 'inherit',
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <MessageSquareIcon size={24} />
            {t("logo_title")}
          </Typography>

          {!isMobile && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Button
                component={Link}
                to="/chats"
                color="inherit"
                sx={{ mx: 1 }}
              >
                {t("chats:chats")}
              </Button>
            </Box>
          )}

          <ThemeSwitcher mode={mode} setMode={setMode} />

          <Box>
            <IconButton
              onClick={handleMenu}
              color="inherit"
              edge="end"
              aria-label="account"
              aria-controls="menu-appbar"
              aria-haspopup="true"
            >
              <Avatar
                alt={user?.displayName || 'User'}
                src={user?.avatar}
                sx={{ width: 32, height: 32 }}
              >
                {user?.displayName?.charAt(0) || 'U'}
              </Avatar>
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl)}
              onClose={handleClose}
              transformOrigin={{ horizontal: 'right', vertical: 'top' }}
              anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
              <MenuItem dense>
                <Typography variant="body2" fontWeight="medium">
                  {user?.displayName}
                </Typography>
              </MenuItem>
              <Divider />
              <MenuItem onClick={() => { handleClose(); navigate({ to: "/users/me" }) }}>
                <ListItemIcon>
                  <UserIcon size={18} />
                </ListItemIcon>
                <ListItemText>{t("common:profile")}</ListItemText>
              </MenuItem>
              {/* <MenuItem onClick={() => { handleClose(); navigate({ to: '/settings' }) }}>
                <ListItemIcon>
                  <SettingsIcon size={18} />
                </ListItemIcon>
                <ListItemText>{t("common:settings")}</ListItemText>
              </MenuItem> */}
              <Divider />
              <MenuItem onClick={handleLogout}>
                <ListItemIcon>
                  <LogOutIcon size={18} />
                </ListItemIcon>
                <ListItemText>{t("auth:logout")}</ListItemText>
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>

      <Drawer
        anchor="left"
        open={drawerOpen}
        onClose={toggleDrawer}
      >
        <Box
          sx={{ width: 250 }}
          role="presentation"
        >
          <Box sx={{ display: 'flex', alignItems: 'center', p: 2 }}>
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
              Menu
            </Typography>
            <IconButton onClick={toggleDrawer}>
              <ChevronLeftIcon />
            </IconButton>
          </Box>
          <Divider />
          <List>
            <ListItemButton
              component={Link}
              to="/chats"
              onClick={toggleDrawer}
            >
              <ListItemIcon>
                <MessageSquareIcon size={20} />
              </ListItemIcon>
              <ListItemText primary={t("chats:chats")} />
            </ListItemButton>
          </List>
          <Divider />
          <List>
            <ListItemButton
              onClick={() => {
                toggleDrawer();
                logout();
              }}
            >
              <ListItemIcon>
                <LogOutIcon size={20} />
              </ListItemIcon>
              <ListItemText primary={t("auth:logout")} />
            </ListItemButton>
          </List>
        </Box>
      </Drawer>
    </>
  );
};

export default Header;
