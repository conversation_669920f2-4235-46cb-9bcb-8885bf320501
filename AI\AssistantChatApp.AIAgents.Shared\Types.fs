namespace AssistantChatApp.AIAgents.Shared

open System

type JsonText = | JsonText of text: string
with
    member this.Value = match this with | JsonText text -> text

type AuthorRole =
    | User
    | Assistant
    | System
    | Tool

type Author = {
    Id: string
    Name: string
}

type FunctionCall = {
    CallId: string
    PluginName: string
    FunctionName: string
    Arguments: JsonText
}

type FunctionCallResult = {
    CallId: string
    PluginName: string
    FunctionName: string
    Result: JsonText
}

type MessageContent =
    | TextContent of text: string
    | FunctionCall of call: FunctionCall
    | FunctionResult of result: FunctionCallResult


type ChatMessage = {
    AuthorName: string
    AuthorRole: AuthorRole
    Content: MessageContent []
} with
    member this.GetMergedTextContent() =
        [|
            for content in this.Content do
                match content with
                | TextContent text -> yield text
                | _ -> ()
        |]
        |> String.concat "\n"

    static member CreateUserMessage(userName, content): ChatMessage =
        {
            AuthorName = userName
            Content = content
            AuthorRole = AuthorRole.User
        }

    static member CreateAssistantMessage(assistantName, content): ChatMessage =
        {
            AuthorName = assistantName
            Content = content
            AuthorRole = AuthorRole.Assistant
        }

    static member CreateSystemMessage(content): ChatMessage =
        {
            AuthorName = "System"
            Content = content
            AuthorRole = AuthorRole.System
        }

    static member CreateToolMessage(content): ChatMessage =
        {
            AuthorName = "tool"
            Content = content
            AuthorRole = AuthorRole.Tool
        }
