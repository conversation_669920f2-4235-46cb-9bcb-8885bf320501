import React from "react";
import {
  type StringOutputComponentRegistrations,
  useComponentRegistryStore,
  useComponentsFromRegistry,
  InteractiveComponents
} from "../../features/markdown/interactiveComponents/v2";

const getSurveyResultAsMdText = (answers: string[]) => {
  return answers.map((x) => `* ${x}`).join("\n");
};

const getMessageComponentRegistrations =
  (): StringOutputComponentRegistrations => ({
    survey: {
      component: InteractiveComponents.SurveyComponent,
      initialData: { selectedAnswers: [] },
      actionDataMappers: {
        submit: (action) => {
          if (action.payload) {
            const mdText = getSurveyResultAsMdText(
              action.payload.selectedAnswers
            );
            return mdText;
          }
          return undefined;
        },
      },
    },
  });

const useRegisterMessageComponents = () => {
  const registrations = getMessageComponentRegistrations();
  const register = useComponentRegistryStore((s) => s.register);
  React.useEffect(() => {
    register("survey", registrations.survey);
  }, [register, registrations.survey]);
};

export const MessageComponents = {
  useRegisterMessageComponents,
  useMessageComponents: useComponentsFromRegistry<string>,
} as const;
