open System
open System.Net.Http
open System.Threading
open System.Threading.Tasks
open FSharp.Control
open Serilog
open Serilog.HttpClient.Extensions
open Microsoft.Extensions.Hosting
open Microsoft.Extensions.DependencyInjection
open Microsoft.Extensions.Configuration
open Microsoft.Extensions.Logging
open Microsoft.Extensions.Options
open Microsoft.AspNetCore.Builder
open Microsoft.AspNetCore.Http
open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.Agents
open Microsoft.SemanticKernel.ChatCompletion

open AssistantChatApp.AIAgents
open AssistantChatApp.AIAgents.Shared.Api


[<CLIMutable>]
type ChatAppSettings = {
    BaseUrl : string
}
with
    static member ConfigSectionName = "ChatAppSettings"

[<CLIMutable>]
type AppSettings = {
    EnableConsoleIO: bool option
    DefaultAgentId: string option
}
with
    static member ConfigSectionName = "AppSettings"


module Logging =

    Log.Logger <-
        LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .WriteTo.Debug()
            .WriteTo.File("logs/bs-log-.log", rollingInterval = RollingInterval.Day)
            .AddJsonDestructuringPolicies()
            .CreateBootstrapLogger()

    let useSerilogLogging (ctx: HostBuilderContext) (logConfig: LoggerConfiguration) =
        logConfig
            .AddJsonDestructuringPolicies()
            .ReadFrom.Configuration(ctx.Configuration)
        |> ignore


module Http =

    open Fable.Remoting.DotnetClient

    module ClientNames =

        let [<Literal>] ChatAppClient = "ChatAppClient"

    let configureHttpClients (config: IConfiguration) logger (services: IServiceCollection) =
        services
        |> Core.Http.configureHttpClientForApiProviders config logger
        |> Http.configureHttpClient ClientNames.ChatAppClient config

    let createRemotingClientProxy<'Api> (clientFactory: IHttpClientFactory) clientName (baseUrl: string) =
        let client = clientFactory.CreateClient(clientName)
        Remoting.createApi baseUrl
        |> Remoting.withRouteBuilder Routing.apiRouteBuilder
        |> Remoting.withHttpClient client
        |> Remoting.buildProxy<'Api>


let configureSettings (config: IConfiguration) (services: IServiceCollection) =
    services
        .Configure<AppSettings>(config.GetSection(AppSettings.ConfigSectionName))
    |> Configuration.configureFullAgentSettings ""


type IServiceProvider with
    member this.GetChatAppSettingsSnapshot() =
        this.GetRequiredService<IOptionsSnapshot<ChatAppSettings>>().Value

    member this.GetAgentFullSettingsMap() =
        this.GetRequiredService<AgentFullSettingsMap>()


type IConfiguration with
    member this.TryGetAppSettings() =
        this.GetRequiredSection(AppSettings.ConfigSectionName).Get<AppSettings>()
        |> Option.ofObj


module ChatApp =

    let getClientProxy clientFactory baseUrl =
        Http.createRemotingClientProxy<ChatAppApi>
            clientFactory Http.ClientNames.ChatAppClient baseUrl

    let configureSettings (config: IConfiguration) (services: IServiceCollection) =
        services.Configure<ChatAppSettings>(
            config.GetSection(ChatAppSettings.ConfigSectionName)
        )

    let configureClientProxy (services: IServiceCollection) =
        services
            .AddScoped<ChatAppApi>(fun sp ->
                let clientFactory = sp.GetRequiredService<IHttpClientFactory>()
                let settings = sp.GetChatAppSettingsSnapshot()
                getClientProxy clientFactory settings.BaseUrl)

module Rpc =

    open Fable.Remoting.Server
    open Fable.Remoting.AspNetCore

    let createAgentApi (ctx: HttpContext) : AgentApi =
        {
            GetChatCompletion = fun agent inquirer messages -> async {
                // Get the agent service as scoped per request
                // so that it could reflect the configuration changes happened between requests
                let agentSvc = ctx.RequestServices.GetRequiredService<Agents.ChatAgentService>()
                let! responseMsgsOpt =
                    agentSvc.TryCreateAndAskAgentAsync(agent.UserId, messages, inquirer)
                    |> Async.AwaitTask
                // TODO: Return an explicit error (instead of empty array) when the agent by the given id is not found
                return
                    responseMsgsOpt
                    |> Option.defaultWith (fun () -> Array.empty)
            }
        }

    let configureRemoting
        (loggerFactory: ILoggerFactory)
        (loggerName: string)
        (getApi: HttpContext -> 'Api)
        =
        let logger = loggerFactory.CreateLogger(loggerName)
        Remoting.createApi()
        |> Remoting.withRouteBuilder Routing.apiRouteBuilder
        |> Remoting.withErrorHandler (fun error routeInfo ->
            ErrorResult.Ignore)
        #if DEBUG
        |> Remoting.withDiagnosticsLogger logger.LogDebug
        #endif
        |> Remoting.fromContext getApi


    let buildAgentApiRemoting (logger: ILoggerFactory) =
        configureRemoting logger (nameof AgentApi) createAgentApi

    let useAgentRemoting (app: WebApplication) =
        let loggerFactory = app.Services.GetRequiredService<ILoggerFactory>()
        app.UseRemoting<AgentApi>(buildAgentApiRemoting loggerFactory)


type ConsoleAgentWorker(
    services: IServiceProvider,
    settings: AgentFullSettings
) =
    inherit BackgroundService()

    let history = ChatHistory()
    let thread = ChatHistoryAgentThread(history)

    let loggerFactory = services.GetRequiredService<ILoggerFactory>()
    let httpClientFactory = services.GetRequiredService<IHttpClientFactory>()
    let env = services.GetRequiredService<IHostEnvironment>()
    let lifetime = services.GetRequiredService<IHostApplicationLifetime>()
    let memory = KernelMemory.getFromDI services

    let agent =
        Agents.createAgentWithPlugins
            loggerFactory httpClientFactory env memory settings

    override _.ExecuteAsync (stoppingToken: Threading.CancellationToken): Threading.Tasks.Task =
        ConsoleUI.spectreConsoleShell
            lifetime thread agent stoppingToken


let configureConsoleAgentWorker (ctx: HostBuilderContext) (services: IServiceCollection) =
    let appSettings = ctx.Configuration.TryGetAppSettings()
    let enableConsoleIO =
        appSettings
        |> Option.bind (_.EnableConsoleIO)
        |> Option.defaultValue false
    if enableConsoleIO then
        let allAgentsSettings = Configuration.getFullAgentSettings ctx.Configuration
        appSettings
        |> Option.bind (_.DefaultAgentId)
        // In the Agents configuration registry, try find the default agent by `DefaultAgentId` parameter
        |> Option.bind (fun id ->
            allAgentsSettings |> Seq.tryFind (fun s -> s.AgentId = id))
        // If not found, try get the first agent settings from the registry
        |> Option.orElseWith (fun () -> allAgentsSettings |> Seq.tryHead)
        // If there is some agent settings value, add the console worker
        |> Option.map (fun agentSettings ->
            services
                .AddHostedService<ConsoleAgentWorker>(fun sp ->
                    new ConsoleAgentWorker(sp, agentSettings)
                )
        )
        |> Option.defaultValue services
    else services


let addChatAgentService (services: IServiceCollection) =
    services.AddScoped<Agents.ChatAgentService>()


let configureServices (ctx: HostBuilderContext) (services: IServiceCollection) =
    use loggerFactory = new Serilog.Extensions.Logging.SerilogLoggerFactory()
    let logger = loggerFactory.CreateLogger("Startup")
    services
    |> configureSettings ctx.Configuration
    |> Http.configureHttpClients ctx.Configuration logger
    |> addChatAgentService
    |> KernelMemory.configureKernelMemoryWebClient
    //|> configureConsoleAgentWorker ctx
    |> ignore


let configureApp (builder: WebApplicationBuilder) =
    if builder.Configuration.IsSemanticKernelOtlpEnabled() then
        AppContext.SetSwitch("Microsoft.SemanticKernel.Experimental.GenAI.EnableOTelDiagnosticsSensitive", true)

    builder.AddServiceDefaults() |> ignore

    builder.Host
        .UseSerilog(Logging.useSerilogLogging)
        .ConfigureServices(configureServices)
    |> ignore

    let app = builder.Build()

    app.UseSerilogRequestLogging()
    |> ignore

    Rpc.useAgentRemoting app

    app

let runAsync (argv: string[]) = task {
    try
        try
            let app =
                WebApplication.CreateBuilder(argv)
                |> configureApp
            do! app.RunAsync()
        with
        | ex ->
            Log.Fatal(ex, "Application terminated unexpectedly")
    finally
        Log.CloseAndFlush()
}

[<EntryPoint>]
let main argv =
    let mainTask = runAsync argv
    mainTask.Wait()
    0
