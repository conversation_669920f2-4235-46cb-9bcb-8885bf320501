﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="Library.fs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Fable.Remoting.AspNetCore" Version="2.42.0" />
    <PackageReference Include="Fable.Remoting.DotnetClient" Version="3.36.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="9.7.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.7" />
    <PackageReference Include="Serilog.HttpClient" Version="3.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AssistantChatApp.AIAgents.Shared.Dotnet\AssistantChatApp.AIAgents.Shared.Dotnet.fsproj" />
  </ItemGroup>

</Project>
