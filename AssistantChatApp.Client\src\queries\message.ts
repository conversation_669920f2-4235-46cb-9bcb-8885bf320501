import { useQuery } from "@tanstack/react-query";
import { chatApi } from "../api/chatApi";

export const messageQueryKeys = {
  allChatMessages: (chatId: string) => ["messages", chatId],
  pagedChatMessages: (chatId: string, page: number, limit: number) => [
    "messages",
    chatId,
    page,
    limit,
  ],
};

export const useGetChatMessages = (chatId: string, page = 1, limit = 100) => {
  return useQuery({
    queryKey: messageQueryKeys.pagedChatMessages(chatId, page, limit),
    queryFn: () => chatApi.getMessages(chatId, page, limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 20 * 1000, // 20 seconds
  });
};
