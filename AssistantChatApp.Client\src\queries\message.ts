import { useQuery } from "@tanstack/react-query";
import { chatApi } from "../api/chatApi";

export const messageQueryKeys = {
  allChatMessages: (chatId: string) => ["messages", chatId],
  pagedChatMessages: (chatId: string, page: number, limit: number) => [
    "messages",
    chatId,
    page,
    limit,
  ],
};

export const useGetChatMessages = (chatId: string, page = 1, limit = 50) => {
  return useQuery({
    queryKey: messageQueryKeys.pagedChatMessages(chatId, page, limit),
    queryFn: () => chatApi.getMessages(chatId, page, limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
    // refetchInterval: 5 * 1000, // 5 seconds
  });
};
