import React from "react";
import { useNavigate } from "@tanstack/react-router";
import { useUpdateChat } from "../../mutations/chat";
import { ChatFormDialog, ChatFormState } from "./ChatForms";
import { Chat } from "../../types";
import { useTranslation } from 'react-i18next';

export interface UpdateChatFormDialogProps {
  chat: Chat
  onDialogClose: () => void;
  isDialogOpened: boolean;
  formState: ChatFormState;
}

export const UpdateChatFormDialog: React.FC<UpdateChatFormDialogProps> = ({
  chat, formState, isDialogOpened, onDialogClose,
}) => {
  const { t } = useTranslation('chats');
  const { description, title, participants, tags } = formState.formValues;

  const navigate = useNavigate();

  const handleDialogClose = () => {
    formState.reset();
    onDialogClose();
  };

  // Update chat mutation
  const updateChatMutation = useUpdateChat((newChat) => {
    navigate({ to: "/chats/$chatId", params: { chatId: newChat.id } });
    handleDialogClose();
  });

  const handleUpdateChat = () => {
    if (!title.trim()) return;

    updateChatMutation.mutate({
      chatId: chat.id,
      chatData: {
        title,
        description,
        participants: participants.map((p) => p.id),
        tags,
      }
    });
  };

  return (
    <ChatFormDialog
      dialogTitle={t('update_chat')}
      isOpened={isDialogOpened}
      formState={formState}
      isSubmitting={updateChatMutation.isPending}
      onSubmit={handleUpdateChat}
      onClose={handleDialogClose}
    />
  );
};
