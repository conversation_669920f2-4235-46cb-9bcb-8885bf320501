namespace AssistantChatApp.AIAgents.Shared.Dotnet

open System
open System.Text
open System.Text.Json
open AssistantChatApp.AIAgents.Shared

module ChatMessage =

    let prependWithAuthorName (authorName: string) (content: string) =
        $"**{authorName}**:\n{content}"

    let constructCompositeTextMessage
        (contentItems: MessageContent [])
        =
        let textContentItems, toolContentItems =
            let mutable textContentItems = ResizeArray<string>()
            let mutable restContentItems = ResizeArray<MessageContent>()
            for c in contentItems do
                match c with
                | TextContent text -> textContentItems.Add text
                | (FunctionCall _ | FunctionResult _) as c -> restContentItems.Add c
            textContentItems.ToArray(), restContentItems.ToArray()
        let textContentSection = String.concat "\n" textContentItems
        let toolContentSection =
            if toolContentItems.Length > 0 then
                let toolContent = Serialization.Json.serialize toolContentItems
                System.Text.StringBuilder()
                    .AppendLine("<tool-content>")
                    .AppendLine()
                    .AppendLine("```json")
                    .AppendLine(toolContent)
                    .AppendLine("```")
                    .AppendLine()
                    .AppendLine("</tool-content>")
                    .ToString()
            else
                String.Empty
        
        System.Text.StringBuilder()
            .AppendLine(textContentSection)
            .AppendLine()
            .Append(toolContentSection)
            .ToString()
