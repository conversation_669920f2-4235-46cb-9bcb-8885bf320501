## Task Description

Draggable and resizable modal window for showing various interactive components embedded in chat messages (aka "message artifacts"). Examples of such components: forms, polls, data tables (DataGrid), etc. The idea is to show the component separately from the message and chat to not clutter the chat message list with big and complex content. Instead, a special button-like element should be shown in the place where the component appears in the message. And when the user clicks on it, the modal window should be shown.
The modal window should support the following features:
- **Dragging and resizing**: to move the modal window to a desired location and resize it to a desired size.
- **Expand-collapse**: when collapsed, only the title and a button to expand should be shown. When expanded, the full content of the component should be shown.
- **Close button**: to close the modal window.
- **Fullscreen mode**: to switch between the normal and fullscreen modes.
- **Responsive design**: the modal window should look and work well on both desktop and mobile devices.
- **Multiple modal windows handling**: this is optional feature. I.e., in a base setup consists of a single modal window. But it should be possible to support multiple modal windows by providing a set of corresponding props (such as "z-index", "is focused", etc.)

## Technical Requirements

### Structure

- The described functionality must form a self-contained, encapsulated and reusable module ("module" here means a set of TypeScript files). I.e., all its dependencies (types, functions, React hooks, React components) are defined within its scope or explicitly imported.

### Tech Stack Involved

- Framework: React
- Language: TypeScript
- UI kit: Material UI (MUI)
- State management: React Hooks + Jotai (`jotai`)
- Draggable and resizable components: `react-rnd`

## Final instructions

Destination folder: `AssistantChatApp.Client/src/components/message/artifactViewer`
Before writing any code, show me a plan and a structure of your implementation (e.g., what types, hooks, components, etc. you are going to define, with their signatures and relations between them).
