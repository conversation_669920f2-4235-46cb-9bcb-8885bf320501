module AssistantChatApp.AIAgents.SemanticKernelHelpers

open System
open System.Collections.Generic
open Microsoft.SemanticKernel
open Microsoft.SemanticKernel.ChatCompletion
open Microsoft.SemanticKernel.Agents
open Microsoft.Extensions.DependencyInjection
open AssistantChatApp.AIAgents.Http

module KernelBuilder =

    //open Together.SemanticKernel.Extensions

    //let addTogetherAIChatCompletion
    //    apiKey modelId serviceId
    //    httpClient
    //    (builder: IKernelBuilder)
    //    =
    //    builder.AddTogetherChatCompletion(
    //        apiKey = apiKey,
    //        model = modelId,
    //        ?serviceId = serviceId,
    //        ?httpClient = httpClient
    //    )

    let addTogetherOpenAIChatCompletion
        apiKey modelId serviceId
        (httpClient)
        (builder: IKernelBuilder)
        =
        builder.AddOpenAIChatCompletion(
            modelId = modelId,
            endpoint = Uri TogetherAI.ApiEndpoint,
            apiKey = apiKey,
            ?serviceId = serviceId,
            ?httpClient = httpClient
        )

    let addOpenRouterOpenAIChatCompletion
        apiKey modelId serviceId
        httpClient
        (builder: IKernelBuilder)
        =
        builder.AddOpenAIChatCompletion(
            modelId,
            Uri "https://openrouter.ai/api/v1/",
            apiKey,
            ?serviceId = serviceId,
            ?httpClient = httpClient
        )

    let configurePlugins
        (configure: IKernelBuilderPlugins -> IKernelBuilderPlugins)
        (builder: IKernelBuilder)
        =
        configure builder.Plugins
        |> fun _ -> builder

module PromptExecutionSettings =

    let withFunctionChoice
        (behavior: FunctionChoiceBehavior)
        (settings: #PromptExecutionSettings)
        =
        settings.FunctionChoiceBehavior <- behavior
        settings

    let withFunctionChoiceAuto
        (autoInvoke: bool)
        (functions: seq<KernelFunction> option)
        (options: FunctionChoiceBehaviorOptions option)
        (settings: #PromptExecutionSettings)
        =
        settings
        |> withFunctionChoice (
            FunctionChoiceBehavior.Auto(
                ?functions = functions,
                autoInvoke = autoInvoke,
                ?options = options
            ))

    let withFunctionChoiceRequired
        (functions: seq<KernelFunction> option)
        (autoInvoke: bool option)
        (options: FunctionChoiceBehaviorOptions option)
        (settings: #PromptExecutionSettings)
        =
        settings
        |> withFunctionChoice (
            FunctionChoiceBehavior.Required(
                ?functions = functions,
                ?autoInvoke = autoInvoke,
                ?options = options
            ))

    let withFunctionChoiceNone
        (functions: seq<KernelFunction> option)
        (options: FunctionChoiceBehaviorOptions option)
        (settings: #PromptExecutionSettings)
        =
        settings
        |> withFunctionChoice (
            FunctionChoiceBehavior.None(
                ?functions = functions,
                ?options = options
            ))

    let withServiceId (serviceId: string) (settings: #PromptExecutionSettings) =
        settings.ServiceId <- serviceId
        settings

    let withModelId (modelId: string) (settings: #PromptExecutionSettings) =
        settings.ModelId <- modelId
        settings

type IKernelBuilder with

    member builder.AddTogetherOpenAIChatCompletion(apiKey, modelId, ?serviceId, ?httpClient) =
        builder
        |> KernelBuilder.addTogetherOpenAIChatCompletion apiKey modelId serviceId httpClient

    member builder.AddOpenRouterOpenAIChatCompletion(apiKey, modelId, ?serviceId, ?httpClient) =
        builder
        |> KernelBuilder.addOpenRouterOpenAIChatCompletion apiKey modelId serviceId httpClient

type KernelArguments with
    static member Create(?settings: PromptExecutionSettings, ?args: #seq<KeyValuePair<string, obj>>) =
        let kernelArgs =
            settings
            |> Option.map KernelArguments
            |> Option.defaultWith KernelArguments
        args |> Option.iter (fun args ->
            for arg in args do kernelArgs.Add(arg.Key, arg.Value)
        )
        kernelArgs

type AgentInvokeOptions with
    static member Create(?kernel, ?kernelArgs: KernelArguments, ?additionalInstructions, ?onIntermediateMessage: Func<_, _>) =
        let inline (!?) (v: 't option) = v |> Option.defaultValue null
        AgentInvokeOptions(
            KernelArguments = !? kernelArgs,
            Kernel = !? kernel,
            AdditionalInstructions = defaultArg additionalInstructions "",
            OnIntermediateMessage = !? onIntermediateMessage
        )

    static member Create(
        ?kernel,
        ?settings: PromptExecutionSettings,
        ?args: #seq<KeyValuePair<string, obj>>,
        ?additionalInstructions,
        ?onIntermediateMessage)
        =
        let kernelArgs =
            match settings, args with
            | None, None -> None
            | _ -> KernelArguments.Create(?settings = settings, ?args = args) |> Some
        AgentInvokeOptions.Create(
            ?kernel = kernel,
            ?kernelArgs = kernelArgs,
            ?additionalInstructions = additionalInstructions,
            ?onIntermediateMessage = onIntermediateMessage
        )

module ChatHistory =

    let tryGetLastAssistantMessage (history: ChatHistory) =
        let rec loop (i: int) =
            if i > 0 then
                let msg = history[i]
                match msg.Role with
                | r when r = AuthorRole.Assistant -> Some msg
                | _ -> loop (i - 1)
            else None

        loop (history.Count - 1)

    let addAssistantMessage (modelId: string) (message: string) (history: ChatHistory) =
        let aiMessage = ChatMessageContent(
            AuthorRole.Assistant,
            message,
            modelId = modelId
        )
        history.Add(aiMessage)
