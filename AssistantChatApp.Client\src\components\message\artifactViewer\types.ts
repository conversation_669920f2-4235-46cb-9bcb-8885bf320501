import React from 'react';

/**
 * Predefined sizes for the artifact viewer modal
 */
export interface ArtifactViewerSize {
  width: number;
  height: number;
  label: string;
  icon?: React.ReactNode;
}

/**
 * Position coordinates for the modal
 */
export interface Position {
  x: number;
  y: number;
}

/**
 * Size dimensions for the modal
 */
export interface Size {
  width: number;
  height: number;
}

/**
 * State of an individual artifact viewer modal
 */
export interface ArtifactViewerState {
  /** Unique identifier for this artifact viewer instance */
  id: string;
  /** Whether the modal is currently open */
  isOpen: boolean;
  /** Whether the modal is collapsed (showing only header) */
  isCollapsed: boolean;
  /** Whether the modal is in fullscreen mode */
  isFullscreen: boolean;
  /** Whether this modal is currently focused */
  isFocused: boolean;
  /** Current position of the modal */
  position: Position;
  /** Current size of the modal */
  size: Size;
  /** Z-index for layering multiple modals */
  zIndex: number;
  /** Title displayed in the modal header */
  title: React.ReactNode;
  /** Optional description or subtitle */
  description?: string;
  /** Timestamp when the modal was created */
  createdAt: number;
  /** Timestamp when the modal was last updated */
  updatedAt: number;
}

/**
 * Props for the main ArtifactViewer component
 */
export interface ArtifactViewerProps {
  /** Unique identifier for this artifact viewer */
  artifactId: string;
  /** Title displayed in the modal header */
  title: React.ReactNode;
  /** Optional description or subtitle */
  description?: string;
  /** Content to display in the modal */
  children: React.ReactNode;
  /** Initial position when first opened */
  initialPosition?: Position;
  /** Initial size when first opened */
  initialSize?: Size;
  /** Callback when the modal is closed */
  onClose?: () => void;
  /** Callback when the modal is focused */
  onFocus?: () => void;
  /** Custom z-index override */
  zIndex?: number;
  /** Whether this modal should be focused initially */
  autoFocus?: boolean;
  /** Whether the modal can be resized */
  resizable?: boolean;
  /** Whether the modal can be dragged */
  draggable?: boolean;
  /** Minimum size constraints */
  minSize?: Size;
  /** Maximum size constraints */
  maxSize?: Size;
  /** Custom class name for styling */
  className?: string;
}

/**
 * Props for the ArtifactTriggerButton component
 */
export interface ArtifactTriggerButtonProps {
  /** Unique identifier for the artifact */
  artifactId: string;
  /** Title for the artifact */
  title: string;
  /** Optional description */
  description?: string;
  /** Content to display in the modal when opened */
  children: React.ReactNode;
  /** Button variant */
  variant?: 'outlined' | 'contained' | 'text';
  /** Button size */
  size?: 'small' | 'medium' | 'large';
  /** Custom icon for the button */
  icon?: React.ReactNode;
  /** Whether the button is disabled */
  disabled?: boolean;
  /** Custom class name */
  className?: string;
  /** Callback when the artifact is opened */
  onOpen?: () => void;
}

/**
 * Base props for the ArtifactViewerHeader and CompactArtifactViewerHeader components.
 * Does not include size switcher props.
 */
export interface ArtifactViewerHeaderBaseProps {
  /** Artifact viewer state */
  state: ArtifactViewerState;
  /** Callback to update the state */
  onStateUpdate: (updates: Partial<ArtifactViewerState>) => void;
  /** Callback when close button is clicked */
  onClose: () => void;
}

/**
 * Props for the ArtifactViewerHeader component
 */
export interface ArtifactViewerHeaderProps extends ArtifactViewerHeaderBaseProps {

  /** Whether to show the size switcher */
  showSizeSwitcher?: boolean;
  /** Available predefined sizes */
  availableSizes?: ArtifactViewerSize[];
}

/**
 * Props for the ArtifactViewerContent component
 */
export interface ArtifactViewerContentProps {
  /** Whether the content is collapsed */
  isCollapsed: boolean;
  /** Content to display */
  children: React.ReactNode;
  /** Custom class name */
  className?: string;
}

/**
 * Props for the SizeSwitcher component
 */
export interface SizeSwitcherProps {
  /** Current size */
  currentSize: Size;
  /** Available predefined sizes */
  availableSizes: ArtifactViewerSize[];
  /** Callback when size is changed */
  onSizeChange: (size: Size) => void;
  /** Whether the switcher is disabled */
  disabled?: boolean;
  /** Size of the switcher buttons */
  size?: 'small' | 'medium' | 'large';
}

/**
 * State for managing multiple artifact viewers
 */
export interface MultiModalState {
  /** Map of artifact ID to viewer state */
  viewers: Record<string, ArtifactViewerState>;
  /** ID of the currently focused viewer */
  focusedViewerId: string | null;
  /** Next available z-index */
  nextZIndex: number;
}

/**
 * Configuration for responsive breakpoints
 */
export interface ResponsiveConfig {
  /** Mobile breakpoint (px) */
  mobile: number;
  /** Tablet breakpoint (px) */
  tablet: number;
  /** Desktop breakpoint (px) */
  desktop: number;
}

/**
 * Default sizes available for the size switcher
 */
export const DEFAULT_SIZES: ArtifactViewerSize[] = [
  { width: 400, height: 300, label: 'Small' },
  { width: 600, height: 450, label: 'Medium' },
  { width: 800, height: 600, label: 'Large' },
  { width: 1000, height: 750, label: 'Extra Large' },
];

/**
 * Default responsive configuration
 */
export const DEFAULT_RESPONSIVE_CONFIG: ResponsiveConfig = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200,
};

/**
 * Default minimum and maximum sizes
 */
export const DEFAULT_MIN_SIZE: Size = { width: 300, height: 200 };
export const DEFAULT_MAX_SIZE: Size = { width: 1400, height: 1000 };

/**
 * Default initial position and size
 */
export const DEFAULT_INITIAL_POSITION: Position = { x: 100, y: 100 };
export const DEFAULT_INITIAL_SIZE: Size = { width: 600, height: 450 };

/**
 * Base z-index for artifact viewers
 */
export const BASE_Z_INDEX = 1300;
