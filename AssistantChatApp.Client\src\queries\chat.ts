import { useQuery } from "@tanstack/react-query";
import { chatApi } from "../api/chatApi";
import { ChatFilters } from "../types";

export const chatQueryKeys = {
  allChats: ["chats"],
  chats: (filters: ChatFilters) => ["chats", filters],
  chat: (chatId: string) => ["chat", chatId],
};

export const useGetChats = (filters: ChatFilters) => {
  return useQuery({
    queryKey: chatQueryKeys.chats(filters),
    queryFn: () => chatApi.getChats(filters),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useGetChat = (chatId: string) => {
  return useQuery({
    queryKey: chatQueryKeys.chat(chatId),
    queryFn: () => chatApi.getChat(chatId),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};
