import { useQuery, queryOptions } from "@tanstack/react-query";
import { chatApi } from "../api/chatApi";
import { ChatFilters } from "../types";

export const chatQueryKeys = {
  allChats: ["chats"],
  chats: (filters: ChatFilters) => ["chats", filters],
  chat: (chatId: string) => ["chat", chatId],
};

export const createChatQueryOptions = (chatId: string) =>
  queryOptions({
    queryKey: chatQueryKeys.chat(chatId),
    queryFn: () => chatApi.getChat(chatId),
    staleTime: 1 * 60 * 1000, // 1 minute
  })

export const createChatsQueryOptions = (filters: ChatFilters) =>
  queryOptions({
    queryKey: chatQueryKeys.chats(filters),
    queryFn: () => chatApi.getChats(filters),
    staleTime: 1 * 60 * 1000, // 1 minute
  })

export const useGetChats = (filters: ChatFilters) => {
  const options = createChatsQueryOptions(filters);
  return useQuery(options);
};

export const useGetChat = (chatId: string) => {
  const options = createChatQueryOptions(chatId);
  return useQuery(options);
};
