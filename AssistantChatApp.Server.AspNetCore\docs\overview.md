# Assistant Chat App Backend - Technical Overview

This document provides a comprehensive overview of the Assistant Chat App backend implementation, focusing on the application's module structure and database schema.

## Application Module Structure

The backend application follows a layered architecture pattern, separating concerns into distinct modules that interact with each other in a hierarchical manner. This design promotes maintainability, testability, and scalability.

### Module Hierarchy

```
AssistantChatApp.Server.AspNetCore
├── Controllers (API Endpoints)
├── Services (Business Logic)
├── Data (Data Access)
│   └── Migrations
├── Models (Domain Entities)
├── DTOs (Data Transfer Objects)
└── Helpers (Utilities and Middleware)
```

### Module Descriptions

#### 1. Controllers Layer

The Controllers layer is the entry point for all HTTP requests and is responsible for:
- Handling API requests and returning appropriate responses
- Input validation
- Authentication and authorization checks
- Routing requests to the appropriate service

**Key Components:**
- `AuthController`: Handles user authentication (login) and retrieval of the current user
- `ChatsController`: Manages CRUD operations for chat rooms
- `MessagesController`: Manages CRUD operations for messages within chats
- `UsersController`: Provides user-related functionality including listing users and AI assistants

#### 2. Services Layer

The Services layer contains the business logic of the application and acts as an intermediary between the Controllers and Data layers:
- Implements domain-specific operations
- Enforces business rules and constraints
- Transforms between domain models and DTOs

**Key Components:**
- `UserService`: Handles user-related operations including authentication and user retrieval
- `ChatService`: Manages chat room operations including filtering, creation, and updates
- `MessageService`: Handles message operations within chats
- `JwtService`: Generates and validates JWT tokens for authentication

#### 3. Data Layer

The Data layer is responsible for data persistence and retrieval:
- Defines the database context and entity configurations
- Manages database migrations
- Provides database seeding functionality

**Key Components:**
- `ApplicationDbContext`: Entity Framework Core DbContext that defines the database schema
- `DbSeeder`: Utility for seeding the database with initial data
- `Migrations`: Contains database migration files for schema versioning

#### 4. Models Layer

The Models layer defines the domain entities that represent the core business objects:
- Defines entity properties and relationships
- Contains domain-specific validation logic

**Key Components:**
- `User`: Represents a user in the system (extends IdentityUser)
- `Chat`: Represents a chat room
- `Message`: Represents a message within a chat
- `ChatParticipant`: Represents a user's participation in a chat
- `ChatTag`: Represents a tag associated with a chat
- `MessageMention`: Represents a user mention within a message

#### 5. DTOs Layer

The DTOs (Data Transfer Objects) layer defines objects used for data exchange between the client and server:
- Simplifies data transfer by including only necessary properties
- Provides a clear contract for API requests and responses
- Decouples the internal domain model from the external API

**Key Components:**
- Request DTOs: `LoginRequestDto`, `CreateChatDto`, `UpdateChatDto`, etc.
- Response DTOs: `UserDto`, `ChatDto`, `MessageDto`, etc.
- Utility DTOs: `PaginatedResponseDto`, `ErrorResponseDto`, etc.

#### 6. Helpers Layer

The Helpers layer contains utility classes and middleware that support the application:
- Provides cross-cutting concerns like error handling
- Contains utility functions and extensions

**Key Component:**
- `ErrorHandlingMiddleware`: Global exception handler that standardizes error responses

### Module Interactions

1. **Request Flow:**
   - Client sends HTTP request → Controller receives request → Controller calls Service → Service uses Data layer → Service returns result to Controller → Controller returns HTTP response

2. **Authentication Flow:**
   - Client sends credentials → AuthController calls UserService → UserService validates credentials → JwtService generates token → Token returned to client

3. **Data Access Flow:**
   - Service needs data → Service calls ApplicationDbContext → DbContext retrieves/persists data → Data returned to Service

## Database Schema

The database schema is designed to support the chat application's core functionality, including users, chats, messages, and their relationships.

### Entity Relationship Diagram

```
┌─────────┐       ┌─────────────────┐       ┌───────┐
│  User   │◄──────┤ ChatParticipant │◄──────┤ Chat  │
└─────────┘       └─────────────────┘       └───────┘
     ▲                                         │
     │                                         │
     │            ┌─────────┐                  │
     └────────────┤ Message │◄─────────────────┘
                  └─────────┘
                      │
     ┌────────────────┘
     │
┌────────────────┐
│ MessageMention │
└────────────────┘
```

### Entity Descriptions

#### User Entity
- Extends ASP.NET Core Identity's `IdentityUser`
- **Properties:**
  - `Id`: Unique identifier (string, primary key)
  - `DisplayName`: User's display name
  - `Avatar`: Optional URL to user's avatar image
  - `CreatedAt`: Timestamp of user creation
  - `IsAI`: Flag indicating if the user is an AI assistant
- **Relationships:**
  - One-to-many with `Chat` (as owner)
  - One-to-many with `Message` (as author)
  - Many-to-many with `Chat` through `ChatParticipant`
  - Many-to-many with `Message` through `MessageMention`

#### Chat Entity
- **Properties:**
  - `Id`: Unique identifier (string, primary key)
  - `Title`: Chat room title
  - `Description`: Optional chat description
  - `OwnerId`: ID of the user who created the chat
  - `CreatedAt`: Timestamp of chat creation
  - `UpdatedAt`: Timestamp of last update
- **Relationships:**
  - Many-to-one with `User` (as owner)
  - One-to-many with `Message`
  - Many-to-many with `User` through `ChatParticipant`
  - One-to-many with `ChatTag`

#### Message Entity
- **Properties:**
  - `Id`: Unique identifier (string, primary key)
  - `ChatId`: ID of the chat the message belongs to
  - `AuthorId`: ID of the user who created the message
  - `Content`: Message text content
  - `CreatedAt`: Timestamp of message creation
  - `UpdatedAt`: Timestamp of last update
- **Relationships:**
  - Many-to-one with `Chat`
  - Many-to-one with `User` (as author)
  - One-to-many with `MessageMention`

#### ChatParticipant Entity (Join Table)
- **Properties:**
  - `ChatId`: ID of the chat (composite primary key)
  - `UserId`: ID of the user (composite primary key)
  - `JoinedAt`: Timestamp when the user joined the chat
- **Relationships:**
  - Many-to-one with `Chat`
  - Many-to-one with `User`

#### ChatTag Entity
- **Properties:**
  - `ChatId`: ID of the chat (composite primary key)
  - `TagName`: Name of the tag (composite primary key)
- **Relationships:**
  - Many-to-one with `Chat`

#### MessageMention Entity (Join Table)
- **Properties:**
  - `MessageId`: ID of the message (composite primary key)
  - `UserId`: ID of the mentioned user (composite primary key)
- **Relationships:**
  - Many-to-one with `Message`
  - Many-to-one with `User`

### Database Constraints and Indexes

- **Primary Keys:**
  - `User.Id`
  - `Chat.Id`
  - `Message.Id`
  - `(ChatParticipant.ChatId, ChatParticipant.UserId)`
  - `(ChatTag.ChatId, ChatTag.TagName)`
  - `(MessageMention.MessageId, MessageMention.UserId)`

- **Foreign Keys:**
  - `Chat.OwnerId` → `User.Id` (Restrict delete)
  - `Message.ChatId` → `Chat.Id` (Cascade delete)
  - `Message.AuthorId` → `User.Id` (Restrict delete)
  - `ChatParticipant.ChatId` → `Chat.Id` (Cascade delete)
  - `ChatParticipant.UserId` → `User.Id` (Cascade delete)
  - `ChatTag.ChatId` → `Chat.Id` (Cascade delete)
  - `MessageMention.MessageId` → `Message.Id` (Cascade delete)
  - `MessageMention.UserId` → `User.Id` (Restrict delete)

- **Indexes:**
  - `User.Email` (Unique)
  - `User.UserName` (Unique)
  - `Chat.OwnerId`
  - `Message.ChatId`
  - `Message.AuthorId`

### Data Flow Examples

1. **Creating a New Chat:**
   - A new `Chat` entity is created with the current user as owner
   - A `ChatParticipant` entry is created for the owner
   - Additional `ChatParticipant` entries are created for other participants
   - `ChatTag` entries are created for any tags

2. **Sending a Message:**
   - A new `Message` entity is created with references to the chat and author
   - `MessageMention` entries are created for any user mentions
   - The `UpdatedAt` timestamp of the associated chat is updated

3. **Retrieving Chat Messages:**
   - Messages are queried by `ChatId`
   - Results are paginated and ordered by creation time
   - Each message includes its author information and mentions

## Conclusion

The Assistant Chat App backend follows a well-structured, modular architecture that separates concerns and promotes maintainability. The database schema is designed to efficiently support the core functionality of the application while maintaining data integrity through appropriate relationships and constraints.

This design allows for future extensibility, such as adding new features like message reactions, file attachments, or enhanced user profiles, without requiring significant architectural changes.
