﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>fc15411b-71a9-4653-8fee-afc2b415d912</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.KernelMemory.AI.OpenAI" Version="0.98.250508.3" />
    <PackageReference Include="Microsoft.KernelMemory.MemoryDb.Postgres" Version="0.98.250508.3+bd8d34e" />
    <PackageReference Include="Microsoft.KernelMemory.Service.AspNetCore" Version="0.98.250508.3" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.OpenAI" Version="1.64.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\AssistantChatApp.Aspire.ServiceDefaults\AssistantChatApp.Aspire.ServiceDefaults.csproj" />
  </ItemGroup>

</Project>
