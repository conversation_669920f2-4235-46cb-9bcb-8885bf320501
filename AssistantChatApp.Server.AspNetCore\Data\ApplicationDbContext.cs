using AssistantChatApp.Server.AspNetCore.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace AssistantChatApp.Server.AspNetCore.Data
{
    public class ApplicationDbContext : IdentityDbContext<User>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Chat> Chats { get; set; }
        public DbSet<Message> Messages { get; set; }
        public DbSet<ChatParticipant> ChatParticipants { get; set; }
        public DbSet<ChatTag> ChatTags { get; set; }
        public DbSet<MessageMention> MessageMentions { get; set; }
        public DbSet<Tag> Tags { get; set; }
        public DbSet<FunctionCallContent> FunctionCallContents { get; set; }
        public DbSet<FunctionResultContent> FunctionResultContents { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure composite keys for many-to-many relationships
            modelBuilder.Entity<ChatParticipant>()
                .<PERSON><PERSON>ey(cp => new { cp.ChatId, cp.UserId });

            modelBuilder.Entity<ChatTag>()
                .HasKey(ct => new { ct.ChatId, ct.TagName });

            modelBuilder.Entity<MessageMention>()
                .HasKey(mm => new { mm.MessageId, mm.UserId });

            // Configure relationships
            modelBuilder.Entity<Chat>()
                .HasOne(c => c.Owner)
                .WithMany(u => u.OwnedChats)
                .HasForeignKey(c => c.OwnerId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Message>()
                .HasOne(m => m.Chat)
                .WithMany(c => c.Messages)
                .HasForeignKey(m => m.ChatId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Message>()
                .HasOne(m => m.Author)
                .WithMany(u => u.Messages)
                .HasForeignKey(m => m.AuthorId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Message>()
                .HasMany(m => m.Contents)
                .WithOne(m => m.Message)
                .HasForeignKey(m => m.MessageId);

            modelBuilder.Entity<ChatParticipant>()
                .HasOne(cp => cp.Chat)
                .WithMany(c => c.Participants)
                .HasForeignKey(cp => cp.ChatId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ChatParticipant>()
                .HasOne(cp => cp.User)
                .WithMany(u => u.ChatParticipants)
                .HasForeignKey(cp => cp.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ChatTag>()
                .HasOne(ct => ct.Chat)
                .WithMany(c => c.Tags)
                .HasForeignKey(ct => ct.ChatId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<MessageMention>()
                .HasOne(mm => mm.Message)
                .WithMany(m => m.Mentions)
                .HasForeignKey(mm => mm.MessageId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<MessageMention>()
                .HasOne(mm => mm.User)
                .WithMany()
                .HasForeignKey(mm => mm.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Tag>()
                .HasMany(t => t.Messages)
                .WithMany(m => m.Tags)
                .UsingEntity<MessageTag>();

            modelBuilder.Entity<Content>()
                .UseTpcMappingStrategy();
        }
    }
}
