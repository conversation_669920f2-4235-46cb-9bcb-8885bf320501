import {
  Position,
  Size,
  ArtifactViewerSize,
  ResponsiveConfig,
  DEFAULT_RESPONSIVE_CONFIG,
  DEFAULT_MIN_SIZE,
  DEFAULT_MAX_SIZE
} from './types';

/**
 * Gets the current viewport dimensions
 */
export const getViewportSize = (): Size => ({
  width: window.innerWidth,
  height: window.innerHeight,
});

/**
 * Checks if the current viewport is mobile
 */
export const isMobile = (config: ResponsiveConfig = DEFAULT_RESPONSIVE_CONFIG): boolean => {
  return window.innerWidth < config.mobile;
};

/**
 * Checks if the current viewport is tablet
 */
export const isTablet = (config: ResponsiveConfig = DEFAULT_RESPONSIVE_CONFIG): boolean => {
  return window.innerWidth >= config.mobile && window.innerWidth < config.desktop;
};

/**
 * Checks if the current viewport is desktop
 */
export const isDesktop = (config: ResponsiveConfig = DEFAULT_RESPONSIVE_CONFIG): boolean => {
  return window.innerWidth >= config.desktop;
};

/**
 * Gets the device type based on viewport width
 */
export const getDeviceType = (config: ResponsiveConfig = DEFAULT_RESPONSIVE_CONFIG): 'mobile' | 'tablet' | 'desktop' => {
  if (isMobile(config)) return 'mobile';
  if (isTablet(config)) return 'tablet';
  return 'desktop';
};

/**
 * Constrains a position to keep the modal within viewport bounds
 */
export const constrainPosition = (
  position: Position,
  modalSize: Size,
  viewportSize: Size = getViewportSize(),
  padding: number = 20
): Position => {
  const maxX = Math.max(0, viewportSize.width - modalSize.width - padding);
  const maxY = Math.max(0, viewportSize.height - modalSize.height - padding);

  return {
    x: Math.max(padding, Math.min(position.x, maxX)),
    y: Math.max(padding, Math.min(position.y, maxY)),
  };
};

/**
 * Constrains a size to fit within viewport and respect min/max constraints
 */
export const constrainSize = (
  size: Size,
  viewportSize: Size = getViewportSize(),
  minSize: Size = DEFAULT_MIN_SIZE,
  maxSize: Size = DEFAULT_MAX_SIZE,
  padding: number = 40
): Size => {
  const maxViewportWidth = viewportSize.width - padding;
  const maxViewportHeight = viewportSize.height - padding;

  return {
    width: Math.max(
      minSize.width,
      Math.min(size.width, Math.min(maxSize.width, maxViewportWidth))
    ),
    height: Math.max(
      minSize.height,
      Math.min(size.height, Math.min(maxSize.height, maxViewportHeight))
    ),
  };
};

/**
 * Calculates a centered position for a modal
 */
export const getCenteredPosition = (
  modalSize: Size,
  viewportSize: Size = getViewportSize()
): Position => ({
  x: Math.max(0, (viewportSize.width - modalSize.width) / 2),
  y: Math.max(0, (viewportSize.height - modalSize.height) / 2),
});

/**
 * Calculates a cascaded position for multiple modals
 */
export const getCascadedPosition = (
  basePosition: Position,
  index: number,
  offset: number = 30
): Position => ({
  x: basePosition.x + (index * offset),
  y: basePosition.y + (index * offset),
});

/**
 * Gets responsive size based on device type
 */
export const getResponsiveSize = (
  baseSize: Size,
  config: ResponsiveConfig = DEFAULT_RESPONSIVE_CONFIG
): Size => {
  const deviceType = getDeviceType(config);
  const viewportSize = getViewportSize();

  switch (deviceType) {
    case 'mobile':
      // On mobile, use most of the screen
      return constrainSize({
        width: Math.min(baseSize.width, viewportSize.width * 0.95),
        height: Math.min(baseSize.height, viewportSize.height * 0.85),
      });

    case 'tablet':
      // On tablet, use a bit less space
      return constrainSize({
        width: Math.min(baseSize.width, viewportSize.width * 0.8),
        height: Math.min(baseSize.height, viewportSize.height * 0.75),
      });

    case 'desktop':
    default:
      // On desktop, use the base size
      return constrainSize(baseSize);
  }
};

/**
 * Gets responsive position based on device type
 */
export const getResponsivePosition = (
  basePosition: Position,
  modalSize: Size,
  config: ResponsiveConfig = DEFAULT_RESPONSIVE_CONFIG
): Position => {
  const deviceType = getDeviceType(config);

  switch (deviceType) {
    case 'mobile':
      // On mobile, center the modal
      return getCenteredPosition(modalSize);

    case 'tablet': {
      // On tablet, use centered or constrained position
      const centeredPos = getCenteredPosition(modalSize);
      return constrainPosition(centeredPos, modalSize);
    }

    case 'desktop':
    default:
      // On desktop, use the base position but constrain it
      return constrainPosition(basePosition, modalSize);
  }
};

/**
 * Finds the closest predefined size to a given size
 */
export const findClosestSize = (
  currentSize: Size,
  availableSizes: ArtifactViewerSize[]
): ArtifactViewerSize => {
  if (availableSizes.length === 0) {
    return { width: currentSize.width, height: currentSize.height, label: 'Custom' };
  }

  let closestSize = availableSizes[0];
  let minDistance = Infinity;

  for (const size of availableSizes) {
    const distance = Math.sqrt(
      Math.pow(size.width - currentSize.width, 2) +
      Math.pow(size.height - currentSize.height, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      closestSize = size;
    }
  }

  return closestSize;
};

/**
 * Checks if two sizes are equal
 */
export const sizesEqual = (size1: Size, size2: Size): boolean => {
  return size1.width === size2.width && size1.height === size2.height;
};

/**
 * Checks if two positions are equal
 */
export const positionsEqual = (pos1: Position, pos2: Position): boolean => {
  return pos1.x === pos2.x && pos1.y === pos2.y;
};

/**
 * Generates a unique ID for artifact viewers
 */
export const generateViewerId = (prefix: string = 'artifact-viewer'): string => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Calculates the next available z-index
 */
export const getNextZIndex = (currentMaxZIndex: number): number => {
  return currentMaxZIndex + 1;
};

/**
 * Debounces a function call
 */
export const debounce = <T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttles a function call
 */
export const throttle = <T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Gets the fullscreen size for a modal
 */
export const getFullscreenSize = (
  viewportSize: Size = getViewportSize(),
  padding: number = 0
): Size => ({
  width: viewportSize.width - padding * 2,
  height: viewportSize.height - padding * 2,
});

/**
 * Gets the fullscreen position for a modal
 */
export const getFullscreenPosition = (padding: number = 0): Position => ({
  x: padding,
  y: padding,
});

/**
 * Checks if a modal is effectively in fullscreen mode
 */
export const isEffectivelyFullscreen = (
  position: Position,
  size: Size,
  viewportSize: Size = getViewportSize(),
  tolerance: number = 10
): boolean => {
  return (
    Math.abs(position.x) <= tolerance &&
    Math.abs(position.y) <= tolerance &&
    Math.abs(size.width - viewportSize.width) <= tolerance &&
    Math.abs(size.height - viewportSize.height) <= tolerance
  );
};
