using System.Text.Json;
using System.Text.Json.Serialization;
using AssistantChatApp.Server.AspNetCore.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace AssistantChatApp.Server.AspNetCore.Data;

public class RealDbSeeder
{
    public static async Task SeedAsync(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var services = scope.ServiceProvider;
        var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();
        var context = services.GetRequiredService<ApplicationDbContext>();
        var userManager = services.GetRequiredService<UserManager<User>>();
        var env = services.GetRequiredService<IHostEnvironment>();

        try
        {
            // Apply migrations if they are not applied
            await context.Database.MigrateAsync();

            // Seed users if none exist
            if (!await userManager.Users.AnyAsync())
            {
                logger.LogInformation("Seeding users...");
                await SeedHumanUsersAsync(userManager, logger, env);
            }

            // Seed AI assistants if none exist
            if (!await context.Users.AnyAsync(u => u.IsAI))
            {
                logger.LogInformation("Seeding AI assistants...");
                await SeedAIAssistantsAsync(userManager, logger, env);
            }

            // Seed system message tags
            if (!await context.Tags.AnyAsync())
            {
                logger.LogInformation("Seeding system message tags...");
                await SeedSystemMessageTagsAsync(context);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while seeding the database.");
            throw;
        }
    }

    private static async Task SeedSystemMessageTagsAsync(
        ApplicationDbContext context
    )
    {
        var tags =
            Services.MessageTagSets.SystemTags
                .Select(t => new Tag { Id = t.Id, Name = t.Name });

        await context.Tags.AddRangeAsync(tags);
        await context.SaveChangesAsync();
    }

    private static async Task SeedUsersAsync(
        UserManager<User> userManager,
        ILogger logger,
        IHostEnvironment env,
        IEnumerable<User> users,
        string defaultPassword
    )
    {
        using var userPasswordsJsonStream =
            env.ContentRootFileProvider
                .GetFileInfo("Data/DbSeed/userPasswords.json")
                .CreateReadStream();

        var userPasswords =
            JsonSerializer.Deserialize<Dictionary<string, string>>(userPasswordsJsonStream);

        foreach (var user in users)
        {
            if (await userManager.FindByEmailAsync(user.Email!) == null)
            {
                if (user.UserName != null)
                {
                    var password = userPasswords?[user.UserName] ?? defaultPassword;
                    await userManager.CreateAsync(user, password);
                }
                else
                {
                    logger.LogWarning("Seeded user {userId} does not have a UserName!", user.Id);
                }
            }
        }
    }

    private static async Task SeedHumanUsersAsync(
        UserManager<User> userManager,
        ILogger logger,
        IHostEnvironment env
    )
    {
        const string defaultPassword = "Password123!";
        var users = new List<User>
        {
            new User
            {
                UserName = "Artemy",
                Email = "<EMAIL>",
                DisplayName = "Artemy",
                EmailConfirmed = true,
                CreatedAt = DateTime.UtcNow
            },
            new User
            {
                UserName = "Valery",
                Email = "<EMAIL>",
                DisplayName = "Valery",
                EmailConfirmed = true,
                CreatedAt = DateTime.UtcNow
            },
            new User {
                UserName = "Tatiana",
                Email = "<EMAIL>",
                DisplayName = "Tatiana",
                EmailConfirmed = true,
                CreatedAt = DateTime.UtcNow
            }
        };

        await SeedUsersAsync(userManager, logger, env, users, defaultPassword);
    }

    private static async Task SeedAIAssistantsAsync(
        UserManager<User> userManager,
        ILogger logger,
        IHostEnvironment env
    )
    {
        const string defaultPassword = "AIPassword123!";
        var aiAssistants = new List<User>
        {
            new User
            {
                Id = "37913D2A-EE8E-40AB-BE52-7EFAC5FEDE62",
                UserName = "Assistant1",
                Email = "<EMAIL>",
                DisplayName = "AI Assistant #1",
                EmailConfirmed = true,
                IsAI = true,
                CreatedAt = DateTime.UtcNow,
                Avatar = "https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg"
            },
            new User
            {
                Id = "23DE7CAC-4DC1-4204-BE67-9346B72655D0",
                UserName = "Assistant2",
                Email = "<EMAIL>",
                DisplayName = "AI Assistant #2",
                EmailConfirmed = true,
                IsAI = true,
                CreatedAt = DateTime.UtcNow,
                Avatar = "https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg"
            }
        };

        await SeedUsersAsync(userManager, logger, env, aiAssistants, defaultPassword);
    }
}
