The mechanism name is "Component Registry".   The mechanism allows "registering" any component that follows a certain interface (any component can be adapted to the interface by implementing a simple wrapper for the component). I've named the mechanism "Component Registry". The key functions of such component handling are:
1. Passing props from the unified wrapper to a registered component
2. Handling actual component's feedback (e.g. event handlers)
So, the Component Registry serves as intermediary between the app and the registered components.

## Use case

Manage components dynamically embedded into a content produced by parsing Markdown text. The components replace certain AST nodes based on some defined syntactic rules. The actual replacement is handled by the `ReactMarkdown` component based on the component map obtained from the Component Registry. So, for a component to be embedded by the `ReactMarkdown`, firstly it needs to be registered in the Component Registry.
