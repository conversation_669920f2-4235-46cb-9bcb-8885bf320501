# Assistant Chat App - Backend

This is the backend server for the Assistant Chat Application, built with Node.js, Express, PostgreSQL, and Drizzle ORM.

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express
- **Database**: PostgreSQL
- **ORM**: Drizzle ORM
- **Authentication**: JWT
- **Testing**: Jest
- **Package Manager**: pnpm

## Prerequisites

- Node.js (v16+)
- pnpm
- PostgreSQL

## Getting Started

### 1. Clone the repository

```bash
git clone <repository-url>
cd AssistantChatApp.Server
```

### 2. Install dependencies

```bash
pnpm install
```

### 3. Set up environment variables

Copy the example environment file and update it with your configuration:

```bash
cp .env.example .env
```

Update the `.env` file with your PostgreSQL connection string and other settings.

### 4. Set up the database

Create a PostgreSQL database:

```bash
createdb assistant_chat_app
```

Run migrations to create the database schema:

```bash
pnpm db:migrate
```

### 5. Seed the database (optional)

Populate the database with sample data:

```bash
pnpm db:seed
```

### 6. Start the development server

```bash
pnpm dev
```

The server will start on http://localhost:5000 by default.

## API Documentation

### Authentication

#### POST /api/auth/login

Login with email and password.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "token": "jwt-token",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "displayName": "User Name",
    "avatar": "avatar-url",
    "createdAt": "2023-01-01T00:00:00Z",
    "isAI": false
  }
}
```

#### GET /api/auth/me

Get the current authenticated user.

**Headers:**
```
Authorization: Bearer jwt-token
```

**Response:**
```json
{
  "id": "user-id",
  "email": "<EMAIL>",
  "displayName": "User Name",
  "avatar": "avatar-url",
  "createdAt": "2023-01-01T00:00:00Z",
  "isAI": false
}
```

### Users

#### GET /api/users

Get users with pagination and search.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `search` (optional): Search term

**Response:**
```json
{
  "data": [
    {
      "id": "user-id",
      "email": "<EMAIL>",
      "displayName": "User Name",
      "avatar": "avatar-url",
      "createdAt": "2023-01-01T00:00:00Z",
      "isAI": false
    }
  ],
  "total": 100,
  "page": 1,
  "limit": 20
}
```

#### GET /api/users/:userId

Get a user by ID.

**Response:**
```json
{
  "id": "user-id",
  "email": "<EMAIL>",
  "displayName": "User Name",
  "avatar": "avatar-url",
  "createdAt": "2023-01-01T00:00:00Z",
  "isAI": false
}
```

#### GET /api/users/ai-assistants

Get AI assistants.

**Response:**
```json
[
  {
    "id": "ai-id",
    "email": "<EMAIL>",
    "displayName": "AI Assistant",
    "avatar": "avatar-url",
    "createdAt": "2023-01-01T00:00:00Z",
    "isAI": true
  }
]
```

### Chats

#### GET /api/chats

Get chats with pagination and filters.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `search` (optional): Search term
- `tags[]` (optional): Filter by tags
- `participants[]` (optional): Filter by participants
- `startDate` (optional): Filter by start date
- `endDate` (optional): Filter by end date

**Response:**
```json
{
  "data": [
    {
      "id": "chat-id",
      "title": "Chat Title",
      "description": "Chat Description",
      "participants": [
        {
          "id": "user-id",
          "email": "<EMAIL>",
          "displayName": "User Name",
          "avatar": "avatar-url",
          "createdAt": "2023-01-01T00:00:00Z",
          "isAI": false
        }
      ],
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z",
      "tags": ["general", "team"],
      "lastMessage": {
        "id": "message-id",
        "chatId": "chat-id",
        "author": {
          "id": "user-id",
          "email": "<EMAIL>",
          "displayName": "User Name",
          "avatar": "avatar-url",
          "createdAt": "2023-01-01T00:00:00Z",
          "isAI": false
        },
        "content": "Message content",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z",
        "mentions": ["user-id"]
      }
    }
  ],
  "total": 10,
  "page": 1,
  "limit": 20
}
```

#### POST /api/chats

Create a new chat.

**Request:**
```json
{
  "title": "Chat Title",
  "description": "Chat Description",
  "participants": ["user-id-1", "user-id-2"],
  "tags": ["general", "team"]
}
```

**Response:**
```json
{
  "id": "chat-id",
  "title": "Chat Title",
  "description": "Chat Description",
  "participants": [
    {
      "id": "user-id",
      "email": "<EMAIL>",
      "displayName": "User Name",
      "avatar": "avatar-url",
      "createdAt": "2023-01-01T00:00:00Z",
      "isAI": false
    }
  ],
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z",
  "tags": ["general", "team"]
}
```

#### GET /api/chats/:chatId

Get a chat by ID.

**Response:**
```json
{
  "id": "chat-id",
  "title": "Chat Title",
  "description": "Chat Description",
  "participants": [
    {
      "id": "user-id",
      "email": "<EMAIL>",
      "displayName": "User Name",
      "avatar": "avatar-url",
      "createdAt": "2023-01-01T00:00:00Z",
      "isAI": false
    }
  ],
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z",
  "tags": ["general", "team"],
  "lastMessage": {
    "id": "message-id",
    "chatId": "chat-id",
    "author": {
      "id": "user-id",
      "email": "<EMAIL>",
      "displayName": "User Name",
      "avatar": "avatar-url",
      "createdAt": "2023-01-01T00:00:00Z",
      "isAI": false
    },
    "content": "Message content",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z",
    "mentions": ["user-id"]
  }
}
```

#### PATCH /api/chats/:chatId

Update a chat.

**Request:**
```json
{
  "title": "Updated Chat Title",
  "description": "Updated Chat Description",
  "participants": ["user-id-1", "user-id-2"],
  "tags": ["general", "team"]
}
```

**Response:**
```json
{
  "id": "chat-id",
  "title": "Updated Chat Title",
  "description": "Updated Chat Description",
  "participants": [
    {
      "id": "user-id",
      "email": "<EMAIL>",
      "displayName": "User Name",
      "avatar": "avatar-url",
      "createdAt": "2023-01-01T00:00:00Z",
      "isAI": false
    }
  ],
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z",
  "tags": ["general", "team"]
}
```

#### DELETE /api/chats/:chatId

Delete a chat.

**Response:**
```
204 No Content
```

### Messages

#### GET /api/chats/:chatId/messages

Get messages for a chat.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 50)

**Response:**
```json
{
  "data": [
    {
      "id": "message-id",
      "chatId": "chat-id",
      "author": {
        "id": "user-id",
        "email": "<EMAIL>",
        "displayName": "User Name",
        "avatar": "avatar-url",
        "createdAt": "2023-01-01T00:00:00Z",
        "isAI": false
      },
      "content": "Message content",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z",
      "mentions": ["user-id"]
    }
  ],
  "total": 100,
  "page": 1,
  "limit": 50
}
```

#### POST /api/chats/:chatId/messages

Send a message to a chat.

**Request:**
```json
{
  "content": "Message content",
  "mentions": ["user-id"]
}
```

**Response:**
```json
{
  "id": "message-id",
  "chatId": "chat-id",
  "author": {
    "id": "user-id",
    "email": "<EMAIL>",
    "displayName": "User Name",
    "avatar": "avatar-url",
    "createdAt": "2023-01-01T00:00:00Z",
    "isAI": false
  },
  "content": "Message content",
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z",
  "mentions": ["user-id"]
}
```

#### PATCH /api/chats/:chatId/messages/:messageId

Update a message.

**Request:**
```json
{
  "content": "Updated message content",
  "mentions": ["user-id"]
}
```

**Response:**
```json
{
  "id": "message-id",
  "chatId": "chat-id",
  "author": {
    "id": "user-id",
    "email": "<EMAIL>",
    "displayName": "User Name",
    "avatar": "avatar-url",
    "createdAt": "2023-01-01T00:00:00Z",
    "isAI": false
  },
  "content": "Updated message content",
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z",
  "mentions": ["user-id"]
}
```

#### DELETE /api/chats/:chatId/messages/:messageId

Delete a message.

**Response:**
```
204 No Content
```

## Running Tests

```bash
pnpm test
```

## License

This project is licensed under the MIT License.
