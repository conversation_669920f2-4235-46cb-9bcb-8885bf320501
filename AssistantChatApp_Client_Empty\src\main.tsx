import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { RouterProvider, createRouter, NotFoundRoute, } from "@tanstack/react-router";
import { MuiRoot } from "./components/muiRoot";
import { Route as rootRoute } from "./routes/__root";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import '@fontsource/roboto/300.css';
import '@fontsource/roboto/400.css';
import '@fontsource/roboto/500.css';
import '@fontsource/roboto/700.css';

// Import the generated route tree
import { routeTree } from "./routeTree.gen";

const queryClient = new QueryClient();

const notFoundRoute = new NotFoundRoute({
    getParentRoute: () => rootRoute,
    component: () => "404 Not found"
});

// Create a new router instance
const router = createRouter({
    routeTree, notFoundRoute,
    context: { queryClient },
    // Since we're using React Query, we don't want loader calls to ever be stale
    // This will ensure that the loader is always called when the route is preloaded or visited
    // defaultPreloadStaleTime: 0
});

// Register the router instance for type safety
declare module "@tanstack/react-router" {
    interface Register {
        router: typeof router;
    }
}

// Render the app
const rootElement = document.getElementById("root")!;
if (!rootElement.innerHTML) {
    const root = ReactDOM.createRoot(rootElement);
    root.render(
        <StrictMode>
            <MuiRoot>
                <QueryClientProvider client={queryClient}>
                    <RouterProvider router={router} />
                </QueryClientProvider>
            </MuiRoot>
        </StrictMode>,
    );
}
