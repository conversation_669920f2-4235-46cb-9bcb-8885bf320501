#r "nuget: FSharp.Data.LiteralProviders, 1.0.3"
#r "nuget: SQLProvider, 1.5.9"
#r "nuget: SQLProvider.PostgreSql, 1.5.9"

open System
open System.Linq
open FSharp.Data.Sql
open FSharp.Data.Sql.PostgreSql
open FSharp.Data.LiteralProviders

module DbConfig =

    let [<Literal>] DbConnectionStringSample = TextFile<"DbConnectionString.txt">.Text
    let [<Literal>] DbVendor = Common.DatabaseProviderTypes.POSTGRESQL
    let [<Literal>] UseOptionTypes = Common.NullableColumnType.OPTION


type ChatAppDb = SqlDataProvider<
        DbConfig.DbVendor,
        DbConfig.DbConnectionStringSample,
        UseOptionTypes = DbConfig.UseOptionTypes
    >

let ctx = ChatAppDb.GetDataContext()

let allMessages: IQueryable<string> = query {
    for m in ctx.Public.Messages do
    select m.Content
}

allMessages.ToArray()

let allUsers = query {
    for u in ctx.Public.AspNetUsers do
        select u
}

allUsers.ToArray()
