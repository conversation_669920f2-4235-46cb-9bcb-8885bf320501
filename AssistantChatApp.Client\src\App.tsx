import CssBaseline from "@mui/material/CssBaseline";
import { QueryClient } from "@tanstack/react-query";
import { Router, RouterProvider } from "@tanstack/react-router";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import theme from "./theme";
import { routeTree } from "./routes";
import { AuthProvider } from "./contexts/AuthContext";
import { MDXProvider } from "./components/mdx/MDXProvider";
import { ThemeModeProvider } from "./contexts/ThemeModeContext";
import { QueryClientAtomProvider } from "jotai-tanstack-query/react";
import { DevTools } from "jotai-devtools";
import "jotai-devtools/styles.css";

// Create a query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
});

// Create a router instance
const router = new Router({ routeTree });

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

function App() {
  return (
    <ThemeModeProvider baseTheme={theme}>
      <CssBaseline />
      <QueryClientAtomProvider client={queryClient}>
        <DevTools position="bottom-right" theme="dark" />
        <AuthProvider router={router}>
          <MDXProvider>
            <RouterProvider router={router} />
          </MDXProvider>
        </AuthProvider>
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientAtomProvider>
    </ThemeModeProvider>
  );
}

export default App;
