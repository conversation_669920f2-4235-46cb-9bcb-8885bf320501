import CssBaseline from '@mui/material/CssBaseline';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Router, RouterProvider } from '@tanstack/react-router';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import theme from './theme';
import { routeTree } from './routes';
import { AuthProvider } from './contexts/AuthContext';
import { MDXProvider } from "./components/mdx/MDXProvider";
import { ThemeModeProvider } from './contexts/ThemeModeContext';

// Create a query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
});

// Create a router instance
const router = new Router({ routeTree });

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}

function App() {
  return (
    <ThemeModeProvider baseTheme={theme}>
      <CssBaseline />
      <QueryClientProvider client={queryClient}>
        <AuthProvider router={router}>
          <MDXProvider>
            <RouterProvider router={router} />
          </MDXProvider>
        </AuthProvider>
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </ThemeModeProvider>
  );
}

export default App;
