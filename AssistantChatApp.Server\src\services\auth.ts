import { db } from '../db';
import { users } from '../db/schema';
import { comparePassword } from '../auth/password';
import { generateToken } from '../auth/jwt';
import { LoginRequest, LoginResponse, SafeUser } from '../types';
import { UnauthorizedError, NotFoundError } from '../utils/errors';
import { eq } from 'drizzle-orm';

/**
 * Login a user
 * @param loginData Login credentials
 * @returns Login response with token and user data
 */
export async function login(loginData: LoginRequest): Promise<LoginResponse> {
  const { email, password } = loginData;

  // Find user by email
  const userResult = await db.select().from(users).where(eq(users.email, email)).limit(1);
  
  if (!userResult.length) {
    throw new UnauthorizedError('Invalid email or password');
  }

  const user = userResult[0];

  // Verify password
  const isPasswordValid = await comparePassword(password, user.password);
  
  if (!isPasswordValid) {
    throw new UnauthorizedError('Invalid email or password');
  }

  // Create safe user object (without password)
  const safeUser: SafeUser = {
    id: user.id,
    email: user.email,
    displayName: user.displayName,
    avatar: user.avatar,
    createdAt: user.createdAt,
    isAI: user.isAI,
  };

  // Generate JWT token
  const token = generateToken(safeUser);

  return {
    token,
    user: safeUser,
  };
}

/**
 * Get user by ID
 * @param userId User ID
 * @returns User data
 */
export async function getUserById(userId: string): Promise<SafeUser> {
  const userResult = await db.select().from(users).where(eq(users.id, userId)).limit(1);
  
  if (!userResult.length) {
    throw new NotFoundError('User not found');
  }

  const user = userResult[0];

  return {
    id: user.id,
    email: user.email,
    displayName: user.displayName,
    avatar: user.avatar,
    createdAt: user.createdAt,
    isAI: user.isAI,
  };
}
