{"name": "assistant-chat-app-server", "version": "1.0.0", "description": "Backend for Assistant Cha<PERSON> Application", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "lint": "eslint . --ext .ts", "test": "jest", "test:watch": "jest --watch", "db:migrate": "drizzle-kit migrate", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push:pg", "db:seed": "ts-node src/db/seed.ts"}, "keywords": ["chat", "assistant", "api"], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "drizzle-orm": "~0.42", "express": "^4.18.2", "express-async-errors": "^3.1.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.11.3", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.10", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.0", "@types/pg": "^8.11.14", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "drizzle-kit": "^0.31.0", "eslint": "^9.25.1", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.2"}}