using System;
using System.Threading.Tasks;
using AssistantChatApp.DataStore.Infrastructure;
using AssistantChatApp.DataStore.Models;
using AssistantChatApp.Server.Services;
using AssistantChatApp.Server.AspNetCore.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Moq;
using Xunit;

namespace AssistantChatApp.Server.AspNetCore.Tests
{
    public class UserServiceTests
    {
        private readonly Mock<UserManager<User>> _mockUserManager;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly DbContextOptions<ApplicationDbContext> _dbContextOptions;

        public UserServiceTests()
        {
            // Setup UserManager mock
            var userStoreMock = new Mock<IUserStore<User>>();
            _mockUserManager = new Mock<UserManager<User>>(
                userStoreMock.Object, null, null, null, null, null, null, null, null);

            // Setup Configuration mock
            _mockConfiguration = new Mock<IConfiguration>();
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(x => x["Secret"]).Returns("YourSuperSecretKeyHereMakeItLongEnoughForSecurity");
            configurationSection.Setup(x => x["Issuer"]).Returns("AssistantChatApp");
            configurationSection.Setup(x => x["Audience"]).Returns("AssistantChatAppClient");
            _mockConfiguration.Setup(x => x.GetSection("JwtSettings")).Returns(configurationSection.Object);

            // Setup in-memory database
            _dbContextOptions = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
        }

        [Fact]
        public async Task GetUserById_ReturnsUser_WhenUserExists()
        {
            // Arrange
            var userId = "user123";
            var user = new User
            {
                Id = userId,
                DisplayName = "Test User",
                Email = "<EMAIL>",
                CreatedAt = DateTime.UtcNow,
                IsAI = false
            };

            _mockUserManager.Setup(x => x.FindByIdAsync(userId))
                .ReturnsAsync(user);

            using var context = new ApplicationDbContext(_dbContextOptions);
            var jwtService = new JwtService(_mockConfiguration.Object);
            var userService = new UserService(_mockUserManager.Object, context, jwtService);

            // Act
            var result = await userService.GetUserByIdAsync(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(userId, result.Id);
            Assert.Equal("Test User", result.DisplayName);
            Assert.Equal(user.CreatedAt.ToString("o"), result.CreatedAt);
            Assert.False(result.IsAI);
        }

        [Fact]
        public async Task GetUserById_ReturnsNull_WhenUserDoesNotExist()
        {
            // Arrange
            var userId = "nonexistentuser";

            _mockUserManager.Setup(x => x.FindByIdAsync(userId))
                .ReturnsAsync((User)null);

            using var context = new ApplicationDbContext(_dbContextOptions);
            var jwtService = new JwtService(_mockConfiguration.Object);
            var userService = new UserService(_mockUserManager.Object, context, jwtService);

            // Act
            var result = await userService.GetUserByIdAsync(userId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetAIAssistants_ReturnsOnlyAIUsers()
        {
            // Arrange
            var users = new[]
            {
                new User { Id = "user1", DisplayName = "Regular User", IsAI = false },
                new User { Id = "ai1", DisplayName = "AI Assistant 1", IsAI = true },
                new User { Id = "ai2", DisplayName = "AI Assistant 2", IsAI = true }
            };

            _mockUserManager.Setup(x => x.Users)
                .Returns(users.AsQueryable());

            using var context = new ApplicationDbContext(_dbContextOptions);
            var jwtService = new JwtService(_mockConfiguration.Object);
            var userService = new UserService(_mockUserManager.Object, context, jwtService);

            // Act
            var result = await userService.GetAIAssistantsAsync();

            // Assert
            Assert.Equal(2, result.Count);
            Assert.All(result, user => Assert.True(user.IsAI));
            Assert.Contains(result, user => user.Id == "ai1");
            Assert.Contains(result, user => user.Id == "ai2");
        }
    }
}
