import React, { useState } from 'react';
import { Container, Box, Typography, TextField, Button, Paper, Avatar, Alert, Stack } from '@mui/material';
import { Lock as LockIcon } from '@mui/icons-material';
import { useForm } from '@tanstack/react-form';
import { useNavigate } from '@tanstack/react-router';
import { useAuth } from '../contexts/AuthContext';
// import { zodValidator } from '@tanstack/zod-form-adapter';
import { z } from 'zod';
import { useTranslation } from 'react-i18next';

// Define validation schema
const LoginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

const LoginPage: React.FC = () => {
  const { t } = useTranslation("auth");
  const { login, isLoading } = useAuth();
  const navigate = useNavigate();
  const [loginError, setLoginError] = useState<string | null>(null);

  const form = useForm({
    defaultValues: {
      email: '',
      password: '',
    },
    onSubmit: async ({ value }) => {
      try {
        setLoginError(null);
        await login(value.email, value.password);
        navigate({ to: '/' });
      } catch (error) {
        console.error("Login failed:", error);
        setLoginError(t("loginError"));
      }
    },
    // validatorAdapter: zodValidator,
    validators: {
      onSubmit: LoginSchema,
    }
  });

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper
          elevation={3}
          sx={{
            padding: 4,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%',
            borderRadius: 2,
          }}
        >
          <Avatar sx={{ m: 1, bgcolor: 'primary.main' }}>
            <LockIcon />
          </Avatar>
          <Typography component="h1" variant="h5" sx={{ mb: 3 }}>
            {t("signIn")}
          </Typography>

          {loginError && (
            <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
              {loginError}
            </Alert>
          )}
          <Stack
            component="form"
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              form.handleSubmit();
            }}
            sx={{ width: '100%' }}
          >
            <form.Field
              name="email"
              validators={{
                onSubmit: ({ value }) => LoginSchema.shape.email.safeParse(value).success ? undefined : t("emailError"),
              }}
            >
              {(field) => (
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="email"
                  label={t("email")}
                  name="email"
                  autoComplete="email"
                  autoFocus
                  value={field.state.value}
                  onChange={(e) => field.handleChange(e.target.value)}
                  error={!!field.state.meta.errors?.length}
                  helperText={field.state.meta.errors.join(', ')}
                />
              )}
            </form.Field>

            <form.Field
              name="password"
              validators={{
                onSubmit: ({ value }) => LoginSchema.shape.password.safeParse(value).success ? undefined : t("passwordLengthError"),
              }}
            >
              {(field) => (
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label={t("password")}
                  type="password"
                  id="password"
                  autoComplete="current-password"
                  value={field.state.value}
                  onChange={(e) => field.handleChange(e.target.value)}
                  error={!!field.state.meta.errors?.length}
                  helperText={field.state.meta.errors?.join(', ')}
                />
              )}
            </form.Field>

            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isLoading}
              sx={{ mt: 3, mb: 2, py: 1.5 }}
            >
              {isLoading ? t("signingIn") : t("signIn")}
            </Button>
          </Stack>
        </Paper>
      </Box>
    </Container>
  );
};

export default LoginPage;
