import { db } from '../db';
import { messages, mentions, users, chatParticipants, chats } from '../db/schema';
import { Message, PaginatedResponse, SafeUser } from '../types';
import { NotFoundError, ForbiddenError } from '../utils/errors';
import { createPaginatedResponse, calculateOffset } from '../utils/pagination';
import { eq, and, desc, sql } from 'drizzle-orm';

/**
 * Get messages for a chat with pagination
 * @param chatId Chat ID
 * @param userId Current user ID
 * @param page Page number
 * @param limit Items per page
 * @returns Paginated list of messages
 */
export async function getMessages(
  chatId: string,
  userId: string,
  page: number,
  limit: number
): Promise<PaginatedResponse<Message>> {
  // Check if user is a participant
  const participantCheck = await db
    .select()
    .from(chatParticipants)
    .where(and(
      eq(chatParticipants.chatId, chatId),
      eq(chatParticipants.userId, userId)
    ))
    .limit(1);
  
  if (!participantCheck.length) {
    throw new ForbiddenError('You are not a participant in this chat');
  }
  
  const offset = calculateOffset(page, limit);
  
  // Get total count
  const countResult = await db
    .select({ count: sql<number>`count(*)` })
    .from(messages)
    .where(eq(messages.chatId, chatId))
    .execute();
  
  const total = countResult[0]?.count || 0;
  
  // Get messages with authors
  const messagesResult = await db
    .select({
      message: messages,
      author: users,
      mentions: sql<string[]>`array_agg(${mentions.userId})`
    })
    .from(messages)
    .innerJoin(users, eq(messages.authorId, users.id))
    .leftJoin(mentions, eq(messages.id, mentions.messageId))
    .where(eq(messages.chatId, chatId))
    .groupBy(messages.id, users.id)
    .orderBy(desc(messages.createdAt))
    .limit(limit)
    .offset(offset);
  
  // Format messages
  const formattedMessages: Message[] = messagesResult.map((row) => ({
    id: row.message.id,
    chatId: row.message.chatId,
    author: {
      id: row.author.id,
      email: row.author.email,
      displayName: row.author.displayName,
      avatar: row.author.avatar,
      createdAt: row.author.createdAt,
      isAI: row.author.isAI,
    },
    content: row.message.content,
    createdAt: row.message.createdAt,
    updatedAt: row.message.updatedAt,
    mentions: row.mentions.filter(Boolean), // Remove null values
  }));
  
  return createPaginatedResponse(formattedMessages, total, page, limit);
}

/**
 * Send a message to a chat
 * @param chatId Chat ID
 * @param userId Current user ID
 * @param content Message content
 * @param mentionedUserIds User IDs mentioned in the message
 * @returns Created message
 */
export async function sendMessage(
  chatId: string,
  userId: string,
  content: string,
  mentionedUserIds: string[] = []
): Promise<Message> {
  // Check if user is a participant
  const participantCheck = await db
    .select()
    .from(chatParticipants)
    .where(and(
      eq(chatParticipants.chatId, chatId),
      eq(chatParticipants.userId, userId)
    ))
    .limit(1);
  
  if (!participantCheck.length) {
    throw new ForbiddenError('You are not a participant in this chat');
  }
  
  // Start a transaction
  return await db.transaction(async (tx) => {
    // Create message
    const [newMessage] = await tx
      .insert(messages)
      .values({
        chatId,
        authorId: userId,
        content,
      })
      .returning();
    
    // Add mentions if provided
    if (mentionedUserIds.length > 0) {
      for (const mentionedUserId of mentionedUserIds) {
        await tx
          .insert(mentions)
          .values({
            messageId: newMessage.id,
            userId: mentionedUserId,
          });
      }
    }
    
    // Update chat's updatedAt timestamp
    await tx
      .update(chats)
      .set({ updatedAt: new Date() })
      .where(eq(chats.id, chatId));
    
    // Get author
    const [author] = await tx
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);
    
    // Return formatted message
    return {
      id: newMessage.id,
      chatId: newMessage.chatId,
      author: {
        id: author.id,
        email: author.email,
        displayName: author.displayName,
        avatar: author.avatar,
        createdAt: author.createdAt,
        isAI: author.isAI,
      },
      content: newMessage.content,
      createdAt: newMessage.createdAt,
      updatedAt: newMessage.updatedAt,
      mentions: mentionedUserIds,
    };
  });
}

/**
 * Update a message
 * @param chatId Chat ID
 * @param messageId Message ID
 * @param userId Current user ID
 * @param content New message content
 * @param mentionedUserIds User IDs mentioned in the message
 * @returns Updated message
 */
export async function updateMessage(
  chatId: string,
  messageId: string,
  userId: string,
  content: string,
  mentionedUserIds: string[] = []
): Promise<Message> {
  // Check if message exists and belongs to the user
  const messageCheck = await db
    .select()
    .from(messages)
    .where(and(
      eq(messages.id, messageId),
      eq(messages.chatId, chatId)
    ))
    .limit(1);
  
  if (!messageCheck.length) {
    throw new NotFoundError('Message not found');
  }
  
  if (messageCheck[0].authorId !== userId) {
    throw new ForbiddenError('You can only edit your own messages');
  }
  
  // Start a transaction
  return await db.transaction(async (tx) => {
    // Update message
    const [updatedMessage] = await tx
      .update(messages)
      .set({
        content,
        updatedAt: new Date(),
      })
      .where(eq(messages.id, messageId))
      .returning();
    
    // Update mentions
    await tx
      .delete(mentions)
      .where(eq(mentions.messageId, messageId));
    
    if (mentionedUserIds.length > 0) {
      for (const mentionedUserId of mentionedUserIds) {
        await tx
          .insert(mentions)
          .values({
            messageId,
            userId: mentionedUserId,
          });
      }
    }
    
    // Get author
    const [author] = await tx
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);
    
    // Return formatted message
    return {
      id: updatedMessage.id,
      chatId: updatedMessage.chatId,
      author: {
        id: author.id,
        email: author.email,
        displayName: author.displayName,
        avatar: author.avatar,
        createdAt: author.createdAt,
        isAI: author.isAI,
      },
      content: updatedMessage.content,
      createdAt: updatedMessage.createdAt,
      updatedAt: updatedMessage.updatedAt,
      mentions: mentionedUserIds,
    };
  });
}

/**
 * Delete a message
 * @param chatId Chat ID
 * @param messageId Message ID
 * @param userId Current user ID
 */
export async function deleteMessage(
  chatId: string,
  messageId: string,
  userId: string
): Promise<void> {
  // Check if message exists and belongs to the user
  const messageCheck = await db
    .select()
    .from(messages)
    .where(and(
      eq(messages.id, messageId),
      eq(messages.chatId, chatId)
    ))
    .limit(1);
  
  if (!messageCheck.length) {
    throw new NotFoundError('Message not found');
  }
  
  if (messageCheck[0].authorId !== userId) {
    throw new ForbiddenError('You can only delete your own messages');
  }
  
  // Delete message (cascade will delete mentions)
  await db
    .delete(messages)
    .where(eq(messages.id, messageId));
}
